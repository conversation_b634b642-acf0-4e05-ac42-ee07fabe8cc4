// 导出请求封装方法
import http from '@/utils/request'

/**
 * 获取未入驻公司详情
 */
export const get_company_info = (data?: any) => {
  return http('get', `/merchant/company/info`, data)
}

/**
 * 编辑保存商家入驻信息
 */
export const set_company_update = (data?: any) => {
  return http('post', `/merchant/company/update`, data)
}

// 获取账户信息
export const getUserInfo = (data?: any) => {
  return http('get', `/public/user/info`, data)
}

// 获取账户权限信息
export const getUserAuth = (data?: any) => {
  return http('get', `/public/user/permission`, data)
}

/**
 * 会员来源
 */
export const setWechatConfAllList = (data) => {
  return http('get', `/merchant/wechat-conf/all-list`, data)
}

// 获取cos临时密钥
export const getCosAuth = (data?: any) => {
  return http('get', `/public/file/credential`, data)
}

/**
 * 获取用户菜单
 */
export const getRoleMenuList = (params?: any) => {
  return http('get', `/public/role/role-menu`, params)
}

/**
 * 获取店铺列表
 * https://www.apifox.cn/web/project/2014698/apis/api-63590656
 */
export const getShopListApi = (data?: any) => {
  return http('get', `/merchant/shop/list`, { page: 1, page_size: 10000, ...data })
}
// 获取登录后店铺列表
export const getLoginShopListApi = (data?: any) => {
  return http('get', `/merchant/user/login-shop`, { page: 1, page_size: 10000, ...data })
}
/**
 * 获取店铺详情
 */
export const get_shop_info = (data: any) => {
  return http('get', `/merchant/shop/info`, data)
}
// 获取素材库数据
export const getFileList = (data?: any) => {
  return http('get', `/public/file/list`, data)
}
/**
 * 编辑素材库文件名称
 */
export const updateNameFile = (data?: any) => {
  return http('post', `/public/file/update-name`, data)
}
/**
 * 删除素材库文件
 */
export const deleteFile = (data?: any) => {
  return http('post', `/public/file/delete`, data)
}

// 上传素材库图片
export const fileSave = (data?: any) => {
  return http('post', `/public/file/save`, data)
}
// 素材库文件/修改文件或分组名称
export const fileEdit = (data?: any) => {
  return http('post', `/public/file/edit`, data)
}

/**
 * 重置token
 * @param {String} shop_id 店铺id
 * @returns
 */
export const cutShopUpdateTokenApi = (shop_id: string) => {
  return http('post', `/public/token/reset`, {
    shop_id
  })
}

/**
 * 导出
 * @param {String} type :  order-> 订单导出  params： 搜索条件 转为json字符串
 * @returns
 */
export const exportCreate = (data?: any) => {
  return http('post', `/public/export-data/create`, data)
}

/**
 * 客服信息
 */
export const kefuInfo = (data?: any) => {
  return http('get', `/public/kefu/info`, data)
}

/**
 * 消息已读
 */
export const msgUpdateApi = (data?: any) => {
  return http('post', `/public/msg/update`, data)
}

/**
 * 投诉查询
 */
export const pendingCount = () => {
  return http('get', `/merchant/merchant-complaints/pending-count`)
}
/**
 * 版本更新查询
 */
export const get_version_guide_last = () => {
  return http('get', `/merchant/version-guide/last`)
}
/**
 * 变更版本状态
 */
export const update_version_guide_record = (data: any) => {
  return http('post', `/merchant/version-guide/record`, data)
}
/**
 * 获取服务数据页面
 */
export const getServiceStatData = () => {
  return http('get', `/merchant/shop/service-stat-data`)
}

/**
 * 负责人数据
 */
export const getUserListApi = () => {
  return http('get', `/public/user/user-list`)
}

export const get_area_list = () => {
  return http('get', `/merchant/area/list`)
}
/**
 * erp列表
 */
export const authList = (data) => {
  return http('post', `/shop-erp/auth-list`, data)
}

// 账号/发送验证码
export const set_Captcha = (data: any) => {
  return http('post', `/public/sms/code`, data)
}

export const change_phone = (data) => {
  return http('get', `/merchant/order/get-phone`, data)
}

/**
 * 获取广点通授权链接
 */
export const setAuthUrl = (data) => {
  return http('get', `/merchant/ad/generate-auth-url`, data)
}

/**
 * 获取报表商品搜索列表
 */
export const get_search_goods_list = (data: any) => {
  return http('get', `/merchant/goods-log/search-list`, data)
}

/**
 * 获取报表账户搜索列表
 */
export const get_search_advertiser_list = (data: any) => {
  return http('get', `/merchant/advertiserLog/list`, data)
}

// 保存和编辑标题排序
export const save_report_column = (data: any) => {
  return http('post', `/merchant/user-report-sort/add`, data)
}
// 获取标题排序列表
export const get_report_column_list = (data) => {
  return http('get', `/merchant/user-report-sort/list`, data)
}

/**
 * ocr识别
 * https://www.apifox.cn/link/project/2014698/apis/api-66580697
 */
export const setOcrInfo = (data: any) => {
  return http('get', `/public/ocr/info`, data)
}

//运费险充值提示
export const checkMoneyApi = (data: any) => {
  return http('get', `/merchant/shop-premium/get-shop-premium-money`, data)
}

// 查询商品库列表
export const get_product_library_list = (data: any) => {
  return http('get', `/merchant/productLibrary/list`, data)
}

// 获取全球国家信息
export const get_origin_list = (data?: any) => {
  return http('get', `/public/origin-list`, data)
}
// 批量发送短信
export const batch_send_msg = (data: any) => {
  return http('post', `/merchant/shop-sms-temp/batch-send`, data)
}
/**
 * 召回统计
 */
export const getSmsList = (data?: any) => {
  return http('get', `/public/sms/sms-count`, data)
}
/**
 * 是否打开保障金提醒
 */
export const security_isOpen = (data?: any) => {
  return http('get', `/merchant/shop-security/security-open`, data)
}
