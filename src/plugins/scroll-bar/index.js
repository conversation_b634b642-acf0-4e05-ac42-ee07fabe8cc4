import { nextTick } from "vue"
import ScrollBar from "./scroll-bar"

export const scrollBar = {
  mounted(el, binding) {
    binding.value = binding.value || {}

    el.style.overflow = "hidden"

    const options = binding.value

    if (typeof binding.value.onScrollEnd !== "function") {
      options.onScrollEnd = () => {}
    }
    // 需要多一个div
    // 实例：
    /**
     * <div v-scroll-bar>
     *    <div>
     *      <div v-for="item in 20"></div>
     *    </div>
     * </div>
     * 
     */
    el.myScroll = new ScrollBar(el, options)

    nextTick(() => el.myScroll.refresh())
  },
  updated(el) {
    nextTick(() => el.myScroll.refresh())
  },
  unmounted(el) {
    el.myScroll.destroy()
    el.myScroll = null
    delete el.myScroll
  }
}

export default {
  scrollBar
}
