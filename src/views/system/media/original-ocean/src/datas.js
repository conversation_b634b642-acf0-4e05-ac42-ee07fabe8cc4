import { reactive, ref,computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {  message,Modal } from 'ant-design-vue'
import {  getOrglist, setAuthUrl, adCallback, delAccount, getAuthCheck,delBatchAccount,manual_batch_ad_sync } from '../index.api.js'
export default function datas() {
  const route = useRoute()
const router = useRouter()
const account_role_type = ref('ACCOUNT_ROLE_TYPE_ADVERTISER')
const searchFormDataRef = ref()
  const searchList = reactive([
    
    {
      type: 'input.text',
      field: 'token_account_id',
      value: undefined,
      props: {
        placeholder: '请输入账户编号'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'input.text',
      field: 'account_name',
      value: undefined,
      props: {
        placeholder: '请输入账户名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'select',
      field: 'empower_status',
      value: undefined,
      props: {
        placeholder: '请选择授权状态',
        options: [
          {
            value: 1,
            label: '授权成功'
          },
          {
            value: -1,
            label: '授权失效'
          },
         
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    
    // {
    //   type: 'admin',
    //   field: 'admin_id',
    //   value: [],
    //   props: {
    //     mode: 'multiple',
    //     placeholder: '请选择负责人',
    //     showArrow: true
    //   },
    //   layout: {
    //     xs: 24,
    //     sm: 12,
    //     md: 8,
    //     lg: 8,
    //     xl: 8,
    //     xxl: 6
    //   }
    // }
  ])

  const columns = reactive([
    {
      title: '账户编号',
      dataIndex: 'token_account_id',
      key: 'token_account_id',
      width: 150,
      ellipsis: true
    },
    {
      title: '账户名称',
      dataIndex: 'account_name',
      key: 'account_name',
      width: 240,
      ellipsis: true
    },
    {
      title: '组织名称',
      dataIndex: 'oceanengine_name',
      key: 'oceanengine_name',
      width: 200,
      ellipsis: true
    },
    
    {
      title: '授权状态',
      dataIndex: 'status',
      key: 'status',
      width: 140
    },
    {
      title: '负责人',
      dataIndex: 'admin_name',
      key: 'admin_name',
      width: 140
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 150
    }
  ])
  const data = reactive({
    type: 'ad',
    params: {
      page: 1,
      page_size: 10
    },
    selectedRowKeys2: [], // 表格选择的Item
    selectedRowKeys: [],
    dialog: {
      title: '',
      type: '',
      visible: false,
      width: null
    }
  })
  const actions = {
    foldNum: 0,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 6
    }
  }
  // 表格数据
  const tableData = reactive({
    bordered: false,
    scroll: {
      scrollToFirstRowOnChange: true,
      x: 1000
    },
    rowKey: 'id',
    dataSource: [],
    loading: false,
    columns: columns,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  })
  const onSelectChange = (selectedRowKeys, selectedRows) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys)
    data.selectedRowKeys = selectedRowKeys
    data.selectedRows = selectedRows
  }
  const onSelectChange2 = (selectedRowKeys, selectedRows) => {
    data.selectedRowKeys2 = selectedRowKeys
  }
  // 当前显示引导
  const rowSelectionConfig = computed(() => { 
    return  {
      selectedRowKeys:  data.selectedRowKeys ,
      onChange:  onSelectChange 
    }
  })
  // 获取d授权链接
  const getAuthUrl = async () => {
    try {
      const res = await setAuthUrl()
      window.open(res.data.url, '_blank')
    } catch (error) {
      console.error(error)
    }
  }
  // 检测授权
  const checkAuth = async (row) => {
    try {
      const res = await getAuthCheck({ id: row.id })
      if (res.code == 40102) {
        getAuthUrl()
      } else {
        getList()
      }
    } catch (error) {
      console.error(error)
    }
  }
  const changeValue = (v) => {
    console.log(v.formData, 'v.formData')
    if (v.status) {
      data.params = {
        ...data.params,
        ...v.formData,
        token_account_id: Number(v.formData.token_account_id) || undefined,
        admin_id: !v.formData.admin_id ? '' : v.formData.admin_id.join(',')
      }
      data.params.page = 1
      getList()
    } else {
      data.params = {
        page: 1,
        page_size: 10
      }
      getList()
    }
  }

    // 删除广告
    const onDel = async (row) => {
      try {
        let ids
        if (row?.id) {
          await delAccount({ id: row.id })
        } else {
          const key = data.type === 'ad' ? 'selectedRowKeys' : 'selectedRowKeys2'
          ids = data[key]
          if (!ids.length){
            return message.warning('请选择要删除的账户')
          }
          Modal.confirm({
            title: '提示',
            centered: true,
            content: '请确认是否删除选中的账户？',
            onOk: async() =>{
              await delBatchAccount({ ids: ids })
              message.success('删除成功')
              getList()
              data[key] = []
            },
            onCancel() {
              Modal.destroyAll()
            }
          })
        }
      } catch (error) {
        console.error(error)
      }
    }
       // 更新广告
       const updateData = async (row) => {
        
        try {
          let ids
          if (row?.id) {
            await manual_batch_ad_sync({ account_ids:  [Number( row.token_account_id)],media_type:6 })
          } else {
           if(data.selectedRowKeys.length == 0) {
            return message.warning('请选择要更新的广告账户')
           }
            ids = data.selectedRows.map(item=>Number(item.token_account_id))
            await manual_batch_ad_sync({ account_ids: ids ,media_type:1 })
          }
          message.success('更新成功')
          getList()
          data.selectedRowKeys =[]
        } catch (error) {
          console.error(error)
        }
      }
  
        
  function getLocalTime(nS) {
    let n = new Date(nS)
    return n.toLocaleDateString().replace(/\//g, '-') + ' ' + n.toTimeString().substr(0, 8)
  }
  // 获取广告列表
  const getList = async () => {
    try {
      let res = null
      tableData.loading = true
      const params = {
        ...data.params,
        is_cid:2,
        type: data.type == 'ad' ? 2 : 1
      }
      res = await getOrglist(params)
      tableData.dataSource = res.data?.list || []
      tableData.pagination.total = res.data?.total || 0
      tableData.pagination.current = res.data.page || 1
      tableData.loading = false
    } catch (error) {
      console.error(error)
      tableData.loading = false
    }
  }
  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }
  const changeRadio = async (e) => {
    console.log(account_role_type.value, 'account_role_type.value')
    await getList()
  }
  // 获取广告回调code
  const getadCallback = async () => {
    try {
      console.log(route, '路由参数')
      let code = ''
      let state = route.query.state

      code = route.query.auth_code

      if (!code) return getList()
      const res = await adCallback({ auth_code: code, state: state })
      await getList()
      if (res.code === 0) {
        if (!res.data || res.data?.length > 0) {
          message.warning('部分组织授权失败')
        } else {
          onShowDialog('add', {})
        }
      }
      router.replace({
        name: 'oceanEngine'
      })
    } catch (error) {
      router.replace({
        name: 'oceanEngine'
      })
    }
  }


  const onShowDialog = (type, item) => {
    const title = data.type == 'ad' ? '新增广告主账户' : '新增组织账户'
    switch (type) {
      case 'add':
        data.dialog = { title: title, type: 'add', width: 750, visible: true, item: item }
        break
    }
  }
  const onConfigDialog = (type, item) => {
    const title = data.type == 'config' ? '分配' : '分配记录'
    data.dialog = { title: title, type: type, width: type == 'config' ?  550 : 1000, visible: true, item: item === 'batch' ? {token_account_id: data.selectedRowKeys} : item }
  }

  const tabsChange = (val) => {
    data.type = val
    if (val == 'org') {
      tableData.columns = columns.filter((item) => item.key != 'oceanengine_name')
    } else {
      tableData.columns = columns
    }
    searchFormDataRef.value.changeFormValue(false)
    // getList()
  }
  const onEvent = (e) => {
    data.dialog = {
      title: '',
      type: '',
      visible: false,
      width: null
    }
    data.selectedRowKeys = []
    data.selectedRows = []
    getList()
  }
  return { searchList, columns,data,actions,tableData,getAuthUrl,checkAuth,changeValue,onDel,getLocalTime,getList,pageChange,changeRadio,getadCallback,onShowDialog ,tabsChange,onEvent,updateData,rowSelectionConfig,onConfigDialog}
}
