import http from '@/utils/request'

/**
 * 获取广告列表
 * 
 */
export const setList = (data) => {
  return http('get', `/merchant/ad/dmp_list`, data)
}

export const agnetism_list = (data) => {
  return http('get', `/merchant/ad/magnetism_list`, data)
}

/**
 * 获取授权链接
 * 
 */
export const setAuthUrl = (data) => {
  return http('get', `/merchant/ad/oceanengine-generate-auth_url`, data)
}
/**
 * 获取主体列表
 */
 export const getCompanyInfo = (data) => {
  return http('get', `/merchant/ad/get_company_info`, data)
}

/**
 * 广告授权回调
 * 
 */
export const adCallback = (data) => {
  return http('get', `/merchant/ad/oceanengine_callback`, data)
}
export const magnetism_callback = (data) => {
  return http('get', `/merchant/ad/magnetism_callback`, data)
}
/**
 * 删除账号
 * 
 */
export const delAccount = (data) => {
  return http('post', `/merchant/ad/oceanengine_delete`, data)
}
/**
 * 批量删除账号
 * 
 */
export const delBatchAccount = (data) => {
  return http('post', `/merchant/ad/oceanengine-batch-delete`, data)
}

/**
 * 获取子账号信息
 *
 */
export const getChildInfo = (data) => {
  return http('get', `/merchant/ad/get_oceanengine_children`, data)
}
/**
 * 添加子账号
 *
 */
 export const addChildAccount = (data) => {
  return http('post', `/merchant/ad/add_oceanengine_children`, data)
}
/**
 * 获取组织/广告主列表
 *
 */
 export const getOrglist = (data) => {
  return http('get', `/merchant/ad/get-oceanengine-list`, data)
}
/**
 * 授权检测
 *
 */
 export const getAuthCheck = (data) => {
  return http('get', `/merchant/ad/get_oceanengine_empower`, data)
}
export const manual_batch_ad_sync = (data) => {
  return http('post', `/merchant/ad/manual_batch_ad_sync`, data)
}
// 分配 media_type 1 广点通， 4 磁力， 5 bilibili 6 巨量， 7 百度
export const configUserApi = (data) => {
  return http('post', `/merchant/ad/assign-account`, data)
}
/**
 * 分配记录  media_type 1 广点通， 4 磁力， 5 bilibili 6 巨量， 7 百度
 *
 */ 
export const getAssignLog = (data) => {
  return http('get', `/merchant/ad/assign-account-log`, data)
}
