<template>
  <DesTablePage class="page_main common_page_warp">
    <template #title> 巨量引擎 </template>
    <template #tableWarp>
      <TabList :active="data.type" @change="tabsChange" />
      <SearchBaseLayout ref="searchFormDataRef" :data="searchList" @changeValue="changeValue" :actions="actions" />
      <div class="mt-24px mb-16px">
        <!-- <a-button v-auth="['sysOceanAdd']" type="primary" @click="getAuthUrl"> 添加账户 </a-button> -->
        <a-button
          v-auth="['sysOceanShare']"
          :disabled="data.type == 'ad' && data.selectedRowKeys.length === 0"
          type="primary"
          @click="onConfigDialog('config', 'batch')"
        >
          批量分配
        </a-button>
        <!-- <a-button
          v-if="isAuth(['original_ocean_batch_delete']) && data.selectedRowKeys.length"
          type="primary"
          @click="onDel"
        >
          批量删除账户
        </a-button> -->
      </div>
      <TableZebraCrossing :data="tableData" @change="pageChange" :row-selection="rowSelectionConfig">
        <template #bodyCell="{ scope }">
          <template v-if="scope.column.key === 'status'">
            <span v-if="scope.record.empower_status === 1" class="c-#40AE26">已授权</span>
            <span v-else class="c-#868686"> 授权已失效 </span>
          </template>
          <template v-if="scope.column.key === 'operation'">
            <a-space>
              <!-- <a-button
                size="small"
                class="pa-0!"
                type="link"
                :disabled="scope.record.empower_status === 0"
                @click="updateData(scope.record)"
                >更新</a-button
              > -->
              <!-- <a-button v-auth="['sysOceanAuth']" type="link" @click="checkAuth(scope.record)">{{
                scope.record.empower_status === 1 ? '同步' : '授权'
              }}</a-button>
              <a-popconfirm title="请确认是否删除当前账户？" placement="topRight" @confirm="onDel(scope.record)">
                <a-button v-auth="['sysOceanDel']" type="link">删除</a-button>
              </a-popconfirm> -->

              <!-- <a-button type="link" @click="onShowDialog('add', scope.record)" v-auth="['sysOceanExport']" v-show="data.type === 'org'">导入账户</a-button> -->
              <a-button type="link" v-auth="['sysOceanShare']" @click="onConfigDialog('config', scope.record)"
                >分配</a-button
              >
              <a-button type="link" v-auth="['sysOceanShare']" @click="onConfigDialog('shareList', scope.record)"
                >分配记录</a-button
              >
            </a-space>
          </template>
        </template>
      </TableZebraCrossing>
      <a-modal
        v-model:open="data.dialog.visible"
        wrapClassName="modal-custom-style"
        :title="data.dialog.title"
        :width="data.dialog.width"
        destroyOnClose
        :footer="null"
      >
        <AccountList v-if="data.dialog.type === 'add'" :from="data.type" :item="data.dialog.item" @event="onEvent" />
        <AssignTem v-if="data.dialog.type === 'config'" :item="data.dialog.item" @event="onEvent" :media_type="6" />
        <ShareList v-if="data.dialog.type === 'shareList'" :data="data.dialog.item" :media_type="6" />
      </a-modal>
    </template>
  </DesTablePage>
</template>
<script setup name="AdDmp">
  import AccountList from './components/AccountList.vue'
  import TabList from './components/TabList.vue'
  import AssignTem from './components/AssignPerson.vue'
  import ShareList from './components/shareList.vue'
  import { ref } from 'vue'
  import datas from './src/datas'
  import { useAuth } from '@/hooks'
  const { isAuth } = useAuth()
  const {
    searchList,
    columns,
    data,
    actions,
    tableData,
    getAuthUrl,
    checkAuth,
    changeValue,
    onDel,
    getLocalTime,
    getList,
    pageChange,
    changeRadio,
    getadCallback,
    onShowDialog,
    onEvent,
    updateData,
    onConfigDialog,
    rowSelectionConfig
  } = datas()
  const account_role_type = ref('ACCOUNT_ROLE_TYPE_ADVERTISER')
  const searchFormDataRef = ref()
  const tabsChange = (val) => {
    data.type = val
    if (val == 'org') {
      tableData.columns = columns.filter((item) => item.key != 'oceanengine_name')
    } else {
      tableData.columns = columns
    }
    searchFormDataRef.value.changeFormValue(false)
  }
  getadCallback()
</script>
<style lang="scss" scoped>
  .pd {
    padding: 20px 30px 4px 30px;
    border-bottom: 1px solid #eef0f3;
  }
  .btn_group {
    margin-bottom: 20px;
    :deep(.el-radio-button__inner) {
      padding: 9px 15px;
    }
  }
  .round {
    width: 8px;
    height: 8px;
    background: #60a13b;
    border-radius: 50%;
  }
  .red {
    color: red;
  }
  .bgred {
    background: red;
  }
  .btn {
    color: #118bce;
    cursor: pointer;
    user-select: none;
    margin-right: 5px;
  }
  .mr {
    margin-right: 15px;
  }
  :deep(.ant-radio-button-wrapper-checked) {
    border-color: #d9d9d9 !important;
    &::before {
      background-color: #d9d9d9 !important;
    }
  }
</style>
