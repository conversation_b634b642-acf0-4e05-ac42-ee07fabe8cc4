<template>
  <div class="tab-warp">
    <a-tabs v-model:activeKey="activeName" @change="handleChange">
      <a-tab-pane key="ad" tab="广告主账户"></a-tab-pane>
      <!-- <a-tab-pane key="org" tab="组织账户"></a-tab-pane> -->
    </a-tabs>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  const props = defineProps(['active'])
  const activeName = ref(props.active)
  const emits = defineEmits(['change'])
  function handleChange(val) {
    emits('change', val)
  }
  watch(
    () => props.active,
    (vld) => {
      activeName.value = vld
    },
    { immediate: true }
  )
</script>

<style scoped lang="scss">
  .tab-warp {
    :deep(.el-tabs__header) {
      margin: 0;
    }
  }
</style>
