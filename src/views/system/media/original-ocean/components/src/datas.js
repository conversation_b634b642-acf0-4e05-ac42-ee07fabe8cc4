import { reactive } from 'vue'
import { getAssignLog } from '../../index.api.js'


export default function datas() {
  const searchList = reactive([
    {
      type: 'input.text',
      field: 'corporation_name',
      value: undefined,
      props: {
        placeholder: '请输入账户名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  ])
  const shareListSearch = reactive([
    {
      type: 'select',
      field: 'to_admin_ids',
      value: undefined,
      props: {
        placeholder: '请选择',
        options: [
         
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
    ])
    const columns = reactive([
      {
        title: '账户编号',
        dataIndex: 'account_id',
        key: 'account_id',
        width: 150,
        ellipsis: true
      },
      {
        title: '账户名称',
        dataIndex: 'account_name',
        key: 'account_name',
        width: 240,
        ellipsis: true
      },
      {
        title: '分配人员',
        dataIndex: 'admin_name',
        key: 'admin_name',
        width: 200,
        ellipsis: true
      },
      
      {
        title: '分配时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 140
      }
    ])
    const data = reactive({
      params: {
        page: 1,
        page_size: 10
      },
      selectedRowKeys: [],
      dialog: {
        title: '',
        type: '',
        visible: false,
        width: null
      }
    })
    const actions = {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
    // 表格数据
    const tableData = reactive({
      bordered: false,
      scroll: {
        scrollToFirstRowOnChange: true,
        x: 800
      },
      rowKey: 'id',
      dataSource: [],
      loading: false,
      columns: columns,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        pageSize: 10,
        current: 1,
        size: 'small',
        showTotal: (total) => `共${total}条数据`
      }
    })
    const changeValue = (v) => {
      if (v.status) {
        data.params = {
          ...data.params,
          to_admin_ids: !v.formData.to_admin_ids ? '' : v.formData.to_admin_ids
        }
        data.params.page = 1
        getList()
      } else {
        data.params = {
          page: 1,
          media_type: data.params.media_type,
          token_account_id: data.params.token_account_id,
          page_size: 10
        }
        getList()
      }
    }
// 获取广告列表
  const getList = async () => {
    try {
      let res = null
      tableData.loading = true
      const params = {
        ...data.params,
        
      }
      res = await getAssignLog(params)
      tableData.dataSource = res.data?.list || []
      tableData.pagination.total = res.data?.total || 0
      tableData.pagination.current = res.data.page || 1
      tableData.loading = false
    } catch (error) {
      console.error(error)
      tableData.loading = false
    }
  }
  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }


  return { searchList, tableData, shareListSearch, actions,data, changeValue, pageChange,getList }
}
