<template>
  <div>
    <SearchBaseLayout ref="searchFormDataRef" :data="shareListSearch" @changeValue="changeValue" :actions="actions" />

    <TableZebraCrossing :data="tableData" @change="pageChange">
      <template #bodyCell="{ scope }"> </template>
    </TableZebraCrossing>
  </div>
</template>
<script setup>
  import { onMounted, reactive } from 'vue'
  import datas from './src/datas'
  import { getAssignLog } from '../index.api'
  import { get_user_list } from '@/views/system/manage/organization/index.api'

  const { shareListSearch, actions, columns, tableData, pageChange, changeValue, getList, data } = datas()

  const props = defineProps({
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    media_type: {
      type: Number,
      default: 1
    }
  })
  onMounted(() => {
    // console.log('-----65565', props.data, data)
    const params = {
      media_type: props.media_type,
      token_account_id: props.data.token_account_id || props.data.advertiser_id
    }
    data.params = {
      ...data.params,
      ...params
    }
    getDataInfo()
    getList()
  })

  const getDataInfo = async () => {
    const res = await get_user_list({ page: 1, page_size: 500 })
    const data = res.data.list.map((item) => {
      return {
        label: item.userinfo.realname,
        value: item.userinfo.id
      }
    })
    shareListSearch?.forEach((item) => {
      if (item.field === 'to_admin_ids') {
        item.props.options = data
      }
    })
  }
</script>
