<template>
  <div class="mt-40px">
    <a-form
      ref="formRef"
      :model="state.formState"
      name="basic"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
      @finish="onFinish"
      @finishFailed="onFinishFailed"
    >
      <a-form-item label="分配人员" name="username" :rules="[{ required: true, message: '请选择分配人员' }]">
        <a-select
          allowClear
          showSearch
          :filter-option="onFiterOption"
          placeholder="请选择分配人员"
          v-model:value="state.formState.username"
          :options="state.orgList"
        />
      </a-form-item>
    </a-form>
    <div class="flex flex-justify-end m-t-2 mt-40px">
      <a-button @click="() => emits('event', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" :loading="state.saveLoading" @click="submit">确定</a-button>
    </div>
  </div>
</template>
<script setup lang="tsx">
  import { uniqBy } from 'lodash'
  import { reactive, ref, watch, onMounted, nextTick } from 'vue'
  import { message } from 'ant-design-vue'
  import { getChildInfo, getOrglist, addChildAccount, configUserApi } from '../index.api'
  import { get_user_list } from '@/views/system/manage/organization/index.api'
  const props = defineProps(['from', 'item', 'media_type'])
  const emits = defineEmits(['event'])
  const formRef = ref()

  const state = reactive({
    saveLoading: false,
    activeIds: [], //ids
    orgList: [], //组织列表
    formState: {
      username: undefined
    }
  })

  onMounted(() => {
    getDataInfo()
    // if (props.item.admin_id) {
    //   state.formState.username = props.item.admin_id
    // }
  })

  const getDataInfo = async () => {
    const res = await get_user_list({ page: 1, page_size: 500, ad: 1 })
    state.orgList = res.data.list.map((item) => {
      return {
        label: item.userinfo.realname,
        value: item.userinfo.id
      }
    })
    console.log(res, 'res')
  }
  const onFiterOption = (value: any, option: any) => {
    if (option.label.indexOf(value) != -1) {
      return true
    } else {
      return false
    }
  }

  async function submit() {
    try {
      if (!(await formRef.value.validate())) return
      const params = {
        admin_id: state.formState.username,
        media_type: props.media_type,
        token_account_ids: String(props.item.token_account_id || props.item.advertiser_id)
      }
      await configUserApi(params)
      message.success('操作成功')
      state.saveLoading = true
      emits('event', { cmd: 'refresh' })
    } catch (error) {
      console.error(error)
    } finally {
      state.saveLoading = false
    }
  }
</script>
