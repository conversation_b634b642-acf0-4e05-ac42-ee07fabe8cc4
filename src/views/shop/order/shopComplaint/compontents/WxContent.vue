<template>
  <a-space class="w-full" direction="vertical">
    <WxMessage @unloadwx="unloadwx" />
    <SearchBaseLayout
      class="mt9px"
      :data="state.searchConfigData.data"
      @changeValue="changeValue"
      :actions="state.searchConfigData.options"
      :key="state.timeFlag"
    />
    <div class="flex flex-items-center flex-justify-between">
      <div class="flex">
        <ButtonRadioGroup :data="state.actionConfig" @changeValue="selectDataItem" />
        <div class="m-t-24px flex flex-items-center laba_msg" v-if="state.complaint_switch == 1">
          <SvgIcon icon="laba" class="m-r-8px" />
          <span>微信支付投诉已被平台接管处理，仅可查看操作记录，无法处理</span>
        </div>
      </div>

      <div class="mt-34px">
        <a-checkbox v-model:checked="state.autoReplyAfter" @change="handleAutoReplyAfterChange"
          >等待人工介入</a-checkbox
        >
        <a-checkbox v-model:checked="state.isNewMsg" @change="handleNewMsgChange">新消息回复</a-checkbox>
        <a-checkbox v-model:checked="state.replied" @change="handleRepliedChange">已回复消息</a-checkbox>
      </div>
    </div>
    <DescTableLayout :data="state.tableConfigData">
      <template #action>
        <a-space align="center" v-if="state.tableConfigData.list.length" class="mb-12px">
          <a-checkbox
            :indeterminate="state.indeterminate"
            :checked="state.allSelectTable.length && state.selectTable.length === state.allSelectTable.length"
            @change="onCheckAllChange"
          >
            当页全选
          </a-checkbox>
          <a-button @click="batchSendMsg()">批量发送短信</a-button>

          <a-button
            v-if="state.actionConfig.value === 'processing'"
            :disabled="!state.selectItem.length"
            type="primary"
            v-auth="['shopToushuBatchComplete']"
            @click="handleBatch"
            >批量处理完成</a-button
          >
        </a-space>
      </template>

      <template #desc="{ data }">
        <div class="desc-table-layout-list-item-desc">
          <a-row :gutter="20">
            <a-col>
              <a-checkbox
                :checked="state.selectTable.includes(data.id)"
                @change="(checkedValue) => tabelItemSelect(checkedValue, data)"
              >
              </a-checkbox>
            </a-col>
            <a-col>
              <span>订单编号: </span>
              <span>{{ data.order_info.order_num }}</span>
            </a-col>
            <a-col>
              <span>订单来源: </span>
              <span>{{ data.app_name }}</span>
            </a-col>
            <a-col>
              <span>投诉原因: </span>
              <span>{{ typeType(data.problem_type) || '--' }}</span>
            </a-col>
          </a-row>
        </div>
      </template>
      <template #bodyCell="{ scope, pindex }">
        <template v-if="scope.column.key === 'order'">
          <ul class="flex pb_14">
            <li>
              <div class="goods-img"><img :src="scope.record.order_product.image || errorImg" alt="" /></div>
            </li>
            <li class="flex_1">
              <div class="goods-img-title">
                <a-tooltip placement="topLeft">
                  <template #title>{{ scope.record.order_product.name }}</template>
                  <div class="goods-title">
                    {{ scope.record.order_product.name }}
                  </div>
                </a-tooltip>
              </div>
              <div class="goods-mess-item">
                <span>商品ID:</span>
                <span>{{ scope.record.order_product.product_code }}</span>
              </div>
              <div class="goods-mess-item">
                <span>{{ scope.record?.order_product?.sku_name || '--' }}</span>
                <span>{{ scope.record?.order_product?.num || '--' }}件</span>
              </div>
              <div class="goods-mess-item">
                <span>实付款:</span>
                <span>¥ {{ centsToYuan(scope.record.amount) || '0.00' }}</span>
              </div>
              <span
                class="border p-4px pt0 pb0 border-rd-2px font-size-12px border-color-#C4EEE2 color-#19A378 bg-#E8F6F2 mr8px"
                v-show="[4].includes(scope.record?.activity_type)"
              >
                送礼物
              </span>

              <!-- <div v-if="scope.record.order_type == 2" class="pl-10px mt--3px">
                <span
                  class="border p-3px pt0 pb0 border-rd-2px border-color-#E6D9FF color-#7C49DC bg-#F8F5FF font-size-12px line-height-14px inline-block"
                >
                  全球购
                </span>
              </div> -->
            </li>
          </ul>
        </template>
        <template v-if="scope.column.key === 'sale'">
          <div>{{ orderStatusType(scope.record.order_info.order_status) }}</div>
          <div v-if="scope.record.refund_sn">
            <div v-if="state.saleData">{{ saleStatusType(scope.record.after_sale_status) }}</div>
            <div
              class="cursor-pointer item-primary"
              @click="router.push({ path: '/mall/order/sale_list', query: { refund_sn: scope.record.refund_sn } })"
            >
              {{ scope.record.refund_sn }}
            </div>
          </div>
          <div v-else>无售后</div>
          <div :style="{ color: customsState(scope.record.customs_status)?.color }">
            {{ customsState(scope.record.customs_status)?.name }}
          </div>
        </template>
        <template v-if="scope.column.key === 'source'">
          <a-space direction="vertical">
            <div>{{ scope.record.app_name }}</div>
            <div>
              投诉单号：
              <div class="w-150px line-height-none">{{ scope.record.complaint_id }}</div>
            </div>
          </a-space>
        </template>
        <template v-if="scope.column.key === 'user'">
          <div class="user">
            <div class="user-content">
              <!-- <div>
                <WechatOutlined class="wx-item" />
                <span class="wx-item">{{ scope.record.user.phone }}</span>
              </div> -->
              <div class="flex_ju_sp">
                <div class="user-content-name">
                  <span>{{ scope.record.order_spread.consignee }}</span>
                  <span class="user-content-phone">
                    {{ scope.record.order_spread.receive_phone }}
                    <DataEncipher
                      v-if="isAuth(['shopComplaintLookIphone'])"
                      v-model:data="state.tableConfigData.list[pindex]"
                      showKey="show_phone"
                      :goalkey="['order_spread', 'receive_phone']"
                      :type="1"
                      :argKey="['order_info', 'order_id']"
                    />
                  </span>
                </div>
                <a-tag color="error" class="m-0 pl-4px pr-4px" v-if="scope.record.is_new == 1">新回复</a-tag>
              </div>
              <div class="text_overflow_2 complaint_detail">
                <template v-if="scope.record?.complaint_detail">
                  <a-tooltip placement="top" :title="scope.record.complaint_detail">
                    <span>{{ scope.record?.complaint_detail }}</span>
                  </a-tooltip>
                </template>
                <template>--</template>
              </div>
              <div class="first-complaint" v-if="scope.record?.first_complaint_detail">
                <div class="text-right">
                  <a-tag color="error" class="m-0 pl-4px pr-4px">首次投诉</a-tag>
                </div>
                <div class="text_overflow_2 complaint_detail mt-3px">
                  <a-tooltip placement="top" :title="scope.record.first_complaint_detail">
                    <span>{{ scope.record?.first_complaint_detail || '--' }}</span>
                  </a-tooltip>
                </div>
              </div>
              <div class="user-content-date">投诉时间：{{ scope.record.complaint_time }}</div>
            </div>
          </div>
        </template>
        <template v-if="scope.column.key === 'wx_code'">
          <div>
            <div class="w-180px line-height-none">{{ scope.record.transaction_id }}</div>
            <div>{{ scope.record.complainted_mchid }}</div>
          </div>
        </template>
        <template v-if="scope.column.key === 'status'">
          <div>
            <span
              :style="{
                color:
                  scope.record.complaint_state == 'PENDING'
                    ? '#E63030'
                    : scope.record.status == 'PROCESSING'
                      ? '#E77316'
                      : ''
              }"
              >{{ complaintType(scope.record.complaint_state) }}</span
            >
            <div v-if="[6, 9].includes(scope.record.after_sale_status)">
              <div class="c-#e63030">退款完成</div>
            </div>
            <div v-else>
              <div v-if="['PENDING'].includes(scope.record.complaint_state)">
                <div class="mt-4px font-size-12px c-#e63030" v-if="scope.record.notice_status == 1">
                  已超时，请尽快处理！
                </div>
                <countDown
                  v-if="scope.record.notice_time && !scope.record.notice_status"
                  :target-time="scope.record.notice_time"
                ></countDown>
              </div>
            </div>
          </div>
          <div class="tag" v-if="scope.record.complaint_switch == 1">平台已接管处理</div>
          <div v-if="![0, 99].includes(scope.record.auto_reply_after)">自动处理</div>
          <div class="c-#e63030" v-if="scope.record.auto_reply_after == 1">等待人工介入</div>
        </template>
        <template v-if="scope.column.key === 'related_complaints'">
          <div class="related_complaints_cell">
            <div class="related_complaints table_bottom flex">
              <div class="related_complaints_lable">
                <span @click="handleShowRelated(2, scope.record.order_info.order_num, 0)">小程序投诉</span>
              </div>
              <div class="related_complaints_item">
                <div class="table_bottom" @click="handleShowRelated(2, scope.record.order_info.order_num, 1)">
                  待处理：<span class="related_complaints_num red_text">{{
                    relateDataFilter(2, 'wait_num', scope.record.relate_data)
                  }}</span>
                </div>
                <div class="table_bottom" @click="handleShowRelated(2, scope.record.order_info.order_num, 2)">
                  处理中：<span class="related_complaints_num red_text">{{
                    relateDataFilter(2, 'process_num', scope.record.relate_data)
                  }}</span>
                </div>
                <div @click="handleShowRelated(2, scope.record.order_info.order_num, 3)">
                  已处理：<span>{{ relateDataFilter(2, 'complete_num', scope.record.relate_data) }}</span>
                </div>
              </div>
            </div>
            <div class="related_complaints flex">
              <div class="related_complaints_lable">
                <span @click="handleShowRelated(3, scope.record.order_info.order_num, 0)"
                  >{{ getConfig('TITLE') }}平台投诉</span
                >
              </div>
              <div class="related_complaints_item">
                <div class="table_bottom" @click="handleShowRelated(3, scope.record.order_info.order_num, 1)">
                  待处理：<span class="related_complaints_num red_text">{{
                    relateDataFilter(1, 'wait_num', scope.record.relate_data)
                  }}</span>
                </div>
                <div class="table_bottom" @click="handleShowRelated(3, scope.record.order_info.order_num, 2)">
                  处理中：<span class="related_complaints_num red_text">{{
                    relateDataFilter(1, 'process_num', scope.record.relate_data)
                  }}</span>
                </div>
                <div @click="handleShowRelated(3, scope.record.order_info.order_num, 3)">
                  已处理：<span>{{ relateDataFilter(1, 'complete_num', scope.record.relate_data) }}</span>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-if="scope.column.key === 'action'">
          <a-space direction="vertical" class="w-full" :size="0">
            <a-button
              v-auth="['shopToushulookOrder']"
              class="pl-0 pr-0"
              size="small"
              type="link"
              @click="handleActions('details', scope.record)"
            >
              查看订单
            </a-button>
            <a-button class="pa-0 ma-0" size="small" type="link" @click="handleActions('sendMsg', scope.record)"
              >发送短信</a-button
            >
            <a-popconfirm
              title="是否确定处理完成？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="confirm(scope.record.id)"
            >
              <a-button
                type="link"
                size="small"
                class="pl-0 pr-0"
                v-if="scope.record.complaint_state == 'PROCESSING' && scope.record.complaint_switch == 0"
              >
                处理完成
              </a-button>
            </a-popconfirm>
            <div v-if="scope.record.complaint_state !== 'PROCESSED' && scope.record.complaint_switch == 0">
              <a-button
                class="pl-0 pr-0"
                v-auth="['shopToushuDispone']"
                type="link"
                size="small"
                @click="handleActions('handle', scope.record)"
                >处理投诉</a-button
              >
            </div>
            <a-button v-else size="small" class="pl-0 pr-0" type="link" @click="handleActions('handle', scope.record)">
              查看详情
            </a-button>
            <a-button
              class="pa-0"
              style="color: #f2a626"
              v-auth="['wxorderComplaintRemark']"
              type="link"
              size="small"
              @click="handleActions('remark', scope.record)"
            >
              备注({{ scope.record.shop_remark_count || 0 }})
            </a-button>
          </a-space>
        </template>
      </template>
    </DescTableLayout>
    <Pagination
      v-model:page="state.initParams.page"
      v-model:pageSize="state.initParams.page_size"
      :total="state.tableConfigData.pagination.total"
      @change="initData"
    ></Pagination>
    <a-modal
      v-model:open="state.modalConfigData.open"
      :title="state.modalConfigData.title"
      centered
      :closable="state.modalConfigData.type === 'remark'"
      @cancel="modalCancel"
      @ok="modalOk"
    >
      <RemarkDialog
        ref="formRef"
        v-if="['remark'].includes(state.modalConfigData.type)"
        :item="state.modalConfigData.data"
      />
      <SendMsg
        v-if="['sendMsg'].includes(state.modalConfigData.type)"
        v-model:data="state.modalConfigData.data"
        ref="formRef"
      />
    </a-modal>
  </a-space>
</template>
<script setup lang="ts">
  import RemarkDialog from './RemarkDialog.vue'
  import { SvgIcon } from '@/components'
  import Pagination from '@/components/ui/common/PaginationComponent/index.vue'
  import { onActivated, onMounted, createVNode, reactive, ref } from 'vue'
  import WxMessage from './WxMessage.vue'
  import datas from '../wx_data'
  import {
    get_merchantcomplaints_list,
    get_merchantcomplaints_head,
    post_exportdata_create,
    handleComplete,
    saleStatus,
    batch_complete,
    recordAdd
  } from '../index.api'
  import { batch_send_msg } from '@/api/common'
  import { centsToYuan, getConfig, convertToImg } from '@/utils'
  import { useRoute, useRouter } from 'vue-router'
  import { message, Modal, notification } from 'ant-design-vue'
  import { useDownloadCenter, useAuth } from '@/hooks'
  import { cloneDeep, isArray } from 'lodash-es'
  import countDown from './countDown.vue'
  import SendMsg from '@/views/shop/order/order_list/components/SendMsg.vue'
  import errorImg from '@/assets/images/error.png'
  import dayjs from 'dayjs'
  const last30DaysStart = dayjs().subtract(29, 'days').startOf('day')
  const { goCenter } = useDownloadCenter()
  const { isAuth } = useAuth()
  const router = useRouter()
  const route = useRoute()
  const formRef = ref(null)
  const props = defineProps(['tabData', 'active'])
  const {
    actionConfig,
    shopComplaintStatus,
    searchConfigData,
    tableConfigData,
    initParams,
    typeType,
    complaintType,
    orderStatusType,
    customsState,
    staticTitle
  } = datas()
  const emits = defineEmits(['event', 'tabEvent', 'resetTabSwitch'])
  const state = reactive({
    initParams,
    actionConfig,
    shopComplaintStatus,
    searchConfigData,
    tableConfigData,
    saleData: null,
    timeFlag: Date.now(),
    complaint_switch: 0,
    isNewMsg: false, // 是否为新消息回复
    autoReplyAfter: false,
    replied: false,
    indeterminate: false,
    selectTable: [] as any,
    allSelectTable: [] as any,
    selectItem: [] as any,
    modalConfigData: {
      open: false,
      title: '',
      type: '',
      data: ''
    }
  })

  // Filters
  const relateDataFilter = (type: any, field: any, data: any) => {
    if (!data || !isArray(data)) return 0
    const target = data.find((v) => v.type == type)
    if (!target) return 0
    return target[field]
  }

  // 关联投诉切换
  const handleShowRelated = (tab: any, order_num: any, status: any) => {
    emits('tabEvent', tab, order_num, status)
  }
  const isNumeric = (value: string | number): boolean => !isNaN(Number(value)) && isFinite(Number(value))
  onMounted(() => {
    if (props.active && !isNumeric(props.active)) {
      selectDataItem({ value: props.active })
    } else {
      initData()
    }
  })

  onActivated(() => {
    setTimeout(() => {
      const order_num = props.tabData?.order_num
      const status = props.tabData?.complaint_state
      if (order_num || status) {
        state.searchConfigData.data.forEach((item) => {
          if (item.field === 'complaint_time') item.value = undefined
        })
        state.searchConfigData.data[0].value = order_num || undefined
        state.actionConfig.value = isNumeric(status?.toLowerCase()) || !status ? 'all' : status?.toLowerCase()
        state.initParams.order_num = order_num
        state.initParams.complaint_time = undefined
        if (route.query.complaint_state) {
          state.initParams.complaint_state = route.query.complaint_state
        } else if (order_num) {
          state.initParams.complaint_state = !isNumeric(status) ? status : undefined
        } else {
          state.initParams.complaint_state = undefined
        }
        state.timeFlag = Date.now()
        initData()
      }
    }, 0)
  })

  // 处理完成
  const confirm = async (id) => {
    await handleComplete({ id })
    message.success('操作成功')
    initData()
  }
  // 初始化全选数据
  const initSelectAll = (data: any, key: string) => {
    const allArr: any[] = []
    data && data.forEach((item: any) => allArr.push(item[key]))
    return allArr
  }

  const initData = async () => {
    try {
      state.tableConfigData.isLoading = true
      // state.tableConfigData.list = []
      await getSaleStatus()
      const cloneParams = cloneDeep(state.initParams)
      const listResult = await get_merchantcomplaints_list(cloneParams)
      state.complaint_switch = listResult.data.complaint_switch
      let params = {
        ...cloneParams
      }
      delete params.complaint_state
      const headResult = await get_merchantcomplaints_head(params)
      if (listResult.data?.list) {
        state.tableConfigData.list = (listResult.data.list || []).map((v: any) => {
          return {
            ...v,
            show_phone: 0
          }
        })
        state.tableConfigData.pagination.total = listResult.data.total_num
        state.tableConfigData.pagination.current = listResult.data.page
        state.tableConfigData.pagination.pageSize = listResult.data.page_size
        state.allSelectTable = initSelectAll(state.tableConfigData.list, 'id')
      } else {
        state.tableConfigData.list = []
        state.tableConfigData.pagination.total = 0
        state.tableConfigData.pagination.pageSize = 10
      }
      if (headResult.data) {
        state.actionConfig.list.forEach((item) => {
          let val = item.value.toLowerCase()
          item.total = headResult.data[val] || 0
        })
      }
      state.tableConfigData.isLoading = false
      state.indeterminate = false
      state.selectTable = []
      state.selectItem = []

      // 刷新角标
      emits('event')
    } catch (error) {
      state.tableConfigData.isLoading = false
    }
  }

  // 获取售后状态
  const getSaleStatus = async () => {
    try {
      let res = await saleStatus()
      state.saleData = res.data.list || {}
    } catch (error) {}
  }

  const saleStatusType = (type) => {
    return state?.saleData[type]
  }

  const changeValue = (data) => {
    state.initParams = { ...state.initParams, page: 1, page_size: 10, ...data.formData }
    if (data.formData && data.formData.admin_ids) {
      state.initParams.admin_ids = data.formData.admin_ids.join(',')
    }
    if (data.formData && data.formData.complaint_time) {
      state.initParams.complaint_time = data.formData.complaint_time.join('_')
    }
    if (!data.status) {
      state.initParams = {
        page: 1,
        page_size: 10,
        order_num: undefined,
        order_product_name: undefined,
        problem_type: undefined,
        complaint_time: `${last30DaysStart.format('YYYY-MM-DD')}_${dayjs().format('YYYY-MM-DD')}`,
        transaction_id: undefined,
        complainted_mchid: undefined,
        user_phone: undefined,
        admin_ids: undefined,
        complaint_state: undefined
      }
      data.formData.complaint_time = [last30DaysStart.format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      state.isNewMsg = false
      state.autoReplyAfter = false
      state.replied = false
      emits('resetTabSwitch', { tab: '1' })
    }
    initData()
  }
  const selectDataItem = (data) => {
    state.actionConfig.value = data.value
    if (data.value === 'all') {
      state.initParams.complaint_state = undefined
    } else {
      state.initParams.complaint_state = data.value.toUpperCase()
    }
    state.initParams.page = 1
    state.initParams.page_size = 10
    initData()
  }
  const handleActions = (type, data) => {
    if (['details'].includes(type)) {
      router.push({ path: '/mall/order/order_detail', query: { id: data.order_info.order_id } })
    }
    if (['handle'].includes(type)) {
      router.push({ name: 'WxHandle', query: { id: data.id } })
    }
    if (['remark', 'sendMsg'].includes(type)) {
      state.modalConfigData.open = true
      state.modalConfigData.type = type
      state.modalConfigData.data = data
      state.modalConfigData.title = staticTitle[type]
    }
  }
  const modalCancel = () => {
    state.modalConfigData.open = false
    setTimeout(() => {
      if (['remark'].includes(state.modalConfigData.type)) {
        initData()
      }
      state.modalConfigData.data = ''
      state.modalConfigData.type = ''
      state.modalConfigData.title = ''
    }, 300)
  }
  const modalOk = async () => {
    if (['remark'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.validateFields()
      if (values) {
        let list =
          values.url?.map((v) => {
            if (v?.fileInfo?.type == 'video') {
              return {
                url: v.url,
                type: v?.fileInfo?.type,
                cover_image: convertToImg(v.url)
              }
            } else return { url: v.url, type: 'image' }
          }) || []
        const result = await recordAdd({
          order_id: state.modalConfigData.order_id || state.modalConfigData.data.order_info.order_id,
          ...values,
          url: JSON.stringify(list)
        })
        if (result.code === 0) {
          message.success('操作成功')
          initData()
          formRef.value.getRemarkList()
        }
      }
    }
    if (['sendMsg'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.validateFields()
      let order_nums =
        state.modalConfigData.data.type == 'batch'
          ? state.selectItem.map((item) => item.order_info.order_num)
          : [state.modalConfigData.data.order_info.order_num]

      if (values) {
        if (values.content.includes('{立减金额}')) {
          return message.warning('该模板内包含有【立减金额】，请先确认【立减金额】再选用模板')
        }

        const result = await batch_send_msg({
          order_sn_list: order_nums,
          sms_temp_id: values.sms_temp_id
        })
        if (result.code === 0) {
          message.success('操作成功')
          initData()
          modalCancel()
        }
      }
    }
  }
  const unloadwx = async () => {
    const result = await post_exportdata_create({
      type: 'wechatComplaint',
      params: JSON.stringify(state.initParams)
    })
    if (result.code === 0) {
      goCenter('DownloadCenter', 'DownloadCenter')
    }
  }

  // 是否为新消息提示
  const handleNewMsgChange = (e: any) => {
    if (e.target.checked) {
      state.initParams.is_new = 1
    } else {
      delete state.initParams.is_new
    }
    initData()
  }
  const handleAutoReplyAfterChange = (e: any) => {
    if (e.target.checked) {
      state.initParams.auto_reply_after = 1
    } else {
      delete state.initParams.auto_reply_after
    }
    initData()
  }
  const handleRepliedChange = (e: any) => {
    if (e.target.checked) {
      state.initParams.replied = 1
    } else {
      delete state.initParams.replied
    }
    initData()
  }
  const handleBatch = async () => {
    try {
      if (state.selectItem.length <= 0) return
      // if (state.selectItem.some((it) => it.after_sale_status !== 6)) {
      //   message.warning('当所有投诉单的售后状态为退款完成时才支持批量处理完成操作')
      //   return
      // }
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '确认要将这些投诉单状态变更为处理完成吗？'),
        async onOk() {
          await batch_complete({ ids: state.selectItem.map((it) => it.id)?.join(',') })
          message.success('操作成功')

          state.indeterminate = false
          state.selectTable = []
          state.selectItem = []
          initData()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }

  const batchSendMsg = () => {
    if (!state.selectTable.length) {
      message.warning('请选择相关投诉进行批量操作')
      return
    }
    handleActions('sendMsg', { order_id: state.selectTable, type: 'batch' })
  }

  // 选择每一条数据
  const tabelItemSelect = (checkedValue: any, data: any) => {
    console.log(checkedValue, data)
    if (state.selectTable.includes(data.id)) {
      state.selectTable = state.selectTable.filter((item: any) => item !== data.id)
      state.selectItem = state.selectItem.filter((item: any) => item.id !== data.id)
    } else {
      state.selectTable.push(data.id)
      state.selectItem.push(data)
    }
    state.indeterminate = !!state.selectTable.length && state.selectTable.length < state.allSelectTable.length
  }
  // 全选
  const onCheckAllChange = (e: any) => {
    if (state.actionConfig.value === 'PROCESSING') {
      if (state.indeterminate) {
        Object.assign(state, {
          selectTable: [],
          selectItem: [],
          indeterminate: false
        })
        return
      }
      console.log('e.target.checked', e.target.checked)
      Object.assign(state, {
        selectTable: state.tableConfigData.list.map((it) => it.id).filter((o) => o),
        selectItem: state.tableConfigData.list.map((it) => it.id).filter((o) => o)
      })
      state.indeterminate = !!state.selectTable.length && state.selectTable.length < state.allSelectTable.length
    } else {
      Object.assign(state, {
        selectTable: e.target.checked ? state.allSelectTable : [],
        selectItem: e.target.checked ? state.tableConfigData.list : [],
        indeterminate: false
      })
    }
  }
</script>

<style scoped lang="scss">
  @import './src/assets/css/mixin_scss_fn';
  .laba_msg {
    background: #f9f9fa;
    border-radius: 2px;
    line-height: 1;
    padding: 10px 8px;
    margin-left: 8px;
    color: #e63030;
  }
  .tag {
    background: #fa5087;
    display: inline-block;
    border-radius: 2px;
    font-size: 12px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 1;
    padding: 2px 4px;
  }
  img {
    display: block;
    width: 100%;
    height: 100%;
  }
  .goods-img-title {
    display: flex;
    padding-bottom: 14px;
    .goods-title {
      flex: 1;
      overflow: hidden;
      padding-left: 10px;
      @include text_overflow(3);
    }
  }
  .pb_14 {
    padding-bottom: 14px;
  }
  .goods-img {
    @include set_node_whb(64px, 64px);
    @include set_border_radius(var(--border-radius-huge));
    overflow: hidden;
  }
  .goods-mess-item {
    padding-left: 10px;
    @include set_font_config(--font-size-mini, --text-color-gray);
  }

  .user {
    display: flex;
    .user-avatar {
      @include set_node_whb(48px, 48px);
      @include set_border_radius(50%);
      background-color: rgba(36, 47, 87, 0.03);
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    .user-content {
      flex: 1;
      // padding: 0 8px;
      box-sizing: border-box;
      .user-content-name {
        display: flex;
        align-items: center;
      }
      .user-content-phone {
        @include set_font_config(--font-size-small, --text-color-gray);
      }
      .user-content-date {
        @include set_font_config(--font-size-tiny, --text-color-gray);
      }
      .user-content-text {
        word-break: break-word;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
    }
  }
  .wx-item {
    color: #60a13b;
    margin-right: 5px;
    font-size: var(--font-size-small);
  }

  .desc-table-layout-list-item-desc {
    background-color: rgba(36, 47, 87, 0.05);
    padding: 8px 16px;
  }
  .item-primary {
    color: var(--primary-color);
  }

  .item-add {
    color: #fff;
    background: #fe8710;
  }
  .item-unadd {
    color: #636e95;
    background: #e8e8e9;
  }
  .related_complaints_cell {
    // height: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }

  .text_overflow_2 {
    @include text_overflow(2);
  }
  .complaint_detail {
    font-size: 14px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #0b1531;
    word-break: break-all;
  }
  .related_complaints {
    flex: 1;
    .related_complaints_lable {
      width: 116px;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        color: var(--primary-color);
        cursor: pointer;
      }
    }
    .related_complaints_item {
      flex: 1;
      border-left: 1px solid #f0f0f0;
      & > div {
        padding-left: 10px;
        padding-right: 10px;
        height: 33.33%;
        min-width: 76px;
        display: flex;
        align-items: center;
        font-size: 12px;
        cursor: pointer;
        white-space: nowrap;
        .red_text {
          color: var(--error-color);
        }
      }
    }
  }
  .table_bottom {
    border-bottom: 1px solid #f0f0f0;
  }
  :deep(.ant-table-wrapper .ant-table-tbody > tr > td.related_complaints_row) {
    padding: 0;
    position: relative;
  }
</style>
