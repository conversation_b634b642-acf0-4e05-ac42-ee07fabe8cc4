<template>
  <div>
    <div class="message-warp">
      <div class="item-text-warp" v-if="logisticsStatusEnums[state.materialFlowData?.StateEx || '-']">
        <span class="title">{{ logisticsStatusEnums[state.materialFlowData?.StateEx || '-'] || '-' }}</span
        ><span>请联系快递公司或者买家核实处理</span>
      </div>
      <div class="item-text-warp">
        <span>快递公司: </span
        ><span>{{ state.materialFlowData?.expressCompanyName || props.data.order_logistics_company }}</span>
      </div>
      <div class="item-text-warp">
        <span>快递单号: </span
        ><span>{{ state.materialFlowData?.LogisticCode || props.data.order_logistics_number }}</span>
      </div>
    </div>
    <div class="list-warp">
      <!-- <template> -->
      <a-timeline>
        <a-timeline-item v-for="item in state.materialFlowData?.logisticsTraceDetails">
          <!-- <span v-if="item.time != 0">{{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}</span>
            <span v-if="(!item.time || item.time == 0) && data.deliver_time != 0">{{
              secToTime(data.deliver_time)
            }}</span>
            <span> {{ item.desc }}</span> -->
          <span>{{ item.AcceptTime }}</span>
          <span class="ml-10px">{{ item.AcceptStation }}</span>
        </a-timeline-item>
        <!-- <a-timeline-item v-for="item in state.materialFlowData?.customs_detail">
                <span>{{ item.time }}</span>
                <span class="ml-10px">{{ item.message }}</span>
              </a-timeline-item> -->
        <a-timeline-item v-if="props.type != 'orderWl'">
          <span> {{ secToTime(data?.pass_refund_product_time) }}</span>
          <span class="ml-10px">商家同意退货</span>
        </a-timeline-item>

        <a-timeline-item v-if="props.type == 'orderWl'">
          <span v-if="data?.pay_time != 0">{{ secToTime(data?.pay_time) }}</span>
          <span class="ml-10px">您的订单已进入第三方卖家仓库，准备出库中</span>
          <!-- <span class="ml-10px" v-else>您的订单保税仓已接单，仓库正在准备清关发货</span> -->
        </a-timeline-item>
        <a-timeline-item v-if="props.type == 'orderWl'">
          <span>{{ secToTime(data?.order_create_time) }}</span>
          <span class="ml-10px">您提交了订单，等待卖家仓库发货</span>
        </a-timeline-item>
      </a-timeline>
      <!-- </template> -->
      <!-- <span v-else class="desc-card-no-text">暂无物流数据</span> -->
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive, watch } from 'vue'
  import { get_order_express } from '@/views/shop/order/order_details/index.api'
  import { secToTime } from '@/utils'
  import { logisticsStatusEnums } from '../data.ts'
  interface Params {
    express_num: number | string
    phone: number
    order_id?: number | string
    after_id?: number | string
  }
  interface Traces {
    AcceptStation: string
    AcceptTime: string
  }
  interface MaterialFlowData {
    StateEx?: string
    expressCompanyName?: string
    LogisticCode?: string
    logisticsTraceDetails?: Traces[]
  }
  const props = defineProps(['data', 'type'])
  const state = reactive({
    materialFlowData: {} as MaterialFlowData
  })
  const initMaterialFlowData = async () => {
    try {
      const params: Params = {
        express_num: props.type == 'orderWl' ? props.data.order_logistics_number : props.data.after_logistics_number,
        phone: props.type == 'orderWl' ? props.data.order_logistics_phone : props.data.receive_phone
      }
      if (props.type == 'orderWl') {
        params.order_id = props.data.order_id
      } else {
        params.after_id = `${props.data.id}`
      }
      const result: any = await get_order_express(params)
      if (result.code === 0) {
        state.materialFlowData = {
          ...result.data,
          expressCompanyName: props.data?.expressCompanyName,
          logisticsTraceDetails: (result.data?.Traces || []).reverse(),
          customs_detail: (result.data?.customs_detail || []).reverse()
        }
      }
    } catch (error) {
      state.materialFlowData = {
        expressCompanyName: props.data?.expressCompanyName,
        LogisticCode: props.type == 'orderWl' ? props.data.order_logistics_number : props.data.after_logistics_number
      }
      console.error(error)
    }
  }

  watch(
    () => props.type,
    () => {
      initMaterialFlowData()
    },
    {
      immediate: true
    }
  )
</script>

<style scoped lang="scss">
  .item-text-warp {
    line-height: 2;
    span {
      padding-right: 10px;
    }
  }
  .title {
    font-size: var(--font-size-huge);
    color: var(--primary-color);
  }
  .list-warp {
    height: 400px;
    padding: 20px 0;
    overflow: auto;
  }
</style>
