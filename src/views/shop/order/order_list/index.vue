<template>
  <div>
    <DesTablePage id="center" :pagination="state.paginationConfig" @changePages="changePages" class="hydPage">
      <template #title>
        <div>订单查询</div>
      </template>
      <template #extra>
        <a-button v-auth="['shopOrderListExport']" class="hyd" @click="downloadOrder"> 导出 </a-button>
      </template>
      <template #search>
        <div class="parcel hyd">
          <div class="desc">
            <div class="flex flex-items-center mb-16px justify-between">
              <div class="parcel-title flex flex-items-end">
                <div class="font-size-16px color-#5E6584">包裹异常监控</div>

                <div class="c-#999999 font-size-12px line-height-12px ml-8px">
                  数据更新时间：{{ state.logistic_date || '--' }}
                </div>
              </div>
            </div>
          </div>
          <div class="mb-9px">
            <ParcelList :list="state.logistics" show-tooltip source="orderList" @event="onParcelEvent" />
          </div>
        </div>
        <SearchBaseLayout
          ref="searchFormDataRef"
          :data="state.searchConfig.data"
          @changeValue="changeValue"
          :actions="state.searchConfig.options"
          use-config
        />
      </template>
      <template #action>
        <div class="flex-y-center flex-justify-between">
          <ButtonRadioGroup :data="state.actionConfig" @changeValue="changeBtnType" />
        </div>
      </template>
      <template #tableWarp>
        <DescTableLayout :data="state.tableConfig">
          <template #action>
            <div class="action-warp flex flex-justify-between">
              <a-space align="center">
                <a-checkbox
                  :indeterminate="state.indeterminate"
                  :checked="state.allSelectTable.length && state.selectTable.length === state.allSelectTable.length"
                  @change="onCheckAllChange"
                >
                  当页全选
                </a-checkbox>
                <!-- <template v-if="!useInfo.is_private_platform"> -->
                <a-button v-auth="['shopOrdersERP']" @click="syncErp('all')">同步ERP</a-button>
                <!-- </template> -->
                <a-button v-auth="['orderBatchRemark']" @click="batchRemark()">批量备注</a-button>
                <a-button @click="batchSendMsg()">批量发送短信</a-button>
                <a-button v-auth="['batchCheckOrder']" @click="batchCheckOrder()">批量查单</a-button>
              </a-space>
              <div class="checkbox mt-24px flex">
                <div v-show="[0, 3].includes(state.initParams.status)">
                  <a-checkbox v-model:checked="state.signed_for" @change="changeSignedFor">
                    <span class="inline-block">已签收</span>
                  </a-checkbox>
                </div>
                <div v-show="[0, 2].includes(state.initParams.status) && ![6].includes(state.initParams.after_status)">
                  <a-checkbox v-model:checked="state.urge_send" @change="changeUrgeSend">
                    <span class="inline-block">已催发货</span>
                  </a-checkbox>
                </div>
              </div>
              <!--            <a-space>-->
              <!--              <SetTableColumns class="cursor-pointer" v-model:data="state.tableConfig.columns" :column="columns" />-->
              <!--            </a-space>-->
            </div>
          </template>
          <template #desc="{ data }">
            <div class="desc-table-layout-list-item-desc flex flex-justify-between">
              <a-space>
                <span
                  ><a-checkbox
                    :checked="state.selectTable.includes(data.id)"
                    @change="(checkedValue) => tabelItemSelect(checkedValue, data)"
                  >
                  </a-checkbox
                ></span>
                <span class="mini_program_logo" v-if="data.order_type == 4">
                  <img src="@/assets/images/order/werxin_shop.png" alt="微信小店" />
                </span>
                <span v-if="data.order_type == 4">{{ data.wechat_shop_name || '--' }}</span>
                <span class="mini_program_logo">
                  <img v-if="data.page_type === 2" src="@/assets/images/order/icon_h5.png" alt="h5来源" />
                  <img v-else src="@/assets/images/order/mini_program.png" alt="小程序来源" />
                </span>
                <span>{{ data.app_name || '--' }}</span>
                <span>订单编号: {{ data.order_num }}</span>
                <span class="flex flex-items-center">
                  <span>来源: </span>
                  <template v-if="true">
                    <a-tooltip placement="topLeft">
                      <template #title>{{ channelType(data.channel_type) }}</template>
                      <img
                        v-if="[1, 4, 5, 6, 8].includes(data.channel_type)"
                        class="icon m-l-4px"
                        :src="iconType(data.channel_type)"
                        alt=""
                      />
                    </a-tooltip>
                    <span>{{ data.recall_type == 1 ? '弃单召回' : ordersource(data.order_source) }}</span>
                    <!-- <span v-if="[2, 3].includes(data.order_source)">{{ isCallbackType(data.is_callback) }}</span> -->
                    <span v-if="![1, 4].includes(data.order_source)"
                      >（计划ID：{{ data.c_id == 0 ? '--' : data.c_id }}
                      <span style="width: 5px; display: inline-block"></span> 账户ID：{{ data.ad_account_id || '--'
                      }}<a-tooltip>
                        <template #title
                          >广告账户授权异常，为避免报表数据错误，请点击
                          <span class="cursor-pointer c-#97DEFF" @click="getAuthUrl">去授权</span></template
                        >
                        <ExclamationCircleOutlined
                          v-if="data.is_authorized == 1"
                          class="c-#e63030 ml-10px mr-5px"
                        /> </a-tooltip
                      >）</span
                    >
                  </template>
                  <template v-else> -- </template>
                </span>
              </a-space>
              <a-space>
                <span>负责人: {{ data.admin_name }}</span>
                <span>
                  <span>所属项目:</span>
                  <a-tooltip placement="topLeft">
                    <template #title>{{ data.ad_name || '--' }}</template>
                    <span>{{
                      data.ad_name && data.ad_name.length > 6 ? `${data.ad_name.slice(0, 6)}...` : data.ad_name || '--'
                    }}</span>
                  </a-tooltip>
                </span>

                <span v-if="data.pay_time !== 0">支付时间: {{ secToTime(data.pay_time) }}</span>
                <span v-else>创建时间: {{ secToTime(data.created_at) }}</span>
              </a-space>
            </div>
          </template>
          <template #headerCell="{ scope, data }">
            <template v-if="scope.column.key === 'prices'">
              <span>{{ data.pay_time != 0 ? '实收金额（元）' : '应收金额（元）' }}</span>
            </template>
          </template>
          <template #bodyCell="{ scope, data }">
            <template v-if="scope.column.key === 'goods'">
              <div class="flex">
                <div class="cellos-item_img"><img :src="scope.record.image" alt="" /></div>
                <div class="flex" style="flex: 1">
                  <div class="flex-1 cellos-item-prod-warp">
                    <a-tooltip placement="topLeft">
                      <template #title>{{ scope.record.name }}</template>
                      <div class="cellos-item_title">
                        {{ scope.record.name }}
                      </div>
                    </a-tooltip>

                    <div class="cellos-item_style">
                      <a-tooltip placement="topLeft">
                        <template #title>{{ scope.record.sku_name || '--' }}</template>
                        <span>{{ scope.record.sku_name || '--' }}</span>
                      </a-tooltip>
                    </div>
                    <div class="number-id cellos-item_id">
                      <span>商品ID：</span>
                      <span>{{ data.order_type == 4 ? data.product_code : scope.record.product_code }}</span>
                    </div>
                    <div v-if="data.order_type == 4 && data.wechat_shop_product_id" class="number-id cellos-item_id">
                      <span>小店商品ID：</span> <span>{{ data.wechat_shop_product_id }}</span>
                    </div>
                    <span
                      class="border p-4px pt0 pb0 border-rd-2px border-color-#FFE3D1 color-#FE9653 bg-#FFF7F3 mr8px"
                      v-show="[1, 2].includes(scope.record?.active_type)"
                    >
                      {{ scope.record.active_type === 1 ? '顺手买一件' : '周边推荐' }}
                    </span>
                    <span
                      class="border p-4px pt0 pb0 border-rd-2px font-size-12px border-color-#C4EEE2 color-#19A378 bg-#E8F6F2 mr8px"
                      v-show="[4].includes(data.activity_type)"
                    >
                      送礼物
                    </span>

                    <!-- <span
                      v-if="data.order_type == 2"
                      class="border p-3px pt0 pb0 border-rd-2px border-color-#E6D9FF color-#7C49DC bg-#F8F5FF font-size-12px line-height-14px inline-block"
                    >
                      {{ '全球购' }}
                    </span> -->
                  </div>
                  <div class="">
                    <div class="text-right pb-1">¥ {{ scope.record.price }}</div>
                    <div class="text-right">{{ scope.record.num }}件</div>
                  </div>
                </div>
              </div>
            </template>
            <template v-if="scope.column.key === 'order'">
              <div class="flex flex-col">
                <span
                  class="font-medium"
                  :style="{
                    color: orderType(data.status)?.color
                  }"
                  >{{ orderType(data.status)?.text || '--' }}</span
                >
                <template v-if="data.order_type == 4">
                  <span v-if="data.after_status > 0" class="c-red font-medium">有售后</span>
                  <span class="color-opacity-6" v-else>无售后</span>
                </template>
                <template v-else>
                  <span v-if="[1, 2, 3, 8].includes(data.after_status)" class="c-red font-medium">售后中</span>
                  <span v-else-if="[6, 9, 10].includes(data.after_status)" class="c-#404040 font-medium">
                    <span>售后完成</span>
                    <a-popover v-if="data.after_status_remark">
                      <template #content>
                        <span>{{ data.after_status_remark }}</span>
                      </template>
                      <InfoCircleOutlined style="margin-left: 6px" />
                    </a-popover>
                  </span>
                  <span v-else-if="[7].includes(data.after_status)" class="c-#404040 font-medium">售后关闭</span>
                  <span v-else-if="[12].includes(data.after_status)" class="c-#404040 font-medium">退款失败</span>
                  <span class="color-opacity-6" v-else>无售后</span>
                </template>

                <span style="color: #f2a626" v-if="data.time_out_str && state.actionConfig.value != 9">{{
                  data.time_out_str
                }}</span>
                <div>
                  <span class="item-urge" v-if="[1].includes(data.urge_send)">{{ urgeStatus[data.urge_send] }}</span>
                  <div class="date-urge" v-if="data.urge_time">
                    {{ formatDate(data.urge_time * 1000) }}
                  </div>
                </div>
                <div class="c-#ff4052" v-if="data.time_out_str && state.actionConfig.value == 9">
                  <InfoCircleOutlined class="mr-6px" />
                  <span>发货超时,即将退款</span>
                </div>
              </div>
            </template>

            <template v-if="scope.column.key === 'prices'">
              <div>
                <div>¥ {{ data.money?.toFixed(2) }}</div>

                <div class="flex flex-items-center">
                  <span>微信支付</span
                  ><span class="ml8px c-#f2a626"> {{ data.mch_id ? `（${data.mch_id}）` : '--' }}</span>
                </div>
                <a-tooltip color="#fff" trigger="click" placement="bottom" :getPopupContainer="getPopupContainer">
                  <template #title>
                    <div class="p-8px">
                      <div class="c-#494949">运费：¥ {{ data.freight_money?.toFixed(2) || '0.00' }}</div>
                      <div class="c-#494949" v-if="scope.record.active_type !== 2">
                        优惠：¥ {{ data.discount?.toFixed(2) || '0.00' }} {{ couponSource(data.coupon_source) }}
                      </div>
                      <div v-else class="c-#494949">优惠减：¥0.00</div>
                      <div class="c-#494949" v-if="data.rebate_money > 0">
                        返现：¥ {{ data.rebate_money?.toFixed(2) || '0.00' }}
                      </div>
                      <div class="c-#494949" v-if="data.recall_type == 1">
                        召回立减：¥{{ data.recall_money.toFixed(2) || '0.00' }}
                      </div>
                      <div class="c-#494949" v-if="data.recall_type == 2">
                        降价：¥{{ data.recall_money.toFixed(2) || '0.00' }}
                      </div>
                      <div class="c-#494949" v-if="data.transfer_money > 0">
                        打款：¥ {{ data.transfer_money?.toFixed(2) || '0.00' }}
                      </div>
                    </div>
                  </template>
                  <a-button type="link" class="p-0! h-auto!">查看明细</a-button>
                </a-tooltip>
              </div>
            </template>
            <template v-if="scope.column.key === 'user'">
              <div>
                <div class="wx_info" v-if="data.phone" style="display: flex; align-items: center">
                  <img
                    src="@/assets/images/order/wx_icon.png"
                    style="width: 14px; height: 14px; font-size: 14px; margin-right: 5px"
                  />
                  {{ data.phone }}
                </div>
                <div class="flex flex-items-center">
                  <a-tooltip placement="topLeft">
                    <template #title>{{ data.consignee }}</template>
                    <span class="text_overflow max-w-80px"> {{ data.consignee }}</span>
                  </a-tooltip>
                  <span class="pl-1">
                    {{ data.receive_phone || '--' }}
                  </span>
                  <div v-auth="['shopOrderLookIphone']">
                    <EyeOutlined
                      class="pl-1 cursor-pointer"
                      v-if="data.showPhone === 1"
                      @click="getPhone(data, { type: 1, id: data.order_id, is_show: 0 }, scope.index)"
                    />
                    <SvgIcon
                      v-else
                      :id="`initVerifyCopy${scope.index}`"
                      class="pl-1 cursor-pointer"
                      icon="eye"
                      @click="getPhone(data, { type: 1, id: data.order_id, is_show: 1 })"
                    />
                  </div>
                </div>
                <a-tooltip placement="topLeft">
                  <template #title>{{ data.address }}{{ data.address_detail }}</template>
                  <div class="text_overflow_row2">
                    <div class="user-address">{{ data.address }}{{ data.address_detail }}</div>
                  </div>
                </a-tooltip>

                <!-- v-auth="['addressUpdate']" -->
                <span
                  class="cursor-pointer"
                  :class="[
                    data.wechat_shop_change_address_num >= 5 && data.order_type == 4
                      ? 'c-#00000040 cursor-not-allowed'
                      : 'edit_btn'
                  ]"
                  v-if="data.address && [1, 2].includes(data.status)"
                  @click="handleActions('area', data)"
                  >修改</span
                >
              </div>
            </template>
            <template v-if="scope.column.key === 'pay'">
              <div v-if="data?.express_number">
                <div class="flex flex-items-center">
                  <span>{{ data.express_name }}</span>
                  <span class="parcel_tag ml-6px" v-if="data.logistics_status_name">{{
                    data.logistics_status_name || '--'
                  }}</span>
                </div>
                <div>{{ data.express_number }}</div>
                <div>{{ data.deliver_time != 0 ? formatDate(data.deliver_time * 1000) : '--' }}</div>
                <span class="cu_po" @click="handleActions('lookWl', data)"> 查看物流 </span>
                <!-- 3已签收 301正常签收 302派件异常后最终签收 304待收签收 311快递柜或驿站签收 -->
                <span class="c-#1981ff ml-2" v-if="[3, 301, 302, 304, 311].includes(data.logistics_status)">
                  已签收
                </span>
                <div class="flex_align_center" v-if="data.remind == 2" style="margin-top: 6px">
                  <div class="flex_align_center">
                    <span style="color: #e63030; margin-right: 2px">同步失败</span>
                    <a-tooltip placement="topLeft" popper-class="toolpt">
                      <template #title>{{ data.remind_err_msg || '' }}</template>
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </div>
                  <a-button
                    type="link"
                    v-if="data.remind == 2 && data.showSync"
                    @click="syncOrder(data)"
                    style="margin-left: 20px"
                  >
                    同步
                  </a-button>
                </div>
              </div>
              <div v-else>--</div>
              <!-- <template v-if="data.order_type === 2">
                <div v-if="data.customs_status !== 'CUSTOMS_AUDIT_IM_SUCCESS_CALLBACK'">
                  支付清关:
                  <span :style="{ color: payCustomsState(data.pay_customs_status)?.color }">{{
                    data.pay_customs_status_name || '-'
                  }}</span>
                  <a-tooltip placement="topLeft">
                    <template #title>{{
                      data?.order_customs?.pay_customs_failed_reason || data?.order_customs?.cert_check_result_name
                    }}</template>
                    <ExclamationCircleOutlined
                      class="c-#FE4042 ml4px"
                      v-if="
                        data?.order_customs?.pay_customs_failed_reason || data?.order_customs?.cert_check_result_name
                      "
                    />
                  </a-tooltip>
                </div>
                <div>
                  海关清关:
                  <span :style="{ color: customsState(data.customs_status)?.color }">{{
                    customsState(data.customs_status)?.name
                  }}</span>
                  <a-tooltip placement="topLeft">
                    <template #title>{{ data?.order_customs?.customs_failed_reason || '-' }}</template>
                    <ExclamationCircleOutlined
                      class="c-#FE4042 ml4px"
                      v-if="data?.order_customs?.customs_failed_reason"
                    />
                  </a-tooltip>
                </div>
              </template> -->
            </template>
            <template v-if="scope.column.key === 'action'">
              <div class="flex flex-col">
                <span
                  v-auth="['shopOrderLook']"
                  class="cu_po"
                  @click="
                    router.push({
                      path: '/mall/order/order_detail',
                      query: data?.sub_order_num
                        ? {
                            id: data?.order_id,
                            status: data?.status,
                            active_type: data?.product_list?.[0]?.active_type,
                            sub_order_num: data?.sub_order_num
                          }
                        : {
                            id: data?.order_id,
                            status: data?.status,
                            active_type: data?.product_list?.[0]?.active_type
                          }
                    })
                  "
                  >查看详情</span
                >
                <template v-if="true">
                  <template v-if="![2, 4].includes(data.order_type) && ![7].includes(data?.status)">
                    <span v-auth="['shopOrderERP']" class="cu_po" @click="syncErp('alone', data)">同步ERP</span>
                  </template>
                  <span
                    v-auth="['shopOrderLookSale']"
                    class="cu_po"
                    v-show="[1, 2, 3, 8].includes(data?.after_status)"
                    @click="go_page('sale', data)"
                    >查看售后</span
                  >
                  <span
                    v-auth="['shopOrderListEditPrice']"
                    v-show="[1].includes(data.status) && showEditPrice(data.created_at)"
                    class="cu_po"
                    @click="handleActions('editPrice', data)"
                    >修改金额</span
                  >
                  <span
                    v-auth="['shopOrderListRoker']"
                    style="color: #f2a626"
                    class="cu_po"
                    @click="handleActions('remark', data)"
                    >备注({{ data.shop_remark_count }})</span
                  >

                  <span
                    v-auth="['shopOrderListNoOrder']"
                    class="cu_po"
                    v-show="[1, 2, 3, 7].includes(data?.status) && data.order_type != 4"
                    @click="handleCancelAction('cancelOrder', data)"
                    >取消订单</span
                  >
                  <span
                    class="cu_po"
                    v-show="![7].includes(data?.status) && data.order_type != 4"
                    @click="handleCancelAction('sendMsg', data)"
                    >发送短信</span
                  >
                  <!-- 跨境订单  并且不是(已取消/待付款)  并且 不是海关清关成功或失败 并且不是售后完成 才能展示 -->
                  <!-- <span
                  class="cu_po"
                  @click="submitRowBtn(1, data)"
                  v-if="
                    data?.order_type == 2 &&
                    ![-1, 1].includes(data?.status) &&
                    !['CUSTOMS_AUDIT_IM_SUCCESS_CALLBACK', 'CUSTOMS_AUDIT_IM_FAILURE_CALLBACK'].includes(
                      data?.customs_status
                    ) &&
                    ![6, 9, 10].includes(data?.after_status)
                  "
                  >提交支付申报</span
                > -->
                  <!-- <span class="cu_po" @click="submitRowBtn(2, data)">提交海关清关</span> -->
                  <span
                    v-show="data?.status == 2 && ![2, 4].includes(data.order_type)"
                    v-auth="['shopOrderListFahuo']"
                    class="cu_po cu_btn"
                    @click="handleActions('deliverGoods', data)"
                  >
                    发货
                  </span>
                </template>
              </div>
            </template>
          </template>
          <template #footer="{ scope, data }">
            <div class="table_row_bottom flex flex-items-center" v-if="data.shop_remark || data.user_remark">
              <div class="label">{{ data.shop_remark ? getPlatformInfo(data.remark_type) : '买家备注：' }}</div>
              <div class="max-w-60% text_overflow">
                <a-tooltip placement="topLeft">
                  <template #title>{{ data.shop_remark ? data.shop_remark : data.user_remark }}</template>
                  {{ data.shop_remark ? data.shop_remark : data.user_remark }}
                </a-tooltip>
              </div>
              <div class="flex" v-if="data.shop_remark_url">
                <div
                  v-for="(i, index) in JSON.parse(data.shop_remark_url)"
                  :key="index"
                  class="overflow-hidden border-rd-4px ml-8px"
                  style="width: 30px; height: 30px"
                >
                  <a-image
                    v-if="i.type == 'img' || i.type == 'image'"
                    width="30px"
                    height="30px"
                    :src="i.url"
                  ></a-image>
                  <FilePreview
                    v-if="i.type == 'video'"
                    :src="i.url"
                    :cover="i.cover_image"
                    style="width: 30px; height: 30px"
                  ></FilePreview>
                </div>
              </div>
            </div>
          </template>
        </DescTableLayout>
      </template>
    </DesTablePage>
    <a-modal
      :width="calcWidth"
      centered
      :maskClosable="false"
      :destroyOnClose="true"
      v-model:open="state.modalConfigData.open"
      :title="state.modalConfigData.title"
      @cancel="onClose"
    >
      <template #footer>
        <div class="flex justify-end">
          <div class="mr-10px">
            <a-button
              v-if="['editPrice'].includes(state.modalConfigData.type) && state.modalConfigData.data.order_type != 4"
              type="primary"
              :disabled="state.recallDisable"
              @click="okAndBack"
            >
              <span>确认并召回</span>
              <a-tooltip placement="topLeft">
                <template #title>确认并召回将会通过公众号、订阅消息、短信等场景通知用户。</template>
                <QuestionCircleOutlined />
              </a-tooltip>
            </a-button>
            <div
              class="recall_tips mt-5px"
              v-if="
                state.recallDisable &&
                ['editPrice'].includes(state.modalConfigData.type) &&
                state.modalConfigData.data.order_type != 4
              "
              @click="router.push({ name: 'RecallStatistics', query: { isRecharge: true } })"
            >
              余额不足，无法进行短信召回，<span class="color-red">点击去充值>></span>
            </div>
          </div>
          <a-button @click="modalCancel" v-if="!['checkOrder'].includes(state.modalConfigData.type)">取消</a-button>
          <a-button type="primary" @click="modalOk" v-if="!['checkOrder'].includes(state.modalConfigData.type)"
            >确定</a-button
          >
        </div>
      </template>
      <div
        v-if="['slidingId'].includes(state.modalConfigData.type)"
        id="slidingId"
        style="position: relative; height: 32px; overflow: hidden"
      ></div>
      <Remark
        v-if="['remark'].includes(state.modalConfigData.type)"
        :item="state.modalConfigData.data"
        v-model:data="state.remarkData"
        ref="formRef"
      />
      <CancelOrder
        v-if="['cancelOrder'].includes(state.modalConfigData.type)"
        v-model:data="state.cancelOrderData"
        :order-num="state.modalConfigData.data.order_num"
        :order-status="state.modalConfigData.data.status"
        ref="formRef"
      />
      <DeliverGoods
        v-if="['deliverGoods'].includes(state.modalConfigData.type)"
        v-model:data="state.deliverGoodsData"
        ref="formRef"
        :order-type="state.modalConfigData.data.order_type"
      />
      <LookWl v-if="['lookWl'].includes(state.modalConfigData.type)" :data="state.modalConfigData.data" />
      <EditArea
        v-if="['area'].includes(state.modalConfigData.type)"
        ref="formRef"
        v-model:data="state.areaFormData"
        :order-type="state.modalConfigData.data.order_type"
      />
      <EditPrice
        v-if="['editPrice'].includes(state.modalConfigData.type)"
        :item="state.modalConfigData.data"
        ref="formRef"
      />
      <CheckOrder
        v-if="['checkOrder'].includes(state.modalConfigData.type)"
        :pageSize="state.initParams.page_size"
        :invalidOrderNums="state.invalid_order_nums"
        @close="onClose"
        ref="checkOrder"
        @submit="onSubmit"
      />
      <SendMsg
        v-if="['sendMsg'].includes(state.modalConfigData.type)"
        v-model:data="state.modalConfigData.data"
        ref="formRef"
      />
    </a-modal>
  </div>
</template>
<script setup lang="tsx">
  import DeliverGoods from './components/DeliverGoods.vue'
  import CancelOrder from './components/CancelOrder.vue'
  import DCancelOrder from './dialog/CancelOrder.vue'
  import Remark from './components/Remark.vue'
  import LookWl from './components/LookWl.vue'
  import EditArea from './components/EditArea.vue'
  import ParcelList from '@/views/shop/dataCenter/dataOverview/components/ParcelList.vue'
  import EditPrice from './components/EditPrice.vue'
  import CheckOrder from './components/CheckOrder.vue'
  import SendMsg from './components/SendMsg.vue'
  import { onMounted, reactive, ref, watch, createVNode, onActivated, computed, h, nextTick } from 'vue'
  import { EyeOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
  import SliderVerification from '@/components/ui/common/FunCom/SliderVerification/index'
  import { setAuthUrl, batch_send_msg, getSmsList } from '@/api/common'
  import {
    get_order_list,
    get_order_list_count,
    order_get_order_phone,
    post_order_record_add,
    post_exportdata_create,
    addressUpdate,
    syncWx,
    postSyncErp,
    listLogisticCount,
    order_recall,
    get_order_info,
    getWechatConfAllList,
    checkOrderNumsApi,
    batchRemarkApi,
    replay_pay_customs
  } from './index.api'
  import datas from './data'
  import { secToTime, formatDate, localStg, yuanToCents, checkMobilePermission, convertToImg } from '@/utils'
  import { useRoute, useRouter } from 'vue-router'
  import { message, notification, Modal, Tooltip } from 'ant-design-vue'
  import { get_order_get_express, post_order_cancel, post_order_send } from '@/views/shop/order/order_details/index.api'
  import { QuestionCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { useDownloadCenter, useVerifyCopy, useApp } from '@/hooks'
  import moment from 'moment'
  import { debounce, isArray } from 'lodash-es'
  const { useInfo } = useApp()
  const { goCenter } = useDownloadCenter()
  const {
    ordersource,
    SEARCH_CONFIG_DATA,
    ACTIONS_DATA,
    orderType,
    modalConfigData,
    staticTitle,
    cancelOrderData,
    deliverGoodsData,
    remarkData,
    couponSource,
    iconType,
    channelType,
    // isCallbackType,
    areaFormData,
    columns,
    customsState,
    payCustomsState
  } = datas()
  const { initVerifyCopy, isNvcPass, nvc, isModal, setIsModal } = useVerifyCopy(success, phoneCode)
  const route = useRoute()
  const formRef = ref(null)
  const searchFormDataRef = ref()
  const router = useRouter()
  const state = reactive({
    invalid_order_nums: [],
    indeterminate: false,
    selectTable: [],
    allSelectTable: [],
    selectItem: [],
    yzData: null,
    row: null,
    initParams: {
      page: 1,
      page_size: 10,
      status: route.query?.status || 0,
      after_status: 0,
      un_split_status: 0,
      order_num: '',
      order_nums: '',
      product: route.query?.productId || '',
      sku: '',
      urge_send: 0,
      channel_type: 0,
      order_source: 0,
      consignee: '',
      phone: '',
      pay_num: '',
      ad_id: '',
      ad_account_id: 0,
      express_number: '',
      admin_ids: '',
      created_at: [
        moment().subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
      ].join('_'),
      deliver_time: '',
      success_at: '',
      pay_at: '',
      coupon_type: 0,
      appids: '',
      active_type: ''
    },
    logistics_code: '',
    recallDisable: false,
    signed_for: false,
    urge_send: false,
    searchConfig: SEARCH_CONFIG_DATA,
    modalConfigData,
    remarkData: JSON.parse(JSON.stringify(remarkData)),
    cancelOrderData: JSON.parse(JSON.stringify(cancelOrderData)),
    deliverGoodsData: JSON.parse(JSON.stringify(deliverGoodsData)),
    areaFormData: JSON.parse(JSON.stringify(areaFormData)),
    actionConfig: {
      value: 0,
      list: ACTIONS_DATA
    },
    tableConfig: {
      isLoading: false,
      size: 'small',
      dataSourceKey: 'product_list',
      pagination: false,
      columns,
      list: []
    },
    paginationConfig: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    },
    logistics: [
      // {
      //   logistics_status: 501,
      //   label: '支付单清关失败',
      //   unit_text: '(单)',
      //   num: 0,
      //   desc: '微信支付单清关失败的订单数'
      // },
      // {
      //   logistics_status: 502,
      //   label: '订购人身份异常',
      //   unit_text: '(单)',
      //   num: 0,
      //   desc: '用户实名认证信息填写错误的订单数'
      // },
      // {
      //   logistics_status: 503,
      //   label: '海关待清关',
      //   unit_text: '(单)',
      //   num: 0,
      //   desc: '还未提交海关清关的订单数'
      // },
      // {
      //   logistics_status: 504,
      //   label: '海关清关中',
      //   unit_text: '(单)',
      //   num: 0,
      //   desc: '海关清关中的订单数'
      // },
      // {
      //   logistics_status: 505,
      //   label: '海关拒单',
      //   unit_text: '(单)',
      //   num: 0,
      //   desc: '海关清关审核失败的订单数'
      // },
      { logistics_status: 0, label: '异常总单数', unit_text: '(个)', num: 0, desc: '包含所有异常包裹', children: [] },
      {
        logistics_status: 1401,
        label: '揽收异常',
        unit_text: '(个)',
        num: 0,
        children: [],
        desc: '系统打单或下单后，超过了预设监控时段还未被取走的包裹'
      },
      {
        logistics_status: 1402,
        label: '签收异常',
        unit_text: '(个)',
        num: 0,
        children: [],
        desc: '派件员长期持有包裹或联系不上客户未派送。可能存在丢件或收派网点积压，需要及时跟踪和处理'
      },
      {
        logistics_status: 1403,
        label: '物流停滞',
        unit_text: '(个)',
        num: 0,
        children: [],
        desc: '包裹运输过程中，长时间没有产生下一步物流轨迹。可能存在丢件或派件网点积压，需要及时干预'
      },
      {
        logistics_status: 1405,
        label: '派件异常',
        unit_text: '(个)',
        num: 0,
        children: [],
        desc: '快递员派件过程中，因无法联系、超区件等问题导致派件异常'
      },

      {
        logistics_status: 1404,
        label: '拒收/拦截',
        unit_text: '(个)',
        num: 0,
        children: [],
        desc: '收件人拒绝签收或包裹运输过程中，因拦截而退回的包裹'
      },
      {
        logistics_status: 1406,
        label: '问题件',
        unit_text: '(个)',
        num: 0,
        children: [],
        desc: '其他破损、退货未签收等问题件'
      }
    ],
    logistic_date: ''
  })
  const getPlatformInfo = (type) => {
    const obj = {
      1: '总后台备注：',
      2: '商家后台备注：',
      3: '平台客服备注：',
      4: '商家客服备注：',
      5: '买家备注：'
    }
    return obj[type]
  }

  const calcWidth = computed(() => {
    const { type } = state.modalConfigData
    switch (type) {
      case 'slidingId':
        return 360
      case 'editPrice':
        return 890
      case 'checkOrder':
        return 474
      default:
        return 600
    }
  })
  // 获取包裹异常监控
  const getListLogisticCount = debounce(async () => {
    try {
      const { data } = await listLogisticCount()
      state.logistics.forEach((item) => {
        data.cnt.forEach((items) => {
          if (item.logistics_status == items.code) {
            item.num = items.count
            item.children = items.children
          }
        })
      })
      state.logistic_date = data.last_callback_date || formatDate(new Date().getTime())
    } catch (error) {
      console.error(error)
    }
  }, 500)

  const parcel_tag_filter = (type: any) => {
    if (!type) return false
    const result = state.logistics.find((v: any) => v.logistics_status === type)
    if (result) {
      return result.label
    } else {
      return '-'
    }
  }
  // 获取小程序列表
  const initGetWechatConfAllList = async () => {
    const result = await getWechatConfAllList({ page_size: 1000 })
    if (result.code === 0) {
      ;(state.searchConfig.data || []).forEach((item) => {
        if (item.field === 'appids') {
          item.props.options = (result.data.list || []).map((v) => {
            return {
              value: v.app_id,
              label: v.app_name
            }
          })
        }
      })
    }
  }
  const urgeStatus = reactive({
    1: '已催发货',
    0: '未催发货'
  })

  // 卡片点击事件
  const onParcelEvent = (data: any, it: any) => {
    setTimeout(() => {
      if (data.checked) {
        state.initParams.logistics_status = data.logistics_status || 1
        searchFormDataRef.value.formData.created_at = []
        delete state.initParams.created_at
      } else {
        searchFormDataRef.value.formData.created_at = [
          moment().subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ]
        delete state.initParams.logistics_status
      }
      if (it?.code) {
        state.initParams.logistics_status = it.code
      }
      state.initParams.created_at = searchFormDataRef.value.formData.created_at
      initPageData(state.initParams)
    }, 0)
  }

  async function success(data) {
    try {
      const result = await order_get_order_phone({ ...state.yzData, nvc: data })
      const status = isNvcPass(result, 'slidingId')
      if (status) {
        setIsModal()
        state.tableConfig.list.forEach((item) => {
          if (item.id === state.row.id) {
            item.showPhone = 1
            item.receive_phone = result.data.phone
          }
        })
      }
    } catch (e) {
      console.log(e)
    }
  }
  function phoneCode() {
    SliderVerification({
      handleOk(data) {
        return okphone(data)
      }
    })
  }
  const okphone = async (data) => {
    const result = await order_get_order_phone({ ...state.yzData, captcha: data.code })
    if (result.code === 0) {
      state.tableConfig.list.forEach((item) => {
        if (item.id === state.row.id) {
          item.showPhone = 1
          item.receive_phone = result.data.phone
          localStg.remove('countdown')
        }
      })
    } else {
      message.warning(result.msg)
      return true
    }
  }

  // 搜索
  const changeValue = (data) => {
    if (data.type == 'batchCheckOrder') {
      state.initParams.page = 1
      batchCheckOrder()
      return
    }
    state.initParams.order_nums = ''
    state.initParams = { ...state.initParams, ...data.formData, page: 1 }
    if (data.formData && data.formData.created_at) {
      state.initParams.created_at = data.formData.created_at.join('_')
    }
    // 支付完成时间搜索
    if (data.formData && data.formData.pay_at) {
      state.initParams.pay_at = data.formData.pay_at.join('_')
    }
    if (data.formData && data.formData.deliver_time) {
      state.initParams.deliver_time = data.formData.deliver_time.join('_')
    }
    if (data.formData && data.formData.logistics_fail_time) {
      state.initParams.logistics_fail_time = data.formData.logistics_fail_time.join('_')
    }
    // 退款完成时间
    if (data.formData && data.formData.success_at) {
      state.initParams.success_at = data.formData.success_at.join('_')
    }
    if (data.formData && data.formData.appids?.length > 0) {
      state.initParams.appids = data.formData.appids.join(',')
    }
    if (!data.status) {
      state.initParams.order_nums = ''
      data.formData.created_at = [
        moment().subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
      ]
      searchFormDataRef.value.formData.created_at = [
        moment().subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
      ]
      state.logistics.forEach((v: any) => {
        v.checked = false
      })
      console.log('---', searchFormDataRef.value.formData)
      state.initParams.created_at = searchFormDataRef.value.formData.created_at.join('_')
      delete state.initParams.logistics_status
    }
    const orderSource = data.formData.order_source

    if (orderSource) {
      const lastSource = orderSource[orderSource.length - 1]

      if (orderSource[0] === 123) {
        state.initParams.sms_source = lastSource
        state.initParams.order_source = undefined
        state.initParams.active_type = undefined
        state.initParams.activity_id = undefined
      } else {
        state.initParams.order_source = lastSource
        state.initParams.sms_source = undefined
        state.initParams.activity_id = undefined
        state.initParams.active_type = undefined
      }
    } else {
      // 如果 order_source 不存在，可以根据需要处理
      state.initParams.sms_source = undefined
      state.initParams.order_source = undefined
      state.initParams.activity_id = undefined
      state.initParams.active_type = undefined
    }
    if (state.signed_for) {
      state.initParams.logistics_status = 3
    }
    initPageData(state.initParams)
  }
  // 按钮操作
  const changeBtnType = (data) => {
    state.actionConfig.value = data.value
    state.initParams.status = data.value
    state.initParams.page = 1
    state.initParams.after_status = 0
    if ([7, 9].includes(data.value)) {
      state.initParams.un_split_status = 2
      state.initParams.just_time_out = 1
    } else if (data.value == 8) {
      state.initParams.un_split_status = 1
      state.initParams.just_time_out = undefined
    } else if (data.value == 6) {
      state.initParams.status = 0
      state.initParams.un_split_status = 0
      state.initParams.after_status = 6
      state.initParams.just_time_out = undefined
    } else {
      state.initParams.un_split_status = 0
      state.initParams.just_time_out = undefined
    }
    initPageData(state.initParams)
  }
  // 获取自定义的父容器
  const getPopupContainer = (triggerNode) => {
    // 返回目标父级元素
    return document.getElementById('center')
  }
  //批量查单
  const batchCheckOrder = () => {
    state.initParams.page = 1
    handleActions('checkOrder', { pageSize: state.initParams.page_size })
  }
  //关闭弹窗
  const onClose = () => {
    state.initParams.order_nums = ''
    modalCancel()
  }
  // 批量备注
  const batchRemark = () => {
    if (!state.selectTable.length) {
      message.warning('请选择相关订单进行批量操作')
      return
    }
    handleActions('remark', { order_id: state.selectTable, type: 'batch' })
  }
  const batchSendMsg = () => {
    if (!state.selectTable.length) {
      message.warning('请选择相关订单进行批量操作')
      return
    }

    handleActions('sendMsg', { order_id: state.selectTable, type: 'batch' })
  }
  const checkOrder = ref()
  //提交数据
  const onSubmit = async (data: any) => {
    state.initParams.order_nums = (data && data.join(',')) || ''
    if (data.length == 0) {
      message.warning('请输入正确的订单编号')
      return
    }
    if (data.length > state.initParams.page_size) {
      message.warning(`每次最多可查${state.initParams.page_size}条！`)
      return
    }
    try {
      let params = {
        order_nums: data
      }
      let res = await checkOrderNumsApi(params)
      state.invalid_order_nums = res.data?.invalid_order_nums || []
      if (state.invalid_order_nums.length > 0) {
        message.warning('请输入正确的订单编号')
      }
      if (state.invalid_order_nums.length == 0) {
        modalCancel()
        initPageData(state.initParams)
        checkOrder.value.init()
      }
    } catch (error) {}
  }
  onMounted(() => {
    if (route.query.order_sn) {
      state.initParams.order_num = route.query.order_sn
      state.initParams.created_at = undefined
      nextTick(() => {
        searchFormDataRef.value.formData.created_at = undefined
        searchFormDataRef.value.formData.order_num = route.query?.order_sn
      })
    }
    if (route.query?.sms_source) {
      state.initParams.sms_source = +route.query?.sms_source
    }
    state.searchConfig.data.forEach((item: any) => {
      if (item.field === 'product') {
        item.value = route.query?.productId || undefined
      }
      if (item.field === 'order_source' && route.query?.sms_source) {
        const sms_source = +route.query.sms_source // 提取并转换为数字
        item.value = [1, 2, 3].includes(sms_source) ? [123, sms_source] : [sms_source]
        item.useConfig = false
      }
      if (item.field === 'created_at') {
        if (route.query?.created_at) {
          let dates = route.query.created_at?.split('_')
          item.value = dates?.length
            ? [
                moment(dates[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                moment(dates[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
              ]
            : []
          state.initParams.created_at = dates?.length
            ? [
                moment(dates[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                moment(dates[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
              ]
            : []
        } else if (!route.query?.created_at && route.query?.sms_source) {
          state.initParams.created_at = undefined
          item.value = []
          item.field = undefined
        }
      }
    })
    initPageData(state.initParams)
    initExpress()
    // getListLogisticCount()
    initGetWechatConfAllList()
    if (route.query?.status) {
      changeBtnType({ value: Number(route.query?.status) })
    }
    if (route.query?.logistics_status) {
      state.logistics_code = Number(route.query?.code) || undefined
      let checkedParcel
      state.logistics.forEach((v: any) => {
        if (v.logistics_status === Number(route.query?.logistics_status)) {
          v.checked = true
          checkedParcel = v
        } else {
          v.checked = false
        }
      })
      onParcelEvent(checkedParcel, { code: state.logistics_code })
    }
  })
  onActivated(() => {
    if (route.query?.logistics_status) {
      //组件激活后把页面位置设置为0,页面跳转到顶部
      document.getElementById('scrollbar').scrollTop = 0

      let checkedParcel
      state.logistics.forEach((v: any) => {
        if (v.logistics_status === Number(route.query?.logistics_status)) {
          v.checked = true
          checkedParcel = v
        } else {
          v.checked = false
        }
      })
      onParcelEvent(checkedParcel)
    } else {
      delete state.initParams.logistics_status
    }
  })
  const initExpress = async () => {
    const result = await get_order_get_express()
    if (result.code === 0) {
      state.expressData = result.data
    }
  }
  // 初始化全选数据
  const initSelectAll = (data, key) => {
    const allArr = []
    data && data.forEach((item) => allArr.push(item[key]))
    return allArr
  }
  const initPageData = debounce(async (data) => {
    try {
      if (isArray(state.initParams.created_at)) {
        state.initParams.created_at = state.initParams.created_at.join('_')
      }

      state.tableConfig.isLoading = true
      if (route.query?.status) {
        data.created_at = undefined
        state.searchConfig.data.forEach((item) => {
          if (item.field === 'created_at') {
            item.props.value = undefined
          }
        })
      }

      let [result, resultCount, smsCount] = await Promise.all([
        get_order_list(data),
        get_order_list_count(data),
        getSmsList()
      ])
      // const result = await get_order_list(data)
      // const resultCount = await get_order_list_count(data)
      state.recallDisable = smsCount.data.left_num > 0 ? false : true
      if (result.code === 0 && resultCount.code === 0) {
        state.tableConfig.list = result?.data?.list || []
        state.tableConfig.list = (result?.data?.list || []).map((v) => {
          return {
            ...v,
            showSync: true,
            showPhone: 0
          }
        })
        state.allSelectTable = initSelectAll(state.tableConfig.list, 'id')
        state.paginationConfig.total = result.data?.total_num || 0
        state.paginationConfig.current = result.data?.page || 1

        if (resultCount.data?.count) {
          state.actionConfig.list &&
            state.actionConfig.list.forEach((item) => {
              if ([7, 9].includes(item.value)) {
                item.total = resultCount.data?.count[99] || 0
              } else if (item.value === 8) {
                item.total = resultCount.data?.count[98] || 0
              } else if (item.value === 6) {
                item.total = resultCount.data?.count[97] || 0
              } else if (item.value === 17) {
                item.total = resultCount.data?.count[7] || 0
              } else {
                item.total = resultCount.data?.count[item.value] || 0
              }
            })
        } else {
          state.actionConfig.list.forEach((item) => {
            item.total = 0
          })
        }
      }
      state.indeterminate = false
      state.selectTable = []
      state.selectItem = []
      getListLogisticCount()
    } catch (error) {
      console.error(error)
    } finally {
      state.tableConfig.isLoading = false
    }
  }, 500)
  // 分页
  const changePages = (data) => {
    state.initParams.page = data.page
    state.initParams.page_size = data.pageSize
    state.paginationConfig.pageSize = data.pageSize

    initPageData(state.initParams)
  }
  // 选择每一条数据
  const tabelItemSelect = (checkedValue, data) => {
    console.log(checkedValue, data)
    if (state.selectTable.includes(data.id)) {
      state.selectTable = state.selectTable.filter((item) => item !== data.id)
      state.selectItem = state.selectItem.filter((item) => item.id !== data.id)
    } else {
      state.selectTable.push(data.id)
      state.selectItem.push(data)
    }
    state.indeterminate = !!state.selectTable.length && state.selectTable.length < state.allSelectTable.length
  }
  const showEditPrice = (time) => {
    return moment().diff(moment.unix(time), 'minutes') > 3
  }
  // 全选
  const onCheckAllChange = (e: any) => {
    Object.assign(state, {
      selectTable: e.target.checked ? state.allSelectTable : [],
      selectItem: e.target.checked ? state.tableConfig.list : [],
      indeterminate: false
    })
  }
  const syncErp = async (type: string, row: { order_num: any }) => {
    try {
      if (!state.selectTable.length && !row) {
        message.warning('请选择相关售后订单进行批量操作')
        return
      }

      Modal.confirm({
        title: '提示',
        centered: true,
        content: createVNode('div', {}, '确认同步ERP?'),
        async onOk() {
          try {
            let _order_num
            if (type == 'alone') {
              _order_num = row.order_num
            } else {
              _order_num = state.selectItem.map((item) => item.order_num).join(',')
            }
            await postSyncErp({ order_nums: _order_num })
            await initPageData(state.initParams)
            message.success('操作成功')
          } catch (error) {
            console.error(error)
          } finally {
            Modal.destroyAll()
          }
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {}
  }
  // // 集中处理 操作事件
  // const handleActions = (type, data) => {
  //   if (!state.selectTable.length && !data) {
  //     message.warning('请选择相关售后订单进行批量操作')
  //     return
  //   }
  //   alert('11')
  // }

  // 获取手机号
  const getPhone = async (row, data) => {
    if (data.is_show === 1) {
      await initVerifyCopy()
      state.yzData = { ...data, nvc: nvc.value }
      state.row = row
      const result = await order_get_order_phone(state.yzData)
      const status = isNvcPass(result, 'slidingId')
      if (status) {
        row.showPhone = data.is_show
        row.receive_phone = result.data.phone
      }
    } else {
      const result = await order_get_order_phone({ ...data })
      row.showPhone = data.is_show
      row.receive_phone = result.data.phone
    }
  }
  // 确认并召回
  const okAndBack = async () => {
    try {
      const values = await formRef.value.validateFields()
      if (Number(values.dataSource[0].change_price) == 0) {
        return modalCancel()
      }
      if (values) {
        // if (values) {
        //   return message.warning('当前账户下可用短信余额不足，请先前往短信统计中充值')
        // }
        const params = {
          order_num: values.dataSource[0].order_num,
          type: 1,
          money: yuanToCents(values.dataSource[0].change_price)
        }
        const result = await order_recall(params)
        if (result.code === 0) {
          message.success('操作成功')
          modalCancel()
          initPageData(state.initParams)
        }
      }
    } catch (e) {
      console.log(e)
    }
  }
  // 取消订单确认
  const cancelOrderConfirm = (content: string) => {
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title: '提示',
        centered: true,
        icon: createVNode(ExclamationCircleOutlined),
        content: content,
        onOk() {
          resolve('confirm')
        },
        onCancel() {
          reject('cancel')
        }
      })
    })
  }

  // 取消订单
  const handleCancelAction = async (type: any, data: any) => {
    try {
      if ([1, 2, 3, 8].includes(data.after_status)) return await cancelOrderConfirm('该订单已存在售后!')
      if (data.status === 3) await cancelOrderConfirm('当前订单已发货。取消后订单金额将原路退回。是否要取消？')

      if (
        data.status === 1 &&
        type === 'cancelOrder' &&
        (data.sub_order_num || (!data.sub_order_num && data.product_list[0].active_type))
      ) {
        const result = await get_order_info({
          order_id: data.order_id
        })

        let _product_list = [...result.data.product_list, ...result.data?.extend_info?.product_list]
        if (type === 'cancelOrder' && result.data.extend_info) {
          let modal = await Modal.confirm({
            title: '取消订单',
            width: '500px',
            centered: true,
            closable: true,
            icon: null,
            content: h(DCancelOrder, { product_list: _product_list }),
            async onOk() {
              modal.destroy()
              handleActions(type, data)
            }
          })
        } else {
          handleActions(type, data)
        }
      } else {
        handleActions(type, data)
      }
    } catch (error) {
      console.error(error)
    }
  }
  //提交按钮
  const submitRowBtn = async (type, row) => {
    switch (type) {
      case 1:
        if (row.pay_custom_status === 'PROCESSING') {
          Modal.confirm({
            title: '提示',
            centered: true,
            icon: createVNode(ExclamationCircleOutlined),
            content: '支付单正在海关申报中，确认要重新提交申报么？',
            async onOk() {
              try {
                await replay_pay_customs({ order_num: row.order_num })
                message.success('提交成功')
                initPageData(state.initParams)
              } catch (error) {
                console.error(error)
              }
            }
          })
        } else {
          try {
            await replay_pay_customs({ order_num: row.order_num })
            message.success('提交成功')
            initPageData(state.initParams)
          } catch (error) {
            console.error(error)
          }
        }

        break
      case 2:
        console.log('暂无')

        break

      default:
        break
    }
    console.log(type, row)
  }
  const handleActions = async (type, data) => {
    try {
      if (type == 'checkOrder') {
        state.modalConfigData.open = true
        state.modalConfigData.type = type
        state.modalConfigData.title = staticTitle[type]
        return
      }
      if (type == 'area') {
        if (data.wechat_shop_change_address_num >= 5 && data.order_type == 4) {
          return message.warning('修改地址次数已超限制')
        }
      }

      state.modalConfigData.open = true
      state.modalConfigData.type = type
      state.modalConfigData.data = data
      state.modalConfigData.title = staticTitle[type]
      state.areaFormData = {
        area_ids: data.area_ids?.split(',') || [],
        consignee: data.consignee,
        receive_phone: data.receive_phone,
        address: data.address,
        order_id: data.order_id,
        address_detail: data.address_detail
      }
    } catch (error) {
      console.error(error)
    }
  }

  // 获取d授权链接
  const getAuthUrl = async () => {
    try {
      const res = await setAuthUrl({ scan: 'ACCOUNT_ROLE_TYPE_ADVERTISER' })
      window.open(res.data.url, '_blank')
    } catch (error) {
      console.error(error)
    }
  }

  const syncOrder = async (row) => {
    try {
      let res = await syncWx({ order_id: row.order_id })
      message.success('同步成功')
      row.showSync = false
      initPageData(state.initParams)
    } catch (error) {
      console.log(error)
    }
  }
  const modalCancel = () => {
    state.modalConfigData.open = false
    setTimeout(() => {
      if (['slidingId'].includes(state.modalConfigData.type)) {
        setIsModal()
      }
      if (['remark'].includes(state.modalConfigData.type)) {
        state.remarkData = JSON.parse(JSON.stringify(remarkData))
        initPageData(state.initParams)
      }
      if (['cancelOrder'].includes(state.modalConfigData.type)) {
        state.cancelOrderData = JSON.parse(JSON.stringify(cancelOrderData))
      }
      if (['deliverGoods'].includes(state.modalConfigData.type)) {
        state.deliverGoodsData = JSON.parse(JSON.stringify(deliverGoodsData))
      }
      if (['area'].includes(state.modalConfigData.type)) {
        state.areaFormData = JSON.parse(JSON.stringify(areaFormData))
      }

      state.modalConfigData.data = ''
      state.modalConfigData.type = ''
      state.modalConfigData.title = ''
    }, 300)
  }
  const modalOk = async () => {
    if (['lookWl', 'slidingId'].includes(state.modalConfigData.type)) {
      modalCancel()
    }
    //批量备注
    if (['remark'].includes(state.modalConfigData.type) && state.modalConfigData.data.type == 'batch') {
      const values = await formRef.value.validateFields()
      let order_nums = state.selectItem.map((item) => item.order_num)
      if (values) {
        console.log('90506606060060', state.selectItem, order_nums, state.modalConfigData.data)
        const result = await batchRemarkApi({
          order_nums: order_nums,
          ...values
        })
        if (result.code === 0) {
          modalCancel()
          initPageData(state.initParams)
        }
        state.remarkData.remark = null
        state.remarkData.url = []
        state.modalConfigData.open = false
      }
    }
    //单个备注
    if (['remark'].includes(state.modalConfigData.type) && state.modalConfigData.data?.type != 'batch') {
      const values = await formRef.value.validateFields()
      if (values) {
        let list =
          values.url?.map((v) => {
            if (v?.fileInfo?.type == 'video') {
              return {
                url: v.url,
                type: v?.fileInfo?.type,
                cover_image: convertToImg(v.url)
              }
            } else return { url: v.url, type: 'image' }
          }) || []

        const result = await post_order_record_add({
          order_id: state.modalConfigData.data.order_id,
          ...values,
          url: JSON.stringify(list)
        })
        if (result.code === 0) {
          initPageData(state.initParams)
          // modalCancel()
          formRef.value.getRemarkList()
        }
        state.remarkData.remark = null
        state.remarkData.url = []
        state.modalConfigData.open = false
      }
    }
    if (['editPrice'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.validateFields()
      if (Number(values.dataSource[0].change_price) == 0) {
        return modalCancel()
      }
      if (values) {
        const params = {
          order_num: values.dataSource[0].order_num,
          type: 2,
          money: yuanToCents(values.dataSource[0].change_price)
        }
        const result = await order_recall(params)
        if (result.code === 0) {
          message.success('操作成功')
          initPageData(state.initParams)
          modalCancel()
        }
      }
    }
    if (['cancelOrder'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.validateFields()
      if (values) {
        const result = await post_order_cancel({
          order_id: state.modalConfigData.data.order_id,
          ...values
        })
        if (result.code === 0) {
          message.success('操作成功')
          initPageData(state.initParams)
          modalCancel()
        }
      }
    }
    if (['sendMsg'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.validateFields()
      console.log(values, 'valuesvalues')

      let order_nums =
        state.modalConfigData.data.type == 'batch'
          ? state.selectItem.filter((item) => item.order_type != 4).map((item) => item.order_num)
          : [state.modalConfigData.data.order_num]
      console.log(values, 'valuesvalues')

      if (values) {
        if (values.content.includes('{立减金额}')) {
          return message.warning('该模板内包含有【立减金额】，请先确认【立减金额】再选用模板')
        }

        const result = await batch_send_msg({
          order_sn_list: order_nums,
          sms_temp_id: values.sms_temp_id
        })
        if (result.code === 0) {
          message.success('操作成功')
          initPageData(state.initParams)
          modalCancel()
        }
      }
    }
    if (['deliverGoods'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.validateFields()
      if (values) {
        const express_name = formRef.value.getExpressName()
        const result = await post_order_send({
          order_id: state.modalConfigData.data.order_id,
          ...values,
          express: express_name
        })
        if (result.code === 0) {
          message.success('操作成功')
          await initPageData(state.initParams)
          await modalCancel()
        }
      }
    }
    if (['area'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.validateFields()
      console.log('--=-0', values, state.areaFormData)
      values.receive_phone = values.receive_phone.indexOf('*') > -1 ? '' : values.receive_phone
      if (values) {
        let params = {
          order_id: state.modalConfigData.data.order_id,
          ...values,
          area_id: Number(values.area_ids[2]),
          area_ids: values.area_ids.join(','),
          address_detail: state.areaFormData.address_detail
        }
        const result = await addressUpdate(params)
        if (result.code === 0) {
          message.success('修改成功')
          await initPageData(state.initParams)
          await modalCancel()
        }
      }
    }
    state.selectTable = []
    state.selectItem = []
    state.indeterminate = false
  }

  const go_page = (type, data) => {
    if (['sale'].includes(type)) {
      let query = { order_sn: data.order_num }
      router.push({
        name: 'ShopSalesQuery',
        query: query
      })
    }
    if (['load'].includes(type)) {
      notification.close('loaditem')
      const href = router.resolve({
        name: 'DownloadCenter'
      })
      window.open(href.href, '_blank')
    }
  }
  const downloadOrder = async () => {
    // 没有对应权限且点击了取消
    if ((await checkMobilePermission('shopOrderLookIphone')) == 1) return
    for (let key in state.initParams) {
      if (state.initParams[key] == null) {
        state.initParams[key] = undefined
      }
    }
    state.initParams.admin_ids = (state.initParams.admin_ids || '').toString()
    state.initParams.ad_account_id = Number(state.initParams.ad_account_id || 0)
    state.initParams.active_type = Number(state.initParams.active_type || 0)

    const result = await post_exportdata_create({
      params: JSON.stringify(state.initParams),
      type: 'order'
    })
    if (result.code === 0) {
      goCenter('DownloadCenter', 'DownloadCenter')
    }
  }
  const changeSignedFor = (event) => {
    const val = event.target.checked ? 3 : ''
    state.initParams.logistics_status = val
    initPageData(state.initParams)
  }
  const changeUrgeSend = (event) => {
    console.log(event.target, 'event.target')

    const val = event.target.checked ? 1 : 0
    state.initParams.urge_send = val
    console.log(state.initParams.urge_send, 'state.initParams.urge_send')

    initPageData(state.initParams)
  }

  watch(
    () => isModal.value,
    (val) => {
      if (val) {
        state.modalConfigData.open = true
        state.modalConfigData.title = '验证'
        state.modalConfigData.type = 'slidingId'
        setTimeout(() => {
          window.nvc.getNC({
            renderTo: 'slidingId'
          })
        }, 100)
      } else {
        modalCancel()
      }
    }
  )
</script>
<style scoped lang="scss">
  @import './src/assets/css/mixin_scss_fn.scss';
  .hydPage {
    :deep(.ant-card-body) {
      padding-top: 16px;
    }
  }
  .parcel.hyd {
    .parcel-title {
      font-weight: 400;
    }
  }
  .parcel {
    .parcel-title {
      line-height: 14px;
      font-size: 14px;
      font-weight: 500;
      color: #080f1e;
      line-height: 14px;
    }
    .tips {
      align-items: center;
      padding: 6px 8px;
      background: #f9f9fa;
      border-radius: 2px;
      margin-left: 8px;
      .tip {
        width: 14px;
        height: 12px;
      }
    }
    .hyd.tips {
      background: #fff;
      box-shadow: 0px 2px 4px 0px rgba(142, 116, 98, 0.18);
    }
  }
  .parcel_tag {
    display: inline-block;
    line-height: 16px;
    background: rgba(211, 13, 13, 0.14);
    border-radius: 2px;
    font-weight: 400;
    font-size: 12px;
    color: #d30d0d;
    padding: 0 4px;
  }
  .edit_btn {
    color: var(--primary-color);
  }
  .icon {
    width: 15px;
    height: 15px;
    margin-right: 5px;
  }
  .mini_program_logo {
    img {
      width: 12px;
      height: 12px;
      display: block;
      margin-right: 5px;
      margin-bottom: 1px;
    }
  }
  .table_row_bottom {
    // height: 52px;
    // line-height: 52px;
    // background: #f3f6fc;
    // padding-left: 20px;
    font-size: 14px;
    color: #404040;
    display: flex;
    // border: 1px solid #eef0f3;
    border-top: none;

    .label {
      color: #404040;
      width: fit-content;
      flex-shrink: 0;
    }
  }
  img {
    display: block;
    width: 100%;
    height: 100%;
  }
  .card-warp-dex {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .action-warp {
    margin-top: 5px;
    padding: 8px 16px;
  }
  .desc-table-layout-list-item-desc {
    background-color: rgba(36, 47, 87, 0.05);
    padding: 8px 16px;
  }
  .user-address {
    @include text_overflow(2);
  }
  .cellos-item_title {
    @include set_font_config(--font-size-large, --text-color-base);
    @include text_overflow(2);
  }
  .cellos-item-prod-warp {
    box-sizing: border-box;
    padding: 0 8px;
  }
  .cellos-item_img {
    @include set_node_whb(70px, 70px);
  }
  .cellos-item_style {
    @include set_font_config(--font-size-tiny, --text-color-gray);
    @include text_overflow(1);
    margin: 4px 0px;
    box-sizing: border-box;
  }
  .cellos-item_id {
    @include set_font_config(--font-size-tiny, --text-color-gray);
    @include text_overflow(1);
  }
  .cellos-item_mask {
    @include set_font_config(--font-size-tiny, --primary-color);
  }
  .cu_po {
    color: var(--primary-color);
    cursor: pointer;
  }
  .c-red {
    color: #b70000;
  }
  .c-primary {
    color: var(--primary-color);
  }
  .item-urge {
    background: #e37c3f;
    border-radius: 3px;
    padding: 2px 4px;
    margin-top: 10px;
    display: inline-block;
    font-size: 12px;
    color: #fff;
  }
  .recall_tips {
    cursor: pointer;
    color: rgb(139, 145, 163);
  }
  :deep(.file_container .svg_play) {
    width: 16px;
    height: 16px;
  }
</style>
