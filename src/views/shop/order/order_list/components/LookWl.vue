<template>
  <div>
    <div class="message-warp">
      <div class="item-text-warp" v-if="state.materialFlowData?.StateExDesc">
        <span class="title">{{ state.materialFlowData?.StateExDesc }}</span
        ><span>请联系快递公司或者买家核实处理</span>
      </div>
      <div class="item-text-warp">
        <span>快递公司: </span><span>{{ data?.express_name }}</span>
      </div>
      <div class="item-text-warp">
        <span>快递单号: </span><span>{{ data?.express_number }}</span>
      </div>
    </div>
    <div class="list-warp">
      <!-- <template> -->
      <a-timeline>
        <a-timeline-item v-for="item in state.materialFlowData?.logisticsTraceDetails">
          <!-- <span v-if="item.time != 0">{{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}</span>
            <span v-if="(!item.time || item.time == 0) && data.deliver_time != 0">{{
              secToTime(data.deliver_time)
            }}</span>
            <span> {{ item.desc }}</span> -->
          <span>{{ item.AcceptTime }}</span>
          <span class="ml-10px">{{ item.AcceptStation }}</span>
        </a-timeline-item>
        <!-- <a-timeline-item v-for="item in state.materialFlowData?.customs_detail">
                <span>{{ item.time }}</span>
                <span class="ml-10px">{{ item.message }}</span>
              </a-timeline-item> -->
        <a-timeline-item>
          <span v-if="data?.pay_time != 0">{{ secToTime(data?.pay_time) }}</span>
          <span class="ml-10px">您的订单已进入第三方卖家仓库，准备出库中</span>
          <!-- <span class="ml-10px" v-else>您的订单保税仓已接单，仓库正在准备清关发货</span> -->
        </a-timeline-item>
        <a-timeline-item>
          <span>{{ secToTime(data?.created_at) }}</span>
          <span class="ml-10px">您提交了订单，等待卖家仓库发货</span>
        </a-timeline-item>
      </a-timeline>
      <!-- </template> -->
      <!-- <span v-else class="desc-card-no-text">暂无物流数据</span> -->
    </div>
  </div>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs'
  import { onMounted, reactive } from 'vue'
  import { get_order_express } from '@/views/shop/order/order_details/index.api'
  import { secToTime } from '@/utils'
  import datas from '@/views/shop/order/order_list/data.ts'
  const { logisticsStatusEnums } = datas()

  const props = defineProps(['data'])
  const state = reactive({
    materialFlowData: null
  })
  onMounted(() => {
    initMaterialFlowData()
  })
  const initMaterialFlowData = async () => {
    const result = await get_order_express({
      express_num: props.data.express_number,
      phone: props.data.receive_phone,
      order_id: props.data.order_id
    })
    if (result.code === 0) {
      state.materialFlowData = {
        ...result.data,
        logisticsTraceDetails: (result.data?.Traces || []).reverse(),
        customs_detail: (result.data?.customs_detail || []).reverse()
      }
    }
  }
</script>

<style scoped lang="scss">
  .item-text-warp {
    line-height: 2;
    span {
      padding-right: 10px;
    }
  }
  .title {
    font-size: var(--font-size-huge);
    color: var(--primary-color);
  }
  .list-warp {
    height: 200px;
    padding: 20px 0;
    overflow: auto;
  }
</style>
