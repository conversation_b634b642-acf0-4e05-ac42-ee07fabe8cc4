<template>
  <div class="notify-modal-wrapper">
    <a-form
      class="notify-form-wrapper"
      :model="state.form"
      ref="formRef"
      :labelCol="{ style: 'width: 70px' }"
      autocomplete="off"
    >
      <a-form-item
        label="模板名称"
        name="sms_temp_id"
        :rules="{ required: true, message: '请选择模板名称', trigger: ['change', 'blur'] }"
      >
        <a-select
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          v-model:value="state.form.sms_temp_id"
          :options="state.tempList"
          @change="onChangeTemp"
          placeholder="请选择模板名称"
        ></a-select>
      </a-form-item>
      <a-form-item label="描述" name="remark">
        <a-textarea
          class="flex-1"
          placeholder=""
          v-model:value="state.form.remark"
          disabled
          :rows="5"
          :maxlength="200"
        />
      </a-form-item>

      <a-form-item label="短信内容" name="content">
        <a-textarea
          disabled
          id="textarea"
          ref="text_content"
          class="flex-1"
          v-model:value="state.form.content"
          :rows="5"
          :maxlength="200"
        />
      </a-form-item>
      <a-form-item label="" name="has_recall_money" class="!h-0"> </a-form-item>
    </a-form>
    <!-- <div class="fooler_btn">
      <div>
        <AButton :mr="30" @click="close">取消</AButton>
        <AButton
          type="primary"
          @click="submitForm"
          :loading="state.loading"
          :disabled="props.item && state.form.scope === 3"
          >保存</AButton
        >
      </div>
    </div> -->
  </div>
</template>
<script setup lang="ts">
  import { get_temp_list } from '@/views/shop/shopMarketing/msgTemp/index.api'
  import { message } from 'ant-design-vue'
  import { ref, reactive } from 'vue'
  const emit = defineEmits(['event'])
  const props = defineProps(['data'])
  const formRef = ref<any>()
  const state = reactive<any>({
    form: {
      name: undefined,
      remark: undefined,
      content: undefined
    },
    tempList: [],
    loading: false
  })

  const validateFields = async () => {
    return await formRef.value.validateFields()
  }
  const onChangeTemp = () => {
    const currentIndex = state.tempList.findIndex((temp) => temp.id === state.form.sms_temp_id)
    state.form.content = state.tempList[currentIndex].sms_content
    state.form.remark = state.tempList[currentIndex].sms_description
    state.form.has_recall_money = state.tempList[currentIndex].has_recall_money
  }
  const getTempList = async () => {
    try {
      let params = {
        sms_status: 2,
        page: 1,
        page_size: 1000
      }
      const res = await get_temp_list(params)
      state.tempList = (res.data.list || []).map((item) => {
        return {
          ...item,
          label: item.sms_temp_name,
          value: item.id
        }
      })
    } catch (error) {}
  }
  getTempList()
  defineExpose({
    validateFields
  })
</script>
<style lang="scss" scoped>
  .notify-modal-wrapper {
    margin-right: -20px;
    .notify-form-wrapper {
      max-height: 500px;
      overflow: auto;
      padding-right: 20px;
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        /**/
      }
      &::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 2px;
      }
      &::-webkit-scrollbar-thumb {
        background: #c5c5c5;
        border-radius: 10px;
        display: block;
        padding-left: 30px;
      }
    }
    .form-item.no-mb {
      margin-bottom: 0;
    }

    .fooler_btn {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 12px;
      margin-right: 20px;
    }

    .btn_list {
      display: flex;
      flex-wrap: wrap;

      .ant-btn {
        padding: 4px 8px;
        font-size: 12px;
        line-height: 12px;
        height: auto;
        .anticon {
          padding: 0;
        }
        &.content_btn {
          border: 1px solid #edf2ff;
          color: #1677ff;
          margin-right: 8px;
          margin-bottom: 12px;
        }
      }
    }
  }
</style>
