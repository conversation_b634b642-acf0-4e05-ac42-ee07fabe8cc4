<template>
  <div>
    <DesTablePage :pagination="state.paginationConfig" @changePages="changePages">
      <template #title>
        <div>售后查询</div>
      </template>
      <template #extra>
        <a-button
          v-auth="['shopSaleNewSale']"
          type="primary"
          @click="router.push({ path: '/mall/order/sale_form_create' })"
        >
          新建售后单
        </a-button>
        <a-button v-auth="['shopSaleExport']" @click="downloadSales"> 导出 </a-button>
      </template>
      <template #search>
        <SearchBaseLayout
          :data="state.searchConfig.data"
          @changeValue="changeValue"
          :actions="state.searchConfig.options"
          ref="searchFormDataRef"
        >
          <template #innerBtns> </template>
        </SearchBaseLayout>
      </template>
      <template #action>
        <div class="flex-y-center justify-between">
          <a-radio-group
            :value="state.actionConfig.value"
            @change="changeBtnType({ value: $event.target.value })"
            :class="['mt16px', state.actionConfig.value === 'time_out' && 'radioGroup']"
          >
            <a-radio-button
              :class="item.value === 'time_out' && 'warn'"
              v-for="item in state.actionConfig.list"
              :key="item.value"
              :value="item.value"
            >
              <a-space>
                <span> {{ item.label }} ({{ item.total }})</span>
                <a-tooltip v-if="item.value === 'time_out'">
                  <template #title>{{ '用户申请售后在规定时间内未进行处理，系统将自动处理售后。' }}</template>
                  <QuestionCircleOutlined class="m-l-4px font-size-14px" />
                </a-tooltip>
              </a-space>
            </a-radio-button>
          </a-radio-group>
          <div class="flex-y-center">
            <a-checkbox
              :class="['mt16px mr-10px', state.actionConfig.value === 'time_out' && 'radioGroup']"
              v-model:checked="state.refund_money_type"
              @change="changeOffline_refund"
            >
              <span class="w-60px inline-block">线下退款</span>
            </a-checkbox>
            <a-checkbox
              :class="['mt16px', state.actionConfig.value === 'time_out' && 'radioGroup']"
              v-model:checked="state.review_status"
              @change="changeReview_status"
            >
              <span class="w-60px inline-block">平台介入</span>
            </a-checkbox>
          </div>
        </div>
      </template>
      <template #tableWarp>
        <DescTableLayout :data="state.tableConfig">
          <template #action>
            <div class="action-warp flex flex-justify-between">
              <a-space align="center">
                <a-checkbox
                  :indeterminate="state.indeterminate"
                  :checked="state.allSelectTable.length && state.selectTable.length === state.allSelectTable.length"
                  @change="onCheckAllChange"
                >
                  当页全选
                </a-checkbox>
                <a-button
                  v-show="state.actionConfig.value == 'only_return_money'"
                  state.actionConfig.value
                  v-auth="['shopSalePiLianYes']"
                  @click="handleActions('refund')"
                  >批量同意仅退款</a-button
                >
                <a-button
                  v-show="state.actionConfig.value == 'return_goods_money'"
                  v-auth="['shopSalePiLianTuihuoTuiK']"
                  @click="handleActions('refundReturn')"
                  >批量同意退货退款</a-button
                >
                <a-button
                  v-show="state.actionConfig.value == 'wait_shop_deliver_goods'"
                  v-auth="['batchShopSaleYelHuo']"
                  @click="handleActions('confirmGoods')"
                  >批量确认收货</a-button
                >
                <a-button v-auth="['saleBatchRemark']" @click="batchRemark()">批量备注</a-button>
                <a-button @click="batchSendMsg()">批量发送短信</a-button>
                <a-button v-auth="['saleBatchCheckOrder']" @click="batchCheckOrder('batchCheckOrder')"
                  >批量查询售后单</a-button
                >
                <a-button v-auth="['saleBatchOrder']" @click="batchCheckOrder('batchOrder')">批量查询订单</a-button>
              </a-space>
              <!-- <a-space align="center">
              <a-button type="link" @click="handleSort">
                {{ state.initParams.apply_time_sort === 'desc' ? '最近申请排序' : '临近逾期排序' }} <DownOutlined
              /></a-button>
            </a-space> -->
            </div>
          </template>
          <template #desc="{ data }">
            <div class="desc-table-layout-list-item-desc flex flex-justify-between">
              <a-space>
                <span
                  ><a-checkbox
                    :checked="state.selectTable.includes(data.id)"
                    @change="(checkedValue) => tabelItemSelect(checkedValue, data)"
                  >
                  </a-checkbox
                ></span>
                <span class="mini_program_logo" v-if="data.order_type == 4">
                  <img src="@/assets/images/order/werxin_shop.png" alt="微信小店" />
                </span>
                <span v-if="data.order_type == 4">{{ data.wechat_shop_name || '--' }}</span>
                <span class="mini_program_logo">
                  <img v-if="data.page_type === 2" src="@/assets/images/order/icon_h5.png" alt="h5来源" />
                  <img v-else src="@/assets/images/order/mini_program.png" alt="小程序来源" />
                </span>

                <span> {{ data.app_name }}</span>
                <span>售后编号: {{ data.refund_sn }}</span>
                <span>订单编号: {{ data.order_sn }}</span>
                <span class="flex flex-items-center">
                  <span>来源: </span>

                  <a-tooltip placement="topLeft">
                    <template #title>{{ channelType(data.channel_type) }}</template>
                    <img
                      v-if="[1, 4, 5, 6, 7, 8].includes(data.channel_type)"
                      class="icon m-l-4px"
                      :src="iconType(data.channel_type)"
                      alt=""
                    />
                  </a-tooltip>
                  <span>{{ ordersource(data.order_source) }}</span>
                  <span v-if="![1, 4].includes(data.order_source)"
                    >（计划ID：{{ data.ad_group_id == 0 ? '--' : data.ad_group_id }}
                    <span style="width: 5px; display: inline-block"></span> 创意ID：{{ data.creative_id || '--' }}
                    <!-- <a-tooltip>
                        <template #title
                          >广告账户授权异常，为避免报表数据错误，请点击
                          <span class="cursor-pointer c-#97DEFF" @click="getAuthUrl">去授权</span></template
                        >
                        <ExclamationCircleOutlined
                          v-if="data.is_authorized == 1 && data.channel_type == 1"
                          class="c-#e63030 ml-10px mr-5px"
                        /> </a-tooltip> -->
                    ）</span
                  >
                </span>
              </a-space>
              <span>支付时间: {{ data.pay_time }}</span>
            </div>
          </template>

          <template #bodyCell="{ scope, data, pindex }">
            <template v-if="scope.column.key === 'name'">
              <div class="flex">
                <div class="cellos-item_img"><img :src="scope.record.image" alt="" /></div>
                <div class="flex" style="flex: 1; overflow: hidden">
                  <div class="flex-1 cellos-item-prod-warp">
                    <a-tooltip placement="topLeft">
                      <template #title>{{ scope.record.product_name }}</template>
                      <div class="cellos-item_title">
                        {{ scope.record.product_name }}
                      </div>
                    </a-tooltip>

                    <div class="cellos-item_style">
                      <a-tooltip placement="topLeft">
                        <template #title>{{ scope.record.sku_name || '--' }}</template>
                        <span>{{ scope.record.sku_name || '--' }}</span>
                      </a-tooltip>
                    </div>
                    <div class="number-id cellos-item_id">
                      <span>商品ID：</span> <span>{{ scope.record.product_code }}</span>
                    </div>
                    <span class="item-lable" v-if="scope.record.active_type_name && scope.record?.activity_type != 4">{{
                      scope.record.active_type_name
                    }}</span>
                    <span
                      class="border p-4px pt0 pb0 border-rd-2px font-size-12px border-color-#C4EEE2 color-#19A378 bg-#E8F6F2 mr8px"
                      v-show="[4].includes(scope.record?.activity_type)"
                    >
                      送礼物
                    </span>

                    <!-- <span
                      v-if="scope.record.order_type == 2"
                      class="border p-3px pt0 pb0 border-rd-2px border-color-#E6D9FF color-#7C49DC bg-#F8F5FF font-size-12px line-height-14px inline-block"
                    >
                      {{ '全球购' }}
                    </span> -->
                  </div>
                </div>
              </div>
            </template>
            <template v-if="scope.column.key === 'price'">
              <div>
                <div>退款：¥ {{ data.refund_money?.toFixed(2) || '0.00' }}</div>
                <div>实收：¥ {{ data.total_money?.toFixed(2) || '0.00' }}</div>
                <div v-if="data.transfer_money > 0">打款：¥ {{ data.transfer_money?.toFixed(2) || '0.00' }}</div>
              </div>
            </template>
            <template v-if="scope.column.key === 'address'">
              <div>
                <div class="wx_info" style="display: flex; align-items: center">
                  <img
                    src="@/assets/images/order/wx_icon.png"
                    style="width: 14px; height: 14px; font-size: 14px; margin-right: 5px"
                  />
                  {{ data.user_phone }}
                </div>
                <div class="flex flex-items-center">
                  <a-tooltip :title="data.receive_name">
                    <span class="text_overflow max-w-80px">
                      {{ data.receive_name }}
                    </span>
                  </a-tooltip>

                  <span class="pl-1">
                    {{ data.receive_phone || '--' }}
                  </span>
                  <DataEncipher
                    v-if="isAuth(['shopSaleLookIphone'])"
                    v-model:data="state.tableConfig.list[pindex]"
                    showKey="showPhone"
                    goalkey="receive_phone"
                    :type="1"
                    argKey="order_id"
                  />
                </div>
                <div>
                  <a-tooltip>
                    <template #title>{{ data.receive_address || '--' }}</template>
                    <p class="address_info">收货地址：{{ data.receive_address || '--' }}</p>
                  </a-tooltip>
                </div>
              </div>
            </template>

            <template v-if="scope.column.key === 'info'">
              <a-space class="w150px" direction="vertical">
                <div class=" " v-if="!data.deliver_time">未发货</div>
                <div class="colorE6 cursor-pointer c-#e63030" v-else @click="handleActions('deliverGoods', data)">
                  {{ data.user_name + '已发货>' }}
                </div>
                <div v-if="![1, 4].includes(data.after_sale_type)">
                  <div class=" " v-if="!data.logistics_number">未退货</div>
                  <div
                    class="colorE6 c-#e63030 cursor-pointer line1 w150px"
                    v-else
                    @click="handleActions('returnGoods', data)"
                  >
                    {{ '已退货>' }}
                    <span
                      class="refund"
                      v-if="[3, 211, 301, 302, 304, 311, 406].includes(data.refund_logistics_status)"
                      >{{ '退货已签收' }}</span
                    >
                  </div>
                  <!-- 跨境订单 -->
                  <!-- <div class="colorE6">{{ data.bonded_manager_name }}</div> -->
                </div>
              </a-space>
            </template>
            <template v-if="scope.column.key === 'wuliu_info'">
              <div style="width: 200px">
                <a-row>
                  <a-col flex="60px">物流公司:</a-col>
                  <a-col flex="140px">{{ data.logistics_company || '--' }}</a-col>
                </a-row>
                <a-row>
                  <a-col flex="60px">物流单号:</a-col>
                  <a-col flex="140px">{{ data.logistics_number || '--' }}</a-col>
                </a-row>
              </div>
            </template>
            <template v-if="scope.column.key === 'sale_info'">
              <div>
                <div class="w200px">{{ data.msg || data.msg_name || '--' }}</div>
                <a-tooltip placement="top" :title="data.reason">
                  <div class="w200px reason">{{ data.reason || '--' }}</div>
                </a-tooltip>

                <div v-if="data.images" class="flex reasonimg">
                  <img
                    class="image mr2"
                    v-for="(item, index) in data.images.split(',')"
                    :key="index"
                    :src="item"
                    alt=""
                  />
                </div>
              </div>
            </template>
            <template v-if="scope.column.key === 'sale_status'">
              <div class="other_style">
                <div
                  :id="pindex == 0 ? 'afterSale' : ''"
                  :ref="pindex == 0 ? 'afterSale' : ''"
                  :class="pindex == 0 && open ? 'other_sale' : ''"
                >
                  <span class="sale_type">
                    {{ salesStatus(data.after_sale_type) }}
                  </span>
                </div>

                <div class="mt-8px">
                  <span style="font-size: 14px; font-family: PingFang SC; font-weight: 500; color: #e77316">{{
                    data.after_sale_status_name
                  }}</span
                  ><span v-if="data.review_status == 1 || data.review_status == 2" class="platform-intervene ml-8px">{{
                    data.review_status == 1 ? '平台介入中' : '平台介入完成'
                  }}</span>
                  <a-tooltip>
                    <template #title>{{ data.refuse_fail || '--' }}</template>
                    <QuestionCircleFilled v-if="data.after_sale_status == 12" class="m-l-8px" />
                  </a-tooltip>
                  <span
                    v-if="data.platform_handle === 1"
                    class="ml-8px"
                    style="font-size: 14px; font-family: PingFang SC; font-weight: 400; color: #999999"
                    >(平台操作退款)</span
                  >
                </div>
                <!-- <span>卖家处理超时，系统自动退款</span> -->
              </div>
              <div class="number-id" v-if="data.after_status_remark">{{ data.after_status_remark }}</div>
              <!-- <span>only_return_money return_goods_money wait_shop_deliver_goods time_out</span>
              <span>1 3</span> -->
              <div v-if="data.order_type != 4">
                <countDown
                  :key="data.id"
                  v-if="[1, 3].includes(data.after_sale_status) && data.next_operate_time"
                  :target-time="data.next_operate_time + ''"
                ></countDown>
              </div>
              <div v-else>
                <countDown
                  :key="data.id"
                  v-if="[1, 3, 4, 12].includes(data.after_sale_status) && lessOneDay(data.next_operate_time)"
                  :target-time="data.next_operate_time + ''"
                />
              </div>

              <div>{{ customsState(data.customs_status) }}</div>

              <div class="offine-refund flex" v-if="data?.refund_money_type === 2">
                <div class="offine-refund-box">线下退款</div>
                <a-button type="link" class="p-0! h-auto" @click="handleNewActions('lookOffineRefund', data)"
                  >查看凭证</a-button
                >
              </div>
            </template>
            <template v-if="scope.column.key === 'apply_time'">
              <a-space direction="vertical">
                <span>{{ data.apply_time || '--' }}</span>
                <span>{{ data.success_at || '--' }}</span>
              </a-space>
            </template>
            <template v-if="scope.column.key === 'action'">
              <a-space direction="vertical" align="start" class="action-group">
                <a-button v-auth="['shopSaleLookDetail']" type="link" size="small" @click="toPath(data)"
                  >查看详情</a-button
                >
                <template v-if="data.after_sale_complaint">
                  <a-button
                    v-auth="['shopRelatedComplaints']"
                    type="link"
                    size="small"
                    @click="handleActions('relatedComplaints', data)"
                    >关联投诉</a-button
                  >
                </template>
                <template v-if="[2].includes(data.after_sale_status) && data.order_type != 4">
                  <a-button
                    v-auth="['shopSaleEditWuliu']"
                    type="link"
                    size="small"
                    link
                    :class="[data?.transfering && 'disabled-btn']"
                    @click="handleActions('sendGoods', data)"
                  >
                    填写退货物流
                  </a-button>
                </template>
                <template v-if="data.after_sale_status === 12">
                  <a-button
                    :class="[data?.transfering && 'disabled-btn']"
                    type="link"
                    size="small"
                    v-auth="['shopSaleYesTuiKuang']"
                    @click="handleNewActions('refundAgain', data)"
                  >
                    再次退款
                  </a-button>
                </template>
                <div v-if="[12].includes(data.after_sale_status) && data.order_type == 4">
                  <a-button type="link" size="small" link @click="handleActions('refundVoucher', data)">
                    上传退款凭证
                  </a-button>
                </div>
                <div
                  v-if="[1, 3].includes(data.after_sale_status) && data.order_type == 4 && data.after_sale_type != 3"
                >
                  <a-button
                    v-auth="['shopSaleNegotiate']"
                    type="link"
                    size="small"
                    link
                    @click="handleActions('Negotiate', data)"
                  >
                    协商
                  </a-button>
                </div>

                <template v-if="[3].includes(data.after_sale_status) && data.order_type != 4">
                  <a-button
                    v-auth="['shopSaleEditWuliu']"
                    type="link"
                    size="small"
                    :class="[data?.transfering && 'disabled-btn']"
                    @click="handleActions('editSendGoods', data)"
                  >
                    修改退货物流
                  </a-button>
                </template>

                <template
                  v-if="
                    (data.after_sale_status == 3 && data.order_type != 4) ||
                    (data.after_sale_type == 2 && data.after_sale_status == 3 && data.order_type == 4) ||
                    (data.after_sale_status == 15 && [2].includes(data.after_sale_type) && data.logistics_number)
                  "
                >
                  <a-button
                    v-auth="['shopSaleYelHuo']"
                    type="link"
                    size="small"
                    :class="[data?.transfering && 'disabled-btn']"
                    @click="handleActions('affirmShippingAddress', data)"
                  >
                    {{ data.order_type != 4 ? '确认收货' : '收货并退款' }}
                  </a-button>
                </template>
                <a-button v-if="data.order_type != 4" type="link" size="small" @click="handleActions('sendMsg', data)"
                  >发送短信</a-button
                >
                <template v-if="showAgreeHandler(data)">
                  <a-button
                    v-auth="['shopSaleYes']"
                    type="link"
                    size="small"
                    :class="[data?.transfering && 'disabled-btn']"
                    @click="handleActions('agree', data)"
                  >
                    同意
                  </a-button>
                </template>

                <template v-if="showRefuseHandler(data)">
                  <a-button
                    v-auth="['shopSaleNo']"
                    type="link"
                    size="small"
                    :class="[data?.transfering && 'disabled-btn']"
                    @click="data.order_type == 4 ? wxShopRefuse(data) : handleActions('turnDown', data)"
                  >
                    拒绝
                  </a-button>
                </template>

                <span
                  v-auth="['shopSaleRemark']"
                  style="color: #f2a626"
                  :class="['cursor-pointer', data?.transfering && 'disabled-btn']"
                  @click="handleActions('remark', data)"
                  >备注({{ data.shop_remark_count }})</span
                >
                <!-- <a-button type="link" size="small" @click="crossBorderdRefundHandle(data)"> 退款 </a-button> -->
              </a-space>
            </template>
          </template>
          <template #footer="{ scope, data }">
            <div class="table_row_bottom" v-if="data.shop_remark || data.user_remark">
              <div class="label">{{ data.shop_remark ? getPlatformInfo(data.remark_type) : '买家备注：' }}</div>
              <div class="val text_overflow">
                <a-tooltip placement="topLeft">
                  <template #title>{{ data.shop_remark ? data.shop_remark : data.user_remark }}</template>
                  {{ data.shop_remark ? data.shop_remark : data.user_remark }}
                </a-tooltip>
              </div>
              <div class="flex" v-if="data.shop_remark_url">
                <div
                  v-for="(i, index) in JSON.parse(data.shop_remark_url)"
                  :key="index"
                  class="overflow-hidden border-rd-4px ml-8px"
                  style="width: 30px; height: 30px"
                >
                  <a-image
                    v-if="i.type == 'img' || i.type == 'image'"
                    width="30px"
                    height="30px"
                    :src="i.url"
                  ></a-image>
                  <FilePreview
                    v-if="i.type == 'video'"
                    :src="i.url"
                    :cover="i.cover_image"
                    style="width: 30px; height: 30px"
                  ></FilePreview>
                </div>
              </div>
            </div>
          </template>
        </DescTableLayout>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="state.modalConfig.isVisible"
      :title="state.modalConfig.title"
      centered
      :width="['Negotiate'].includes(state.modalConfig.warpKey) ? 790 : 520"
      :maskClosable="false"
      :closable="state.modalConfig.warpKey === 'relatedComplaints' ? true : false"
      :confirm-loading="state.modalConfig.confirmLoading"
    >
      <template #footer>
        <a-button
          @click="modalCancel"
          v-if="
            ![
              'batchCheckOrder',
              'batchOrder',
              'Negotiate',
              'refuse',
              'shopRefund',
              'refundVoucher',
              'refuse_voucher',
              'relatedComplaints'
            ].includes(state.modalConfig.warpKey)
          "
          >取消</a-button
        >
        <a-button
          type="primary"
          @click="modalOk"
          v-if="
            ![
              'batchCheckOrder',
              'batchOrder',
              'Negotiate',
              'refuse',
              'shopRefund',
              'refundVoucher',
              'refuse_voucher',
              'relatedComplaints'
            ].includes(state.modalConfig.warpKey)
          "
          >确定</a-button
        >
      </template>
      <div v-if="['refund'].includes(state.modalConfig.warpKey)">确认同意买家退款后，系统将自动发起退款处理</div>
      <div v-if="['refundReturn'].includes(state.modalConfig.warpKey)">确认同意买家退货申请</div>
      <div v-if="['confirmGoods'].includes(state.modalConfig.warpKey)">
        确认已收到买家退回的商品？确认后系统将自动更改订单状态
      </div>
      <div v-if="['agree'].includes(state.modalConfig.warpKey)">
        <div
          class="c-#e63030 font-size-12px mb-4px"
          v-if="state.modalConfig.data?.is_bind && state.modalConfig.data?.active_type === 0"
        >
          该商品与营销商品有绑定!
        </div>
        <div>确认进行此操作吗?</div>
      </div>
      <div v-if="['affirmShippingAddress'].includes(state.modalConfig.warpKey)">
        <div>请确认是否收到货物</div>
        <div>确认收货后，系统将自动发起退款处理</div>
      </div>
      <div v-if="['refuse_voucher'].includes(state.modalConfig.warpKey)">
        <div class="font-size-18px c-#000000E0 font-600 mb-8px">提示</div>
        <div>当前售后暂不支持直接拒绝，需先使用【售后协商】功能与买家进一步沟通，如买家超时仍未回复，您可拒绝申请</div>
        <div class="text-right mt-30px">
          <a-button @click="modalCancel">取消</a-button>
          <a-button type="primary" @click="handleActions('Negotiate', state.modalConfig.data)">去协商</a-button>
        </div>
      </div>

      <TurnDown
        ref="formRef"
        v-if="['turnDown'].includes(state.modalConfig.warpKey)"
        v-model:data="state.formTurnDownData"
      />
      <MaterialFlow
        v-if="['deliverGoods', 'returnGoods'].includes(state.modalConfig.warpKey)"
        :data="state.modalConfig"
      />
      <SendGoods
        v-if="['sendGoods', 'editSendGoods'].includes(state.modalConfig.warpKey)"
        :item="state.modalConfig"
        ref="formRef"
        v-model:data="state.logisticsFormData"
        @changeId="
          (data) => {
            state.logisticsFormData.express_id = data.id
          }
        "
      />
      <Remark
        v-if="['remark'].includes(state.modalConfig.warpKey)"
        :item="state.modalConfig.data"
        v-model:data="state.modalConfig.remarkData"
        ref="formRef"
      />
      <CheckOrder
        ref="checkOrder"
        v-if="['batchCheckOrder', 'batchOrder'].includes(state.modalConfig.warpKey)"
        :pageSize="state.initParams.page_size"
        :invalidOrderNums="state.invalid_order_nums"
        :type="state.invalid_type"
        @close="onClose"
        @submit="onSubmit"
      />
      <!-- 协商 -->
      <Negotiate
        v-if="['Negotiate'].includes(state.modalConfig.warpKey)"
        v-model:data="state.modalConfig.data"
        ref="formRef"
        @event="onEvent"
      />
      <!-- 拒绝 -->
      <Refuse
        v-if="['refuse'].includes(state.modalConfig.warpKey)"
        v-model:data="state.modalConfig.data"
        ref="formRef"
        @event="onEvent"
      />
      <!-- 退款凭证 -->
      <RefundVoucher
        v-if="['refundVoucher'].includes(state.modalConfig.warpKey)"
        v-model:data="state.modalConfig.data"
        ref="formRef"
        @event="onEvent"
      />
      <!-- 小店退户欧提款 -->
      <ShopRefund
        v-if="['shopRefund'].includes(state.modalConfig.warpKey)"
        v-model:data="state.modalConfig.data"
        @event="onEvent"
      />
      <!-- 关联投诉 -->
      <RelatedComplaints
        v-if="['relatedComplaints'].includes(state.modalConfig.warpKey)"
        :data="state.modalConfig.data"
      ></RelatedComplaints>
      <SendMsg
        v-if="['sendMsg'].includes(state.modalConfig.warpKey)"
        v-model:data="state.modalConfig.data"
        ref="formRef"
      />
    </a-modal>
    <a-tour
      class="other_tour"
      scrollIntoViewOptions="false"
      v-if="state.tableConfig.list.length > 0"
      v-model:current="current"
      :open="open"
      :steps="steps"
      @close="handleOpen(false)"
    />
    <a-modal
      v-model:open="state.newModalConfig.isVisible"
      :title="state.newModalConfig.title"
      centered
      :width="state.newModalConfig.width"
      :maskClosable="false"
      :footer="null"
      destroyOnClose
    >
      <RefundAgainModal
        v-if="['refundAgain', 'lookOffineRefund'].includes(state.newModalConfig.type)"
        :data="state.newModalConfig.data"
        :isPreview="state.newModalConfig.type === 'refundAgain' ? false : true"
        @onEvent="onNewEvent"
      />
    </a-modal>
  </div>
</template>
<script setup lang="tsx">
  defineOptions({ name: 'ShopSalesQuery' })
  import MaterialFlow from './components/MaterialFlow.vue'
  import SendGoods from './components/SendGoods.vue'
  import TurnDown from './components/TurnDown.vue'
  import Remark from './components/Remark.vue'
  import CheckOrder from './components/CheckOrder.vue'
  import CountDown from './components/countDown.vue'
  import Negotiate from './components/Negotiate.vue'
  import Refuse from './components/Refuse.vue'
  import RefundVoucher from './components/RefundVoucher.vue'
  import RelatedComplaints from './components/RelatedComplaints.vue'
  import ShopRefund from '@/views/shop/order/sale_details/components/ShopRefund.vue'
  import SendMsg from '@/views/shop/order/order_list/components/SendMsg.vue'
  import { onActivated, onMounted, reactive, ref, createVNode, nextTick } from 'vue'
  import RefundAgainModal from './components/refundAgainModal.vue'
  import { useRouter, useRoute } from 'vue-router'
  import { cloneDeep } from 'lodash-es'
  import { message, notification, Modal } from 'ant-design-vue'
  import { QuestionCircleFilled, QuestionCircleOutlined } from '@ant-design/icons-vue'
  import {
    get_after_sale_list,
    get_after_sale_count,
    post_after_sale_batch_return_apply,
    post_after_sale_edit,
    post_batch_confirms,
    get_after_sale_status,
    post_exportdata_create,
    saleStatus,
    post_after_sale_address_update,
    post_after_sale_address_confirm,
    refundType,
    checkSaleNumsApi,
    batchRemarkApi,
    helpAddApi
  } from './index.api'
  import { post_order_record_add } from '@/views/shop/order/order_list/index.api.ts'
  import { batch_send_msg } from '@/api/common'
  import { returnMoney } from '../sale_details/index.api'
  import datas from './data'
  import { useDownloadCenter, useRouterBack, useAuth } from '@/hooks'
  import moment from 'moment'
  import { requireImg, getConfig, checkMobilePermission, localStg } from '@/utils'

  import type { TourProps } from 'ant-design-vue'
  const { routeParams, resetRouteParams } = useRouterBack()
  const searchFormDataRef = ref()
  const { goCenter } = useDownloadCenter()
  const router = useRouter()
  const { isAuth } = useAuth()
  const route = useRoute()
  const formRef = ref(null)
  const afterSale = ref(null)
  const open = ref<boolean>(false)

  const current = ref(0)
  const {
    SEARCH_CONFIG_DATA,
    BUTTON_RADIO_GROUP,
    salesStatus,
    columns,
    modalConfigTitle,
    formTurnDownData,
    logisticsFormData,
    customsState
  } = datas()
  const state = reactive({
    invalid_order_nums: [],
    review_status: false,
    refund_money_type: false,
    invalid_type: 1, // 区分批量售后单还是批量订单
    logisticsFormData: JSON.parse(JSON.stringify(logisticsFormData)),
    form: {},
    initParams: {
      page: 1,
      page_size: 10,
      apply_time_sort: 'desc', // desc
      after_sale_status: '',
      refund_sns: '',
      edit_admin_id: undefined,
      order_sns: '',
      review_status: 0,
      refund_money_type: 0
    } as any,
    content: {
      page: 1,
      page_size: 10,
      apply_time_sort: 'desc',
      refund_sns: '',
      order_sns: '',
      edit_admin_id: undefined
    },
    searchConfig: SEARCH_CONFIG_DATA,
    actionConfig: JSON.parse(JSON.stringify(BUTTON_RADIO_GROUP)),
    tableConfig: {
      isLoading: false,
      size: 'small',
      pagination: false,
      columns: columns,
      list: []
    },
    paginationConfig: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    },
    selectTable: [],
    allSelectTable: [],
    selectItem: [],
    indeterminate: false,
    formTurnDownData: JSON.parse(JSON.stringify(formTurnDownData)),
    modalConfig: {
      isVisible: false,
      title: '',
      warpKey: '',
      data: '' as any,
      confirmLoading: false,
      remarkData: {
        remark: '',
        url: []
      }
    },
    newModalConfig: {
      isVisible: false,
      title: '',
      type: '',
      data: '' as any
    } as any
  })
  const steps: TourProps['steps'] = [
    {
      title: '按钮位置优化',
      description: '售后列表中的【售后类型】搬家了',
      placement: 'top',
      target: () => document.getElementById('afterSale')
    }
  ]

  const handleOpen = async (val: boolean) => {
    open.value = val
    try {
      await helpAddApi({ ui_change: true })
      let obj = localStg.get('userInfo')
      if (obj) {
        obj.ui_change_show = false
      }
      localStg.set('userInfo', obj)
      initPageData(state.initParams)
    } catch (error) {
      console.log(error)
    }
  }
  const changeReview_status = (event) => {
    const val = event.target.checked ? 1 : 0
    state.initParams.review_status = val
    state.content.review_status = val
    initPageData(state.initParams)
  }
  const changeOffline_refund = (event) => {
    const val = event.target.checked ? 2 : 0
    state.initParams.refund_money_type = val
    state.content.refund_money_type = val
    initPageData(state.initParams)
  }
  const getPlatformInfo = (type) => {
    const obj = {
      1: '总后台备注：',
      2: '商家后台备注：',
      3: '平台客服备注：',
      4: '商家客服备注：',
      5: '买家备注：'
    }
    return obj[type]
  }
  const channelType = (type: number) => {
    let status: Record<number, string> = {
      0: '自然流量',
      1: '广点通',
      2: '视频号广告',
      4: '磁力引擎',
      8: '超级汇川'
    }
    return status[type]
  }
  // 订单来源
  const ordersource = (type: string | number) => {
    let status = {
      1: '自然流量',
      2: '广告平台',
      3: '回流流量',
      4: '外部流量'
    }
    return status[type]
  }
  const iconType = (type: number) => {
    let status: Record<number, string> = {
      4: requireImg('order/o1.png'),
      1: requireImg('order/o8.png'),
      5: requireImg('order/o5.png'),
      6: requireImg('order/o6.png'),
      8: requireImg('order/o9.png')
    }
    return status[type]
  }
  // 售后状态
  const salesStatusText = (type) => {
    return {
      1: '待处理',
      2: '待买家退货',
      3: '待商家收货',
      4: '换货待商家发货',
      5: '待买家收货',
      6: '退款完成',
      7: '售后驳回',
      8: '退款中',
      9: '极速退款成功',
      10: '售后完成',
      11: '待买家处理',
      12: '退款失败',
      13: '售后关闭',
      15: '售后协商',
      16: '待用户确认'
    }[type]
  }
  const orderStatus = reactive([
    { id: 'all', name: '全部', show: true, param: {}, num: 0 },
    {
      id: 'only_return_money',
      name: '仅退款待处理',
      show: true,
      num: 0,
      param: { after_sale_status: 1, after_sale_type: 1 }
    },
    {
      id: 'return_goods_money',
      name: '退货待处理',
      show: true,
      num: 0,
      param: { after_sale_status: 1, after_sale_type: 2 }
    },
    { id: 'wait_user_return_goods', name: '待买家退货', show: true, num: 0, param: { after_sale_status: 2 } },
    { id: 'wait_shop_deliver_goods', name: '待商家收货', show: true, num: 0, param: { after_sale_status: 3 } },
    { id: 'wait_user_accept', name: '待买家确认收货', show: true, num: 0, param: { after_sale_status: 5 } },
    // { id: "after_complete", name: '售后完成', show: false, num: 0, param: { after_sale_status: 10 } },
    { id: 'after_close', name: '售后关闭', num: 0, show: true, param: { after_sale_status: 13 } },
    { id: 'refuse_num', name: '售后驳回', num: 0, show: true, param: { after_sale_status: 7 } },
    { id: 'return_money_complete', name: '退款完成', show: true, num: 0, param: { after_sale_status: 6 } }
  ])
  //同意显示
  const showAgreeHandler = (data) => {
    return (
      data.after_sale_status == 1 ||
      (data.after_sale_status == 15 && [1].includes(data.after_sale_type)) ||
      (data.after_sale_status == 15 && [2].includes(data.after_sale_type) && !data.logistics_number)
    )
  }
  //拒绝显示
  const showRefuseHandler = (data) => {
    return (
      ([1].includes(data.after_sale_status) && ![4].includes(data.order_type)) ||
      ([1, 3, 4].includes(data.after_sale_status) &&
        [4].includes(data.order_type) &&
        [1, 2].includes(data.after_sale_type)) ||
      ([3].includes(data.after_sale_status) && [4].includes(data.order_type) && [3].includes(data.after_sale_type))
    )
  }
  //批量查单
  const batchCheckOrder = (type: string) => {
    state.initParams.page = 1
    state.content.page = 1
    state.invalid_type = type === 'batchOrder' ? 2 : 1
    handleActions(type, { pageSize: state.initParams.page_size })
  }
  //关闭弹窗
  const onClose = () => {
    state.initParams.refund_sns = ''
    state.content.refund_sns = ''
    modalCancel()
  }
  const checkOrder = ref()
  const toPath = (data: any) => {
    let query = {
      id: data.id
    }
    if (data.after_sale_complaint) {
      query.after_sale_complaint = data.after_sale_complaint
      query.order_sn = data.order_sn
    }
    router.push({ path: '/mall/order/sale_detail', query })
  }
  //提交数据
  const onSubmit = async (data: any) => {
    if (data.length == 0) {
      message.warning(state.invalid_type === 2 ? '请输入正确的订单编号' : '请输入正确的售后单号')
      return
    }
    if (data.length > state.initParams.page_size) {
      message.warning(`每次最多可查${state.initParams.page_size}条！`)
      return
    }

    try {
      let params = {
        search_type: state.invalid_type
      } as {
        search_type: number
        refund_nums: string[]
        order_nums: string[]
      }
      if (state.invalid_type === 1) {
        params.refund_nums = data
        params.order_nums = []
      } else {
        params.refund_nums = []
        params.order_nums = data
      }
      let res = await checkSaleNumsApi(params)
      state.invalid_order_nums =
        state.invalid_type == 1 ? res.data?.invalid_refund_nums : res.data?.invalid_order_nums || []
      if (state.invalid_order_nums.length > 0) {
        message.warning(state.invalid_type === 2 ? '请输入正确的订单编号' : '请输入正确的售后单号')
        return
      }
      if (state.invalid_order_nums.length == 0) {
        console.log('-000000e90495-384-5034-5349-5')
        state.modalConfig.isVisible = false
        if (state.invalid_type == 1) {
          state.initParams.order_sns = ''
          state.content.order_sns = ''
          state.initParams.refund_sns = (data && data.join(',')) || ''
          state.content.refund_sns = (data && data.join(',')) || ''
        } else {
          state.initParams.refund_sns = ''
          state.content.refund_sns = ''
          state.initParams.order_sns = (data && data.join(',')) || ''
          state.content.order_sns = (data && data.join(',')) || ''
        }

        initPageData(state.initParams)
        state.invalid_type = 1
        checkOrder.value.init()
      }
      console.log('------res', res)
    } catch (error) {}
    console.log('deadfasdf', data)
  }
  // 批量备注
  const batchRemark = () => {
    if (!state.selectTable.length) {
      message.warning('请选择相关售后进行批量操作')
      return
    }

    handleActions('remark', { order_id: state.selectTable, type: 'batch' })
  }
  const batchSendMsg = () => {
    if (!state.selectTable.length) {
      message.warning('请选择相关售后进行批量操作')
      return
    }

    handleActions('sendMsg', { order_id: state.selectTable, type: 'batch' })
  }
  const wxShopRefuse = (record) => {
    if (record.after_sale_type != 3) {
      // 待商家收货拒绝
      state.modalConfig.isVisible = true
      state.modalConfig.title = ''
      state.modalConfig.warpKey = 'refuse_voucher'
      state.modalConfig.data = record
    } else {
      handleActions('refuse', record)
    }
  }

  // 搜索
  const changeValue = (data) => {
    console.log(data, 'data')
    state.initParams.refund_sns = ''
    state.content.refund_sns = ''
    state.initParams.order_sns = ''
    state.content.order_sns = ''
    state.initParams = { ...state.initParams, ...data.formData, page: 1 }
    state.initParams.edit_admin_id = state.initParams.edit_admin_id * 1
    state.form = { ...data.formData }
    state.content = { ...state.content, ...data.formData }
    if (!data.status) {
      state.initParams.refund_sns = ''
      state.content.refund_sns = ''
      state.initParams.order_sns = ''
      state.content.order_sns = ''
      state.initParams.page_size = 10
      state.content.page_size = 10
      state.paginationConfig.pageSize = 10
    }

    if (data.formData && data.formData.created_at && data.formData.created_at[0] && data.formData.created_at[1]) {
      state.initParams.created_at = data.formData.created_at.join('_')
      state.content.created_at = data.formData.created_at.join('_')
    } else {
      state.initParams.created_at = null
      state.content.created_at = null
    }
    state.initParams.pay_time = data.formData.pay_time ? data.formData.pay_time.join('_') : undefined
    state.content.pay_time = data.formData.pay_time ? data.formData.pay_time.join('_') : undefined
    state.initParams.finish_time = data.formData.finish_time ? data.formData.finish_time.join('_') : undefined
    state.content.finish_time = data.formData.finish_time ? data.formData.finish_time.join('_') : undefined
    // 售后状态和Tab切换联动
    if (data.formData.after_sale_status) {
      const obj =
        orderStatus.find(
          (v) => v.param.after_sale_status == data.formData.after_sale_status && !v.param.after_sale_type
        ) || {}
      changeBtnType({ value: obj.id || 'all' })
    }

    changeBtnType({ value: state.actionConfig.value })
  }
  // 按钮操作
  const changeBtnType = (data) => {
    console.log(data.value)
    state.actionConfig.value = data.value
    let arr = state.actionConfig.list.filter((item) => item.value === data.value)
    state.initParams = {
      ...state.initParams,
      ...{ page: 1, page_size: 10 },
      ...state.form,
      ...arr[0].param,
      created_at:
        state.form.created_at &&
        state.form.created_at[0] &&
        state.form.created_at[1] &&
        state.form.created_at.join('_'),
      pay_time: state.form.pay_time ? state.form.pay_time.join('_') : undefined,
      finish_time: state.form.finish_time ? state.form.finish_time.join('_') : undefined,
      after_sale_type: state.form.after_sale_type
        ? state.form.after_sale_type
        : arr[0].param.after_sale_type
          ? arr[0].param.after_sale_type
          : null,
      after_sale_status: state.form.after_sale_status
        ? state.form.after_sale_status
        : arr[0].param.after_sale_status
          ? arr[0].param.after_sale_status
          : null
    }
    if (data.value !== 'time_out') {
      state.initParams.next_operate_time = undefined
    }
    if (data.value == 'return_goods_money') {
      state.initParams.wait_refund = 1
      state.initParams.after_sale_status = null
      state.initParams.after_sale_type = null
    } else {
      state.initParams.wait_refund = undefined
    }
    // if (data.value == 'only_return_money') {
    //   state.initParams.after_sale_type = 1
    //   state.initParams.next_operate_time = null
    //   state.initParams.after_sale_status = state.form.after_sale_status ? state.form.after_sale_status : 1
    // } else if (data.value == 'return_goods_money') {
    //   state.initParams.after_sale_type = 2
    //   state.initParams.next_operate_time = null
    //   state.initParams.after_sale_status = state.form.after_sale_status ? state.form.after_sale_status : 1
    // } else if (data.value == 'after_close') {
    //   state.initParams.after_sale_status = 13
    //   state.initParams.next_operate_time = null
    //   state.initParams.after_sale_type = state.form.after_sale_type ? state.form.after_sale_type : null
    // } else if (data.value == 'refuse_num') {
    //   state.initParams.after_sale_status = 7
    //   state.initParams.next_operate_time = null
    //   state.initParams.after_sale_type = state.form.after_sale_type ? state.form.after_sale_type : null
    // } else if (data.value == 'return_money_complete') {
    //   state.initParams.after_sale_status = 6
    //   state.initParams.next_operate_time = null
    //   state.initParams.after_sale_type = state.form.after_sale_type ? state.form.after_sale_type : null
    // } else if (data.value == 'after_close_timeout') {
    //   state.initParams.after_sale_status = 14
    //   state.initParams.next_operate_time = null
    //   state.initParams.after_sale_type = state.form.after_sale_type ? state.form.after_sale_type : null
    // } else if (data.value == 'wait_shop_deliver_goods') {
    //   state.initParams.after_sale_status = 3
    //   state.initParams.next_operate_time = null
    //   state.initParams.after_sale_type = state.form.after_sale_type ? state.form.after_sale_type : null
    // } else if (data.value == 'wait_user_return_goods') {
    //   state.initParams.after_sale_status = 2
    //   state.initParams.next_operate_time = null
    //   state.initParams.after_sale_type = state.form.after_sale_type ? state.form.after_sale_type : null
    // } else if (data.value == 'time_out') {
    //   state.initParams.next_operate_time = 1
    //   state.initParams.after_sale_status = null
    //   state.initParams.after_sale_type = null
    //   state
    // } else {
    //   state.initParams.next_operate_time = null
    //   state.initParams.after_sale_status = state.form.after_sale_status ? state.form.after_sale_status : null
    //   state.initParams.after_sale_type = state.form.after_sale_type ? state.form.after_sale_type : null
    // }
    // state.initParams.page = 1
    state.content.page = 1

    const sortArr = [
      'only_return_money',
      'return_goods_money',
      'wait_user_return_goods',
      'wait_shop_deliver_goods',
      'time_out'
    ]
    if (state.actionConfig.value == 'wait_shop_deliver_goods' && state.form.next_time_sort) {
      state.initParams.next_time_sort = state.form.next_time_sort
    } else if (sortArr.includes(state.actionConfig.value)) {
      state.initParams.next_time_sort = 'asc'
    } else {
      state.initParams.next_time_sort = undefined
    }

    initPageData(state.initParams)
  }
  const crossBorderdRefundHandle = (val) => {
    try {
      Modal.confirm({
        content: '当前处理的售后单还未完成退货清关，若清关失败则可能导致钱货两空，确定要退款么？',
        icon: null,
        centered: true,
        okText: '确定退款',
        onOk: async () => {
          // await couponRelease({ id: row.id })
          message.success('操作成功')
          getPageData()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  const getPageData = () => {
    if (route.query?.order_sn) {
      state.initParams.order_sn = route.query?.order_sn
      state.content.order_sn = route.query?.order_sn
      nextTick(() => {
        searchFormDataRef.value.formData.order_sn = route.query?.order_sn
      })
    }
    if (route.query?.refund_sn) {
      state.initParams.refund_sn = route.query?.refund_sn
      state.content.refund_sn = route.query?.refund_sn
      nextTick(() => {
        searchFormDataRef.value.formData.refund_sn = route.query?.refund_sn
      })
    }
    if (route.query?.admin_id) {
      state.initParams.edit_admin_id = route.query?.admin_id * 1
      state.content.edit_admin_id = route.query?.admin_id * 1
      state.searchConfig.data.forEach((item) => {
        if (item.field === 'edit_admin_id') {
          item.value = route.query?.admin_id * 1
        }
      })
    }

    initPageData(state.initParams)
    initStatus()
  }
  const modifyButtonText = () => {
    // 获取 "结束导览" 按钮
    const nextButton = document.querySelector('.ant-tour-next-btn span')
    if (nextButton) {
      nextButton.textContent = '知道了'
    }
  }
  onMounted(() => {
    getPageData()
  })
  onActivated(() => {
    if (routeParams.params?.page) {
      if (routeParams.params?.page === 'add') {
        state.initParams.page = 1
        state.initParams.page_size = 10
      }
      getPageData()
      resetRouteParams()
    }
  })
  const initStatus = async () => {
    const result = await get_after_sale_status()
    if (result.code === 0) {
      let arr = []
      for (const resultKey in result.data.list) {
        let item = {
          value: undefined,
          label: undefined
        }
        item.value = +resultKey
        item.label = result.data.list[resultKey]
        arr.push(item)
      }
      state.searchConfig.data.forEach((item) => {
        if (item.field === 'after_sale_status') {
          item.props.options = arr
        }
      })
    }
    state.saleData = result.data?.list
  }
  // 初始化全选数据
  const initSelectAll = (data, key) => {
    const allArr = []
    data && data.forEach((item) => allArr.push(item[key]))
    return allArr
  }
  const initPageData = async (data) => {
    state.tableConfig.isLoading = true
    let [result, resultCount] = await Promise.all([get_after_sale_list(data), get_after_sale_count(state.content)])
    // const result = await get_after_sale_list(data)
    // const resultCount = await get_after_sale_count(state.content)
    if (result.code === 0 && resultCount.code === 0) {
      state.tableConfig.list = result?.data?.list || []
      state.tableConfig.list = (result?.data?.list || []).map((v) => {
        return {
          ...v,
          showPhone: 0
        }
      })
      state.allSelectTable = initSelectAll(state.tableConfig.list, 'id')
      state.paginationConfig.total = result?.data?.total || 0
      state.paginationConfig.current = result?.data?.page || 1
      if (resultCount.data) {
        state.actionConfig.list.forEach((item) => {
          item.total = resultCount.data[item.value] || 0
        })
      }
    }
    state.tableConfig.isLoading = false
    state.indeterminate = false
    state.selectTable = []
    state.selectItem = []

    nextTick(() => {
      if (localStg.get('userInfo')?.ui_change_show && document.getElementById('afterSale')) {
        setTimeout(() => {
          open.value = true
        }, 500)
        setTimeout(() => {
          modifyButtonText()
        }, 600)
      }
    })
  }
  const handleSort = () => {
    if (state.initParams.apply_time_sort === 'desc') {
      state.initParams.apply_time_sort = 'asc'
      state.content.apply_time_sort = 'asc'
    } else {
      state.initParams.apply_time_sort = 'desc'
      state.content.apply_time_sort = 'desc'
    }
    state.initParams.page = 1
    state.content.page = 1
    initPageData(state.initParams)
  }
  // 分页
  const changePages = (data) => {
    state.initParams.page = data.page
    state.initParams.page_size = data.pageSize
    state.content.page = data.page
    state.content.page_size = data.pageSize
    state.paginationConfig.pageSize = data.pageSize
    initPageData(state.initParams)
  }
  // 选择每一条数据
  const tabelItemSelect = (checkedValue, data) => {
    console.log(checkedValue, data)
    if (state.selectTable.includes(data.id)) {
      state.selectTable = state.selectTable.filter((item) => item !== data.id)
      state.selectItem = state.selectItem.filter((item) => item.id !== data.id)
    } else {
      state.selectTable.push(data.id)
      state.selectItem.push(data)
    }
    state.indeterminate = !!state.selectTable.length && state.selectTable.length < state.allSelectTable.length
  }
  // 全选
  const onCheckAllChange = (e: any) => {
    Object.assign(state, {
      selectTable: e.target.checked ? state.allSelectTable : [],
      selectItem: e.target.checked ? state.tableConfig.list : [],
      indeterminate: false
    })
  }
  // 集中处理 操作事件
  const handleActions = (type, data) => {
    console.log('1111', type, data)
    if (!state.selectTable.length && !data) {
      message.warning('请选择相关售后订单进行批量操作')
      return
    }
    if (
      ['sendGoods', 'editSendGoods', 'affirmShippingAddress', 'agree', 'turnDown', 'remark'].includes(type) &&
      data?.transfering
    ) {
      message.warning('该笔售后单存在打款中记录，不可操作售后单')
      return
    }

    if (['agree'].includes(type) && data.order_type == 4 && [2, 3].includes(data.after_sale_type)) {
      type = 'shopRefund'
    }

    if (['editSendGoods'].includes(type)) {
      // state.logisticsFormData.logistics_company = data.logistics_company
      // state.logisticsFormData.logistics_number = data.logistics_number
      state.logisticsFormData.logistics_company = undefined
      state.logisticsFormData.logistics_number = undefined
    }
    state.modalConfig.isVisible = true
    state.modalConfig.title = modalConfigTitle[type]
    state.modalConfig.warpKey = type
    state.modalConfig.data = data
    console.log('-----------', type, state.modalConfig.data, state.modalConfig.title)
  }
  const modalCancel = () => {
    // state.initParams.refund_sns = ''
    // state.content.refund_sns = ''
    // state.initParams.order_sns = ''
    // state.content.order_sns = ''
    state.invalid_type = 1
    state.modalConfig.isVisible = false
    setTimeout(() => {
      if (['turnDown'].includes(state.modalConfig.warpKey)) {
        state.formTurnDownData = JSON.parse(JSON.stringify(formTurnDownData))
      }
      if (['sendGoods', 'editSendGoods'].includes(state.modalConfig.warpKey)) {
        state.logisticsFormData = JSON.parse(JSON.stringify(logisticsFormData))
      }
      state.modalConfig.title = ''
      state.modalConfig.warpKey = ''
      state.modalConfig.data = ''
      state.modalConfig.confirmLoading = false
    }, 300)
  }

  const onEvent = async (value) => {
    state.modalConfig.isVisible = false
    if (value.cmd == 'submit') {
      initPageData(state.initParams)
    } else if (value.cmd == 'agree_refund') {
      console.log(value, 'valuevalue')
      // 同意退款退货
      state.modalConfig.title = ''
      state.modalConfig.warpKey = ''
      state.modalConfig.data = ''
      await post_after_sale_edit({
        refund_sn: value.data.refund_sn,
        after_sale_status: 1,
        shop_address_id: value.data.shop_address_id
      })
      message.success('操作成功')
      initPageData(state.initParams)
    }
  }

  const modalOk = async () => {
    try {
      state.modalConfig.confirmLoading = true
      let result = null
      if (['refund', 'refundReturn'].includes(state.modalConfig.warpKey)) {
        const type = {
          refund: 1,
          refundReturn: 2
        }[state.modalConfig.warpKey]
        const ids = state.selectItem.filter((v: any) => v.after_sale_type == type).map((v: any) => v.id)
        result = await post_after_sale_batch_return_apply({ ids: ids.join() })
      }
      if (['agree'].includes(state.modalConfig.warpKey)) {
        result = await post_after_sale_edit({ after_sale_status: 1, refund_sn: state.modalConfig.data.refund_sn })
      }
      if (['turnDown'].includes(state.modalConfig.warpKey)) {
        const values = await formRef.value.validateFields()
        let list = formRef.value.state.img
        if (values) {
          list =
            list?.map((v) => {
              if (v?.fileInfo?.type == 'video') {
                return {
                  url: v.url,
                  type: v?.fileInfo?.type,
                  cover_image: convertToImg(v.url)
                }
              } else return { url: v.url, type: 'image' }
            }) || []
          result = await post_after_sale_edit({
            after_sale_status: 2,
            refund_sn: state.modalConfig.data.refund_sn,
            refuse_reason: state.formTurnDownData.refuse_reason,
            evidence: JSON.stringify(list)
          })
        }
      }
      if (['sendGoods', 'editSendGoods'].includes(state.modalConfig.warpKey)) {
        const values = await formRef.value.validateFields()
        if (values) {
          const list = formRef.value.getlist()
          list.forEach((item) => {
            if (item.id === values.express_id) {
              values.logistics_company = item.code
            }
          })

          result = await post_after_sale_address_update({
            id: state.modalConfig.data.id,
            ...values
          })
        }
      }
      if (['affirmShippingAddress'].includes(state.modalConfig.warpKey)) {
        result = await post_after_sale_address_confirm({
          id: state.modalConfig.data.id
        })
      }
      //批量备注
      if (['remark'].includes(state.modalConfig.warpKey) && state.modalConfig.data?.type == 'batch') {
        const values = await formRef.value.validateFields()
        let order_nums = state.selectItem.map((item) => item.order_sn)
        if (values) {
          const res = await batchRemarkApi({
            order_nums: order_nums,
            ...values
          })
          if (res.code === 0) {
            console.log(state.modalConfig.data, 'modalConfig')
            state.modalConfig.isVisible = false
            state.modalConfig.remarkData.remark = null
            state.modalConfig.remarkData.url = []
            state.modalConfig.confirmLoading = false
            initPageData(state.initParams)
          }
        }
      }
      //单个备注
      if (['remark'].includes(state.modalConfig.warpKey) && state.modalConfig.data?.type != 'batch') {
        const values = await formRef.value.validateFields()
        if (values) {
          let list =
            values.url?.map((v) => {
              if (v?.fileInfo?.type == 'video') {
                return {
                  url: v.url,
                  type: v?.fileInfo?.type,
                  cover_image: convertToImg(v.url)
                }
              } else return { url: v.url, type: 'image' }
            }) || []
          const res = await post_order_record_add({
            order_id: state.modalConfig.data.order_id,
            ...values,
            url: JSON.stringify(list)
          })
          if (res.code === 0) {
            console.log(state.modalConfig.data, 'modalConfig')
            state.modalConfig.remarkData.remark = null
            state.modalConfig.remarkData.url = []
            state.modalConfig.confirmLoading = false
            initPageData(state.initParams)
            formRef.value.getRemarkList()
          }
        }
      }
      // 批量同意收货
      if (['confirmGoods'].includes(state.modalConfig.warpKey)) {
        console.log(state.selectItem)
        const ids = state.selectItem.map((v: any) => v.id)
        result = await post_batch_confirms({ ids: ids })
      }
      if (['sendMsg'].includes(state.modalConfig.warpKey)) {
        const values = await formRef.value.validateFields()

        if (values.content.includes('{立减金额}')) {
          return message.warning('该模板内包含有【立减金额】，请先确认【立减金额】再选用模板')
        }
        let order_nums =
          state.modalConfig.data.type == 'batch'
            ? state.selectItem.map((item) => item.order_sn)
            : [state.modalConfig.data.order_sn]

        if (values) {
          result = await batch_send_msg({
            order_sn_list: order_nums,
            sms_temp_id: values.sms_temp_id
          })
        }
      }

      if (result?.code === 0) {
        message.success('操作成功')
        state.selectTable = []
        state.selectItem = []
        state.indeterminate = false
        modalCancel()
        initPageData(state.initParams)
      }
      if (state.modalConfig.warpKey !== 'remark' && state.modalConfig.data?.type != 'batch') modalCancel()
    } catch (error) {
      if (
        !['sendGoods', 'editSendGoods', 'turnDown'].includes(state.modalConfig.warpKey) &&
        state.modalConfig.warpKey !== 'remark' &&
        state.modalConfig.data?.type != 'batch'
      )
        modalCancel()
      state.modalConfig.confirmLoading = false
    }
  }
  const convertToImg = (mp4Url) => {
    const fileName = mp4Url.split('/').pop()
    const path = mp4Url.substring(0, mp4Url.lastIndexOf('/') + 1)

    let upload_type = getConfig('UPLOAD_TYPE') || 'oss'
    let img
    if (upload_type == 'cos') {
      img = `${path.replace('/original/', '/')}${fileName.split('.')[0]}_0.jpg`
    } else if (upload_type == 'oss') {
      img = `${path.replace('/original/', '/')}${fileName.split('.')[0]}_1.jpg`
    } else {
      img = ''
    }

    return img
  }
  const downloadSales = async () => {
    // 没有对应权限且点击了取消
    if ((await checkMobilePermission('shopSaleLookIphone')) == 1) return
    for (let key in state.initParams) {
      if (state.initParams[key] == null) {
        state.initParams[key] = undefined
      }
    }
    let params = {
      ...state.initParams
    }
    if (!state.initParams.after_sale_type) {
      delete params.after_sale_type
    }
    if (!state.initParams.after_sale_status) {
      delete params.after_sale_status
    }
    const result = await post_exportdata_create({
      params: JSON.stringify(params),
      type: 'refund'
    })
    if (result.code === 0) {
      goCenter('DownloadCenter', 'DownloadCenter')
    }
  }
  const go_page = (type, data) => {
    if (['load'].includes(type)) {
      notification.close('loaditem')
      const href = router.resolve({
        path: '/sys/sys_manage/download'
      })
      window.open(href.href, '_blank')
    }
  }
  // 获取售后状态
  const getSaleStatus = async () => {
    try {
      let res = await saleStatus()
      state.saleData = res.data?.list
    } catch (error) {}
  }

  // 获取售后原因
  const getRefundType = async () => {
    try {
      let res = await refundType()
      let arr: any[] = []
      let _data = cloneDeep(res.data)
      for (const key in _data) {
        if (_data[key]) arr.push(..._data[key])
      }
      console.log(res, arr, 'resresresres')
      state.searchConfig.data.forEach((v) => {
        if (v.field == 'msg_id') {
          v.props.options = arr.map((v) => {
            return {
              value: v.key,
              label: v.value
            }
          })
        }
      })
    } catch (error) {
      console.log(error, 'arrarrarr1')
    }
  }
  getRefundType()

  const lessOneDay = (stamp) => {
    if (!stamp) return false
    return moment.unix(stamp).diff(moment(), 'hours') < 24
  }
  const handleNewActions = (type: string, record: any) => {
    try {
      if (type === 'refundAgain' || type === 'lookOffineRefund') {
        if (record?.transfering && type === 'refundAgain') {
          message.warning('该笔售后单存在打款中记录，不可操作售后单')
          return
        }
        state.newModalConfig.type = type
        state.newModalConfig.width = 620
        state.newModalConfig.isVisible = true
        state.newModalConfig.title = type === 'refundAgain' ? '再次退款' : '退款详情'
        state.newModalConfig.data = record
      }
    } catch (err) {
      console.log(e)
    }
  }
  const onNewEvent = ({ cmd, data }: any) => {
    state.newModalConfig.isVisible = false
    if (cmd === 'submit') {
      getPageData()
    }
  }
</script>
<style scoped lang="scss">
  @import './src/assets/css/mixin_scss_fn.scss';

  .sale_type {
    background: #f6f8ff;
    border-radius: 3px;
    border: 1px solid #e1e6ff;
    font-weight: 400;
    font-size: 14px;
    color: #647dff;
    padding: 2px 8px;
    display: inline-block;
  }
  .icon {
    width: 15px;
    height: 15px;
    margin-right: 5px;
  }
  img {
    display: block;
    width: 100%;
    height: 100%;
  }
  .card-warp-dex {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .action-warp {
    margin-top: 5px;
    padding: 8px 16px;
  }
  .desc-table-layout-list-item-desc {
    background-color: rgba(36, 47, 87, 0.05);
    padding: 8px 16px;
  }
  .user-address {
    @include text_overflow(2);
  }
  .cellos-item_title {
    @include set_font_config(--font-size-large, --text-color-base);
    @include text_overflow(2);
  }
  .cellos-item-prod-warp {
    box-sizing: border-box;
    padding: 0 8px;
    overflow: hidden;
  }
  .cellos-item_img {
    @include set_node_whb(70px, 70px);
  }
  .cellos-item_style {
    @include set_font_config(--font-size-tiny, --text-color-gray);
    @include text_overflow(1);
    margin: 4px 0px;
    box-sizing: border-box;
  }
  .cellos-item_id {
    @include set_font_config(--font-size-tiny, --text-color-gray);
    @include text_overflow(1);
  }
  .cellos-item_mask {
    @include set_font_config(--font-size-tiny, --primary-color);
  }
  .after_status {
    width: 120px;
    .colorE6 {
      color: #e63030;
      cursor: pointer;
    }
  }
  .mini_program_logo {
    img {
      width: 12px;
      height: 12px;
      display: block;
      margin-right: 5px;
      margin-bottom: 1px;
      transform: translateX(8px);
    }
  }
  .address_info {
    word-wrap: break-word;
    word-break: break-all;
    @include text_overflow(1);
  }
  .warn {
    color: #e63030 !important;
  }
  .radioGroup {
    .warn {
      border: 1px solid #e63030;
      color: #e63030 !important;
    }
    .warn::before {
      background-color: #e63030;
    }
  }

  .table_row_bottom {
    // height: 52px;
    // line-height: 52px;
    // background: #f3f6fc;
    // padding-left: 20px;
    font-size: 14px;
    color: #404040;
    display: flex;
    // border: 1px solid #eef0f3;
    border-top: none;

    .label {
      color: #404040;
      width: fit-content;
      flex-shrink: 0;
    }
  }
  .item-lable {
    border: 1px solid #fe4d4f;
    color: #fe4d4f;
    border-radius: 4px;
    padding: 2px;
  }
  .refund {
    width: 66px;
    height: 18px;
    font-size: 12px;
    color: #60a13b;
    background: #e5f0df;
    margin-left: 6px;
    border-radius: 4px;
    padding: 4px;
  }
  .reason {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    text-align: justify;
    overflow: hidden;
  }
  .reasonimg {
    width: 100%;
    flex-wrap: wrap;
    .image {
      width: 70px;
      height: 70px;
      display: block;
      margin-bottom: 6px;
    }
  }
  .disabled-btn {
    cursor: not-allowed;
    color: #00000040 !important;
    &:hover {
      color: #00000040 !important;
    }
  }
  .platform-intervene {
    background: #ffeeee;
    border-radius: 2px;
    border: 1px solid #ffbbbc;
    padding: 2px 4px;
    font-size: 12px;
    color: #ff4d4f;
    line-height: 12px;
    display: inline-block;
    box-sizing: border-box;
  }
  :deep(.other_style) {
    width: max-content;
  }
  .other_sale {
    display: flex;
    justify-content: center;
    width: 80px;
  }
</style>
<style>
  .ant-tour {
    width: 314px;
  }
  .ant-tour .ant-tour-inner .ant-tour-header .ant-tour-title {
    font-size: 16px;
  }
  .ant-tour-inner .ant-tour-description {
    font-weight: 400;
    font-size: 14px;
    color: #575757;
    padding-left: 24px !important;
    margin-bottom: 10px;
  }
  .ant-tour .ant-tour-inner .ant-tour-header {
    padding: 20px 18px 16px 24px;
  }
  .ant-tour-target-placeholder {
    padding: 8px 8px;
    box-sizing: border-box;
  }
  .offine-refund-box {
    padding: 2px;
    border-radius: 2px;
    background-color: #e8e8e9;
    color: #636e95;
    margin-right: 4px;
  }
</style>
