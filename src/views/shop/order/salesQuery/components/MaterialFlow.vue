<template>
  <!-- v-if="state.data" -->
  <a-space class="w-full" direction="vertical">
    <div>
      <div v-if="logisticsStatusEnums[state.data?.StateEx]">
        <span style="color: var(--primary-color); padding-right: 10px; font-size: 16px">{{
          logisticsStatusEnums[state.data?.StateEx] || '-'
        }}</span
        ><span>请联系快递公司或者买家核实处理</span>
      </div>
      <div>
        <span style="color: #85878a; padding-right: 10px">快递公司:</span
        ><span>{{
          ['deliverGoods'].includes(data.warpKey) ? data.data.express_name : data.data.logistics_company
        }}</span>
      </div>
      <div>
        <span style="color: #85878a; padding-right: 10px">快递单号:</span
        ><span>{{
          state.data?.LogisticCode ||
          (['deliverGoods'].includes(data.warpKey) ? data.data.express_number : data.data.logistics_number)
        }}</span>
      </div>
    </div>
    <div class="material-flow-content">
      <a-timeline>
        <template v-for="item in state.data?.logisticsTraceDetails">
          <a-timeline-item>
            <template #dot>
              <div class="a-timeline-item-text-dot"></div>
            </template>
            <div class="a-timeline-item-text">
              <!-- <span v-if="item.time != 0">{{ formatDate(item.time) }}</span>
              <span v-if="(!item.time || item.time == 0) && data.data.deliver_time != 0">{{
                formatDate(data.data.deliver_time * 1000)
              }}</span>
              <span> {{ item.desc }}</span> -->
              <span>{{ item.AcceptTime }}</span>
              <span class="ml-10px">{{ item.AcceptStation }}</span>
              <span>{{ data.data.desc }}</span>
            </div>
          </a-timeline-item>
        </template>
        <!-- <a-timeline-item v-for="item in state.materialFlowData?.customs_detail">
                <span>{{ item.time }}</span>
                <span class="ml-10px">{{ item.message }}</span>
              </a-timeline-item> -->
        <a-timeline-item v-if="data.warpKey === 'returnGoods'">
          <template #dot>
            <div class="a-timeline-item-text-dot"></div>
          </template>
          <div class="a-timeline-item-text">
            <span> {{ secToTime(state?.pass_refund_product_time) }}</span>
            <span class="ml-10px">商家同意退货</span>
          </div>
        </a-timeline-item>

        <a-timeline-item v-if="data.warpKey !== 'returnGoods'">
          <template #dot>
            <div class="a-timeline-item-text-dot"></div>
          </template>
          <div class="a-timeline-item-text">
            <span>{{ secToTime(data?.data?.deliver_time) }}</span>
            <span>您的订单已进入第三方卖家仓库，准备出库中</span>
            <!-- <span v-else>您的订单保税仓已接单，仓库正在准备清关发货</span> -->
          </div>
        </a-timeline-item>
        <a-timeline-item v-if="data.warpKey !== 'returnGoods'">
          <template #dot>
            <div class="a-timeline-item-text-dot"></div>
          </template>
          <div class="a-timeline-item-text">
            <span>{{ secToTime(state?.order_create_time) }}</span
            ><span>您提交了订单，等待卖家仓库发货</span>
          </div></a-timeline-item
        >
      </a-timeline>
    </div>
  </a-space>
</template>

<script setup lang="ts">
  import { onMounted, reactive } from 'vue'
  import { get_order_express, get_after_sale_info } from '../index.api'
  import { secToTime } from '@/utils'
  import datas from '@/views/shop/order/order_list/data.ts'
  const { logisticsStatusEnums } = datas()
  const props = defineProps(['data'])
  const state = reactive({
    data: null,
    pass_refund_product_time: undefined,
    order_create_time: undefined
  })
  onMounted(() => {
    initData()
  })
  // 用来获取pass_refund_product_time和order_create_time
  const getInfo = async () => {
    try {
      let { data } = await get_after_sale_info({ after_sale_id: props.data?.data?.id })
      if (data) {
        state.order_create_time = data?.order_create_time
        state.pass_refund_product_time = data?.pass_refund_product_time
      }
    } catch (err) {
      console.log('err——info', err)
    }
  }
  const initData = async () => {
    console.log(props.data)
    getInfo()
    let result = null
    if (!['deliverGoods'].includes(props.data.warpKey)) {
      result = await get_order_express({
        express_num: props.data.data?.logistics_number,
        phone: props.data?.data.return_receive_phone,
        after_id: props.data?.data.id
      })
    } else {
      result = await get_order_express({
        express_num: props.data.data?.express_number,
        phone: props.data.data?.receive_phone,
        order_id: props.data.data?.order_id
      })
    }

    if (result.code === 0) {
      result.data && result.data.logisticsTraceDetails && result.data.logisticsTraceDetails.reverse()
      state.data = {
        ...result.data,
        logisticsTraceDetails: (result.data?.Traces || []).reverse(),
        customs_detail: (result.data?.customs_detail || []).reverse()
      }
    }
    console.log(result)
  }
</script>

<style scoped lang="scss">
  .material-flow-content {
    padding: 20px 10px;
    height: 400px;
    overflow: auto;
  }
  .a-timeline-item-text {
    font-size: var(--font-size-mini);
    color: var(--text-color-gray);
  }
  .a-timeline-item-text-dot {
    position: relative;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #fff;
    border: 3px solid #1677ff;
  }
</style>
