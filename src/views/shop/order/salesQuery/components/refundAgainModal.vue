<template>
  <div v-if="!isPreview">
    <div class="mb-16px tip">
      <ExclamationCircleOutlined />
      <span class="ml-6px">再次发起退款，确认后将发起退款处理</span>
    </div>
    <a-form
      :model="state.form"
      ref="formRef"
      :rules="state.rules"
      :labelCol="{ style: 'width: 100px;' }"
      :colon="false"
    >
      <a-form-item label="退款方式" name="type">
        <a-radio-group v-model:value="state.form.type" @change="handler">
          <a-radio :value="1">线上退回</a-radio>
          <a-radio :value="2">线下退回</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="实际退款金额" name="refund_money" v-if="state.form.type === 2">
        <a-input-number
          :min="0.01"
          :precision="2"
          :controls="false"
          class="w-full"
          v-model:value="state.form.refund_money"
          placeholder="请输入金额"
        />
      </a-form-item>
      <a-form-item label="上传凭证" name="images" v-if="state.form.type === 2">
        <Upload
          v-model="state.form.images"
          multiple
          accept=".jpg,.png,.bmp,.JPG,.PNG,.BMP"
          use="video_speed/original"
          max="4"
          size="2"
        />
        <template #extra>
          <div>媒体图片只支持jpg,png,bmp格式</div>
          <div>最多上传4张图片，文件大小不能超过2M</div>
        </template>
      </a-form-item>
      <a-form-item label="备注" name="refuse_reason" v-if="state.form.type === 2">
        <a-textarea
          v-model:value.trim="state.form.refuse_reason"
          placeholder="最多输入200字"
          :rows="7"
          show-count
          :maxlength="200"
        />
      </a-form-item>
    </a-form>
    <div class="text-right">
      <a-button @click="emits('onEvent', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" :loading="state.loading" @click="submit">确定</a-button>
    </div>
  </div>
  <a-descriptions v-else :column="1" class="description-wrapper">
    <a-descriptions-item label="退款方式">{{
      data.refund_money_type === 1 ? '线上退回' : '线下退回'
    }}</a-descriptions-item>
    <a-descriptions-item label="实际退款金额">¥{{ data.refund_money }}</a-descriptions-item>
    <a-descriptions-item label="上传凭证">
      <div class="img_box" v-for="(item, i) in data.refund_office_image.split(',')" :key="i">
        <a-image class="img" :src="item" />
      </div>
    </a-descriptions-item>
    <a-descriptions-item label="备注">{{ data.refuse_reason || '--' }}</a-descriptions-item>
  </a-descriptions>
</template>
<script lang="ts" setup>
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { returnMoney } from '../../sale_details/index.api'
  import { blow_refund } from '../index.api'
  import { message } from 'ant-design-vue'
  import { ref, reactive } from 'vue'
  const formRef = ref()
  const props = defineProps(['data', 'isPreview'])
  const emits = defineEmits(['onEvent'])
  const state = reactive({
    loading: false,
    form: {
      type: undefined,
      refund_money: props.data.refund_money,
      refuse_reason: undefined,
      images: []
    },
    rules: {
      type: [{ required: true, message: '请选择退款方式', trigger: ['change', 'blur'] }],
      images: [{ required: true, message: '请上传凭证', trigger: ['change', 'blur'] }],
      refund_money: [{ required: true, message: '请输入实际退款金额', trigger: ['change', 'blur'] }],
      refuse_reason: [{ required: false, message: '请输入至少5个字符', trigger: ['change', 'blur'] }]
    }
  })
  const handler = () => {
    // state.form.refund_money = undefined
    state.form.refuse_reason = undefined
    state.form.images = []
  }
  const submit = async () => {
    try {
      state.loading = true
      await formRef.value.validate()
      let res = null
      if (state.form.type === 1) {
        res = await returnMoney({ id: props.data.id, money: props.data.refund_money })
      } else {
        let params = {
          refund_sn: props.data.refund_sn,
          refund_money: parseInt(state.form.refund_money * 100),
          images: state.form.images.map((it: any) => it.url).join(','),
          refuse_reason: state.form.refuse_reason
        }
        res = await blow_refund(params)
      }
      message.success(res?.msg)
      emits('onEvent', { cmd: 'submit' })
    } catch (err) {
      console.log(err)
    } finally {
      state.loading = false
    }
  }
</script>
<style scoped lang="scss">
  .tip {
    width: 100%;
    padding: 6px 10px;
    background: #fff7e8;
    border: 1px solid #ffa013;
    border-radius: 6px;
    font-size: 12px;
    color: #080f1e;
    line-height: 21px;
  }
  .img_box {
    width: 80px;
    height: 80px;
    position: relative;
    border-radius: 6px;
    overflow: hidden;
    margin-right: 16px;
    margin-bottom: 16px;
    .img {
      width: 100%;
      height: 100%;
    }
  }
</style>
