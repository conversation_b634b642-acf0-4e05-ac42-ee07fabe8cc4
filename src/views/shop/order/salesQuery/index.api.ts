import http from '@/utils/request'

export const get_after_sale_list = (data) => {
  return http('get', `/merchant/after-sale/list`, data)
}

export const get_after_sale_count = (data) => {
  return http('get', `/merchant/after-sale/count`, data)
}

// 获取物流信息
export const get_order_express = (data) => {
  return http('get', `/merchant/order/express`, data)
}
// 批量统
export const post_after_sale_batch_return_apply = (data) => {
  return http('post', `/merchant/after-sale/batch-return-apply`, data)
}
export const post_after_sale_edit = (data) => {
  return http('post', `/merchant/after-sale/edit`, data)
}
export const post_batch_confirms = (data) => {
  return http(
    'post',
    `/merchant/after_sale/batch_confirms
`,
    data
  )
}

export const get_after_sale_status = (data) => {
  return http('get', `/merchant/after-sale/status`, data)
}

export const post_exportdata_create = (data) => {
  return http('post', `/public/export-data/create`, data)
}

export const saleStatus = (data) => {
  return http('get', `/merchant/after-sale/status`, data)
}

export const get_order_get_express = (data) => {
  return http('get', `/merchant/order/get-express`, data)
}

export const post_after_sale_address_update = (data) => {
  return http('post', `/merchant/after-sale/address-update`, data)
}

export const post_after_sale_address_confirm = (data) => {
  return http('post', `/merchant/after-sale/confirm`, data)
}

/**
 * 售后原因
 * https://www.apifox.cn/link/project/2014698/apis/api-63125218
 */
export const refundType = (data: any) => {
  return http('get', `/merchant/after-sale/refund-type`, data)
}
//批量校验订单号有效性
export const checkSaleNumsApi = (data) => {
  return http('post', `/merchant/after-sale/check-refund-nums`, data)
}
//批量备注
export const batchRemarkApi = (data) => {
  return http('post', `/merchant/order/batch-remark`, data)
}

// 获取售后详情
export const get_after_sale_info = (data) => {
  return http('get', `/merchant/after-sale/info`, data)
}
/**
 * 获取拒绝原因
 *
 */
export const get_reject_reasons = (data) => {
  return http('post', `/merchant/after_sale/reject_reasons`, data)
}
/**
 * 获取售后原因
 *
 */
export const get_sale_reasons = (data) => {
  return http('post', `/merchant/after_sale/reasons`, data)
}
/**
 * 获取协商原因
 *
 */
export const get_merchant_update_reason_list = (data) => {
  return http('get', `/merchant/after_sale/merchant_update_reason_list`, data)
}
/**
 * 提交商家协商
 *
 */
export const merchant_update_aftersale = (data) => {
  return http('post', `/merchant/after_sale/merchant_update_aftersale`, data)
}
/**
 * 换货拒绝发货
 *
 */
export const refuse_deliver = (data) => {
  return http('post', `/merchant/change_goods/refuse_deliver`, data)
}
/**
 * 上传退款凭证
 *
 */
export const upload_refund_certificate = (data) => {
  return http('post', `/merchant/order/upload_refund_certificate`, data)
}
/**
 * 售后投诉数量统计
 *
 */
export const complaint_num = (data) => {
  return http('get', `/merchant/after_sale/complaint_num`, data)
}
//出现指引后调用
export const helpAddApi = (data: any) => {
  return http('post', `/public/user-help/add`, data)
}
/**
 * 线下退款
 *
 */
export const blow_refund = (data: any) => {
  return http('post', `/public/after-sale/blow-refund`, data)
}
