<template>
  <div>
    <DesTablePage>
      <template #title>
        <div>自动处理流程</div>
      </template>
      <template #extra>
        <a-button type="primary">回复后处理规则</a-button>
        <a-button type="primary" @click="handlerAction('add')">添加</a-button>
      </template>
      <template #search>
        <SearchBaseLayout
          :data="state.searchConfig.data"
          ref="searchFormDataRef"
          @changeValue="changeValue"
          :actions="state.searchConfig.options"
        />
      </template>
      <template #tableWarp>
        <TableZebraCrossing :data="state.tableConfig" @change="pageChange">
          <template #bodyCell="{ scope: { record, column } }">
            <template v-if="column.dataIndex === 'name'">
              <a-tooltip :getPopupContainer="(triggerNode: any) => triggerNode.parentNode" placement="topLeft">
                <template #title>{{ record.name }}</template>
                <span class="text_overflow_row1"> {{ record.name }} </span>
              </a-tooltip>
            </template>
            <template v-if="column.dataIndex === 'description'">
              <a-tooltip :getPopupContainer="(triggerNode: any) => triggerNode.parentNode" placement="topLeft">
                <template #title>{{ record.description }}</template>
                <span class="text_overflow_row1"> {{ record.description }} </span>
              </a-tooltip>
            </template>
            <template v-if="column.dataIndex === 'keywords'">
              <a-tooltip :getPopupContainer="(triggerNode: any) => triggerNode.parentNode" placement="topLeft">
                <template #title>{{ record.keywords }}</template>
                <span class="text_overflow_row1"> {{ record.keywords }} </span>
              </a-tooltip>
            </template>
            <template v-if="column.dataIndex === 'handle'">
              <a-button class="h-auto pa-0" type="link" @click="handlerAction('edit', record)">编辑</a-button>

              <a-popconfirm title="请确认是否删除当前链接？" @confirm="handlerAction('delete', record)">
                <a-button class="h-auto pa-0" type="link">删除</a-button>
              </a-popconfirm>
            </template>
          </template>
        </TableZebraCrossing>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="state.modalConfig.isVisible"
      :title="state.modalConfig.title"
      :width="state.modalConfig.width"
      centered
      closable
      destroyOnClose
      :footer="null"
    >
      <AutoDealRules v-if="['add', 'edit'].includes(state.modalConfig.type)" :item="state.modalConfig.data" />
    </a-modal>
  </div>
</template>
<script setup lang="tsx">
  defineOptions({ name: 'AutoDealProcessing' })
  import { onMounted, reactive, ref } from 'vue'
  import { get_list } from './index.api'
  import { message } from 'ant-design-vue'
  import AutoDealRules from './components/autoDealRules.vue'
  import datas from './data'
  const { searchConfig, tableConfigOptions, modalConfig } = datas()
  const state = reactive({
    initParams: {
      page: 1,
      page_size: 10
    },
    searchConfig,
    tableConfig: tableConfigOptions,
    modalConfig
  })
  // 搜索
  const changeValue = (data: any) => {
    if (data.status) {
      state.initParams = { ...state.initParams, ...data.formData, page: 1 }
    } else {
      state.initParams = {
        page: 1,
        page_size: 10
      }
    }
    initPageData()
  }

  onMounted(() => {
    initPageData()
  })
  const initPageData = async () => {
    try {
      state.tableConfig.loading = true
      // const result: any = await get_list(state.initParams)
      const result = {
        code: 0,
        data: {
          list: [{}]
        }
      }
      if (result.code === 0) {
        if (result.data) {
          state.tableConfig.dataSource = result.data.list || []
          state.tableConfig.pagination.total = result.data.total_num || 0
          state.tableConfig.pagination.current = result.data.page || 1
        } else {
          state.tableConfig.dataSource = []
          state.tableConfig.pagination.total = 0
        }
      }
    } catch (error) {
      console.error(error)
    } finally {
      state.tableConfig.loading = false
    }
  }
  const pageChange = (pagination: any, _filters: any, sorter: any) => {
    console.log('sorter', sorter)

    state.initParams.page = pagination.current
    state.initParams.page_size = pagination.pageSize
    state.tableConfig.pagination.pageSize = pagination.pageSize
    initPageData()
  }
  const handlerAction = (type: string, record?: any) => {
    if (type === 'add' || type === 'edit') {
      state.modalConfig.isVisible = true
      state.modalConfig.type = type
      state.modalConfig.width = 620
      if (type === 'edit') {
        state.modalConfig.data = record
        state.modalConfig.title = '编辑自动处理规则'
      } else {
        state.modalConfig.title = '新增自动处理规则'
        state.modalConfig.data = null
      }
    }
  }
</script>
<style scoped lang="scss"></style>
