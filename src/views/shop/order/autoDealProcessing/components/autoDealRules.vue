<template>
  <div class="form-scroll-wrapper">
    <a-form
      class="form-scroll-box"
      :model="state.form"
      ref="ruleForm"
      :labelCol="{ style: 'width:100px;' }"
      autocomplete="off"
      :colon="true"
      :rules="rules"
    >
      <a-form-item label="规则名称" name="name">
        <a-input v-model:value="state.form.name" :maxlength="20" showCount placeholder="请输入规则名称"></a-input>
      </a-form-item>
      <a-form-item label="规则描述" name="description">
        <a-input
          v-model:value="state.form.description"
          :maxlength="20"
          showCount
          placeholder="请输入规则描述"
        ></a-input>
      </a-form-item>
      <a-form-item label="关键字" required>
        <div class="solid-box">
          <div class="flex-y-center" v-for="(item, index) in state.form.keywords">
            <a-form-item :name="`contain_${index}_select`">
              <a-select
                :show-search="true"
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                v-model:value="item.select"
                :allowClear="false"
                :options="[
                  {
                    label: '半包含',
                    value: 1
                  },
                  {
                    label: '包含',
                    value: 2
                  }
                ]"
              ></a-select>
            </a-form-item>
            <a-form-item :name="`contain_${index}_content`">
              <a-input v-model:value="item.keyword" placeholder="请输入关键字"></a-input>
            </a-form-item>
            <a-button
              type="link"
              class="p-0! mt--16px ml-10px"
              v-if="state.form.keywords.length > 1"
              danger
              @click="handleActions('delete', index)"
            >
              <MinusCircleOutlined class="font-size-16px" />
            </a-button>
            <a-button
              type="link"
              v-if="index === state.form.keywords.length - 1 && state.form.keywords.length <= 10"
              class="p-0! mt--16px ml-10px"
            >
              <PlusCircleOutlined class="font-size-16px" @click="handleActions('add')" />
            </a-button>
          </div>
        </div>
      </a-form-item>
      <a-form-item label="回复内容" name="replyContent">
        <a-input v-model:value="state.form.replyContent" placeholder="请输入回复内容"></a-input>
      </a-form-item>
      <a-form-item label="留言图片" name="imgList">
        <Upload v-model="state.form.imgList" size="2" use="complaint" max="4" accept=".jpeg,.png,.PNG,.JPEG,.bmp" />
        <template #extra>
          <div>媒体图片只支持jpg、png、bmp格式</div>
          <div>最多上传4张图片，文件大小不能超过2M</div>
        </template>
      </a-form-item>
      <a-form-item name="way" label="投诉渠道">
        <a-checkbox-group
          v-model:value="state.form.way"
          :options="[
            {
              label: '微信支付投诉',
              value: 1
            },
            {
              label: '小程序投诉',
              value: 2
            },
            {
              label: '平台投诉',
              value: 3
            }
          ]"
        />
      </a-form-item>
      <a-form-item label="回复后动作" name="action">
        <a-radio-group v-model:value="state.form.action">
          <a-radio :value="1">等待人工介入</a-radio>
          <a-radio :value="2">
            <span>按时间段</span>
            <a-tooltip placement="topLeft">
              <template #title>
                <div>
                  退款提示：<br />
                  会根据用户订单自动生成售后单<br />
                  1. 待发货：仅退款<br />
                  2. 待收货：退货退款<br />
                  3. 已收货：退货退款，待买家退货
                </div>
              </template>
              <ExclamationCircleOutlined class="ml-4px" />
            </a-tooltip>
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item class="ml-100px">
        <div class="flex-y-center">
          <a-radio class="mt--18px" v-model:checked="state.form.hoursradio" :value="1"></a-radio>
          <span class="mt--18px ml-4px mr-4px">自动回复后</span>
          <a-form-item name="hour">
            <a-input-number :controls="false" v-model:value="state.form.hour"></a-input-number>
          </a-form-item>
          <span class="mt--18px ml-4px">小时无新消息，将进行自动触发处理完成</span>
          <a-tooltip placement="topLeft">
            <template #title>
              <div>输入“0”时，则回复后会立即处理完成</div>
            </template>
            <ExclamationCircleOutlined class="ml-4px mt--18px" />
          </a-tooltip>
        </div>
      </a-form-item>
    </a-form>
    <div class="text-right mr-20px">
      <AButton @click="emits('onEvent', { cmd: 'close' })">取消</AButton>
      <AButton type="primary" @click="submitForm" :loading="state.loading">保存</AButton>
    </div>
  </div>
</template>
<script setup lang="ts">
  import type { Rule } from 'ant-design-vue/es/form'
  import { PlusCircleOutlined, MinusCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { reactive, ref, onMounted, nextTick } from 'vue'
  import { debounce } from 'lodash-es'
  import dayjs from 'dayjs'
  import {} from '../index.api'
  import { message } from 'ant-design-vue'
  const rules: Record<string, Rule[]> = {
    name: [{ required: true, message: '请输入规则名称', trigger: ['blur', 'change'] }],
    description: [{ required: false, message: '请输入规则描述', trigger: ['blur', 'change'] }],
    replyContent: [{ required: true, message: '请输入回复内容', trigger: ['blur', 'change'] }],
    imgList: [{ required: true, message: '请上传图片', trigger: ['blur', 'change'] }],
    way: [{ required: true, message: '请选择投诉渠道', trigger: ['blur', 'change'] }],
    action: [{ required: true, message: '请选择回复后动作', trigger: ['blur', 'change'] }],
    hour: [{ required: true, message: '请输入小时', trigger: ['blur', 'change'] }]
  }
  const ruleForm = ref()
  const props = defineProps(['item'])
  const emits = defineEmits(['onEvent'])
  const state = reactive({
    loading: false,
    form: {
      name: undefined,
      description: undefined,
      keywords: [],
      replyContent: undefined,
      imgList: [],
      way: undefined,
      action: undefined,
      hoursradio: 1,
      hour: undefined
    } as any
  })

  const submitForm = async () => {
    try {
      state.loading = true
      await ruleForm.value?.validate()
      let params = {
        ...state.form
      }

      // let res: any = await saveGainCustomerLink(params)
      let res = {
        code: 0,
        data: '',
        msg: '操作成功'
      }
      if (res.code === 0) {
        message.success(res.msg)

        emits('onEvent', { cmd: 'submit' })
      }
    } catch (err) {
      console.log(err)
    } finally {
      state.loading = false
    }
  }
  const handleActions = async (type: string, data?: any) => {
    try {
      if (type === 'save') {
        state.loading = true
        await ruleForm?.value?.validate()
        let params = {
          ...state.form
        }
        // const res: any = await domainAdd(params)
        const res = {
          code: 0,
          msg: '编辑成功'
        }
        if (res.code === 0) {
          state.loading = false
          emits('onEvent', { cmd: 'save' })
        }
      } else if (type === 'add') {
        if (state.form.keywords.some((item) => !item.keyword)) return message.warning('请填写完关键字')
        state.form.keywords.push({ label: undefined, value: undefined })
      } else if (type === 'delete') {
        state.form.keywords.splice(data, 1)
        let list = state.form.keywords.map((_, index) => `contain_${index}_content`)
        nextTick(() => {
          ruleForm?.value?.validate(list)
        })
      }
    } catch (e) {
      console.log(e)
    } finally {
      state.loading = false
    }
  }
  onMounted(async () => {
    try {
      if (props.item?.id) {
        // let res: any = await getLinkInfo({ id: props.item.id })
        let res = {
          code: 0,
          data: {}
        }
        if (res.code === 0) {
          state.form = {
            ...state.form,
            ...res.data
          }

          console.log('state.form', state.form)
        }
      }
    } catch (err) {
      console.log(err)
    }
  })
</script>

<style lang="scss" scoped>
  :deep(.ant-form-item) {
    margin-bottom: 18px;
    .ant-radio-wrapper-checked {
      .span {
        color: var(--primary-color);
      }
    }
    .ant-radio-wrapper,
    .ant-checkbox-wrapper {
      color: #323233;
    }
    .ant-form-item-extra {
      margin-top: 8px;
      font-size: 12px;
    }
    .color-l {
      color: var(--primary-color);
      cursor: pointer;
    }
    .color-r {
      color: rgba(0, 0, 0, 0.25);
      cursor: pointer;
    }
  }

  .down-rules-count-wrapper {
    height: 28px;

    :deep(.ant-input-number) {
      margin-left: 8px;
      margin-right: 8px;
      .ant-input-number-input {
        height: 28px;
        line-height: 28px;
      }
    }
    :deep(.ant-form-item-explain) {
      margin-left: 120px;
    }
  }
  .period-item {
    :deep(.ant-form-item-explain) {
      margin-left: 36px;
    }
  }
</style>
