import { multiply } from 'lodash-es'

export default function datas() {
  // 搜索组件 配置数据
  const searchConfig = {
    data: [
      {
        type: 'input.text',
        field: 'rule_name',
        value: undefined,
        props: {
          placeholder: '请输入规则名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'select',
        field: 'status',
        value: undefined,
        props: {
          placeholder: '请选择投诉渠道',
          multiply: true,
          options: [
            {
              value: 1,
              label: '小程序'
            },
            {
              value: 2,
              label: '微信支付'
            },
            {
              value: 3,
              label: '平台'
            }
          ]
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  }

  // 表格
  const columns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      width: 200
    },
    {
      title: '规则描述',
      key: 'description',
      dataIndex: 'description',
      width: 240
    },
    {
      title: '关键词',
      key: 'keywords',
      dataIndex: 'keywords'
    },
    {
      title: '触发动作',
      key: 'appraise',
      dataIndex: 'appraise',
      width: 250
    },
    {
      title: '投诉渠道',
      key: 'userlink',
      dataIndex: 'userlink',
      width: 250
    },
    {
      title: '添加时间',
      key: 'create_at',
      dataIndex: 'create_at',
      width: 200
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      width: 120
    }
  ]
  const tableConfigOptions = {
    bordered: false,
    loading: false,
    rowKey: 'id',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 2100
    },
    dataSource: [],
    columns,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 10,
      size: 'small',
      showTotal: (total: any) => `共${total}条数据`
    }
  }

  const modalConfig = {
    isVisible: false,
    type: '',
    title: '',
    data: null,
    width: 0
  }
  return {
    searchConfig,
    tableConfigOptions,
    modalConfig
  }
}
