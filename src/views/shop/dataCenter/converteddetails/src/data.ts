import { reactive } from 'vue'

export default function datas() {
  const searchList = reactive([
    {
      type: 'select',
      field: 'channel_type',
      value: null,
      props: {
        placeholder: '请选择投放渠道',

        options: [
          {
            value: 0,
            label: '全部'
          },
          {
            value: 1,
            label: '广点通'
          },
          {
            value: 4,
            label: '磁力引擎'
          },
          {
            value: 6,
            label: '巨量引擎'
          },
          {
            value: 8,
            label: '超级汇川'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'select',
      field: 'type',
      value: null,
      props: {
        placeholder: '请选择商品类型',

        options: [
          {
            value: 0,
            label: '全部'
          },
          {
            value: 1,
            label: '小程序'
          },
          {
            value: 2,
            label: '拼多多'
          },
          {
            value: 3,
            label: '淘宝'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'ad',
      value: undefined,
      props: {
        placeholder: '请输入广告链接或链接名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'product',
      value: undefined,
      props: {
        placeholder: '请输入商品名称或商品ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'tripartite_ad_id',
      value: undefined,
      props: {
        placeholder: '请输入计划ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'select',
      field: 'status',
      value: null,
      props: {
        placeholder: '请选择转化状态',
        options: [
          {
            value: 0,
            label: '全部'
          },
          {
            value: 1,
            label: '未转化'
          },
          {
            value: 2,
            label: '已转化'
          },
          {
            value: 3,
            label: '转化失败'
          },
          {
            value: 4,
            label: '已下单'
          },
          {
            value: 5,
            label: '手动上报'
          },
          {
            value: 8,
            label: '待付款'
          },
          {
            value: 12,
            label: '跳转成功'
          },
          {
            value: 13,
            label: '跳转失败'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'admin',
      field: 'admin_id',
      value: null,
      props: {
        placeholder: '请选择负责人'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'ad_account_id',
      value: undefined,
      props: {
        placeholder: '请输入账户ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'ip',
      value: undefined,
      props: {
        placeholder: '请输入IP'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'order_num',
      value: undefined,
      props: {
        placeholder: '请输入订单编号'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'creative_id',
      value: undefined,
      props: {
        placeholder: '请输入创意ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ])
  const columns = reactive([
    {
      title: '广告/组名称',
      dataIndex: 'ad_name',
      key: 'ad_name',
      width: 220,
      fixed: 'left'
    },
    {
      title: '商品信息',
      dataIndex: 'product_name',
      key: 'product_name',
      // ellipsis: true,
      width: 160
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      key: 'ip',
      width: 140
    },
    {
      title: '地区',
      dataIndex: 'district',
      key: 'district',
      width: 180
    },
    {
      title: '转化状态',
      dataIndex: 'transform_status',
      key: 'transform_status',
      width: 130
    },
    {
      title: '订单信息',
      dataIndex: 'order_deatil',
      key: 'order_deatil',
      width: 260
    },
    {
      title: '设备类型',
      dataIndex: 'device_type',
      key: 'device_type',
      width: 220
    },
    {
      title: '访问时间',
      dataIndex: 'visit_time',
      key: 'visit_time',
      width: 180
    },
    {
      title: '转化时间',
      dataIndex: 'transform_time',
      key: 'transform_time',
      width: 180
    },
    {
      title: '负责人',
      dataIndex: 'admin_name',
      key: 'admin_name',
      width: 120
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      align: 'left',
      width: 120,
      disabled: true
    }
  ])

  const statusData = {
    1: '未转化',
    2: '已转化',
    3: '转化失败',
    4: '已下单',
    5: '手动上报',
    6: '转化黑名单',
    7: '云盾',
    8: '待付款',
    12: '跳转成功',
    13: '跳转失败'
  }
  return { columns, searchList, statusData }
}
