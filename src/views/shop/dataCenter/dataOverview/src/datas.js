import { reactive  } from 'vue'

export default function datas() {
  // 店铺数据
  const flows = reactive([
    {
      desc: '浏览量',
      num: 2871.23,
      percent: '12.3%'
    },
    {
      desc: '被访问商品数',
      num: 25896,
      type: 'down',
      percent: '12.3%'
    },
    {
      desc: '访问-下单转化率',
      num: 1456,
      percent: '12.3%'
    },
    {
      desc: '访问-支付转化率',
      num: 789,
      percent: ''
    }
  ])

  // 店铺数据
  const shopTimes = reactive([
    {
      name: '今日',
      value: 1
    },
    {
      name: '昨日',
      value: 2
    },
    {
      name: '近7日',
      value: 3
    }
  ])
  // 店铺数据 文本提示
  const shopText = [
    {
      id: 1,
      label: '支付金额',
      value: '支付时间在指定时间区间的订单金额总和'
    },
    {
      id: 2,
      label: '支付订单数',
      value: '支付时间在指定时间区间的订单数量'
    },
    {
      id: 3,
      label: 'ROI',
      value: '计算指定时间的投资回报率，计算公式为支付金额/广告消耗=ROI'
    },
    {
      id: 4,
      label: '平均客单',
      value: '指定时间段内的支付金额/支付单数'
    },
    // {
    //   id: 5,
    //   label: '退款金额',
    //   value: '指定时间段内完成退款的售后单退款金额总和（仅退款、退货退款 ）'
    // },
    // {
    //   id: 6,
    //   label: '退款单数',
    //   value: '指定时间段内完成退款的售后单数量（仅退款、退货退款 ）'
    // },
    // {
    //   id: 7,
    //   label: '退款率',
    //   value: '退款单数/支付订单数*100%'
    // },
    {
      id: 5,
      label: '退款金额（退款完成时间）',
      value: '指定时间段内完成退款的售后单退款金额总和（仅退款、退货退款 ），按照退款完成时间进行统计'
    },
    {
      id: 6,
      label: '退款金额（支付时间）',
      value: '指定时间段内完成退款的售后单退款金额总和（仅退款、退货退款 ），按照订单支付时间进行统计'
    },
    {
      id: 7,
      label: '退款单数（退款完成时间）',
      value: '指定时间段内完成退款的售后单数量（仅退款、退货退款 ），按照退款完成时间进行统计'
    },
    {
      id: 8,
      label: '退款单数（支付时间）',
      value: '指定时间段内完成退款的售后单数量（仅退款、退货退款 ），按照订单支付时间进行统计'
    },
    {
      id: 9,
      label: '退款率（退款完成时间）',
      value: '退款单数（退款完成时间）/支付订单数*100%'
    },
    {
      id: 10,
      label: '退款率（支付时间）',
      value: '退款单数（支付时间）/支付订单数*100%'
    },
    {
      id: 11,
      label: '浏览量',
      value: '指定时间段内用户访问店铺内所有商品详情的次数总和，包含广告链接进入和自然流量进入，一个用户访问多次计算为多次'
    },
    
    {
      id: 12,
      label: '访问-下单转化率',
      value: '指定时间段内（访客数/下单人数）*100'
    },
    {
      id: 13,
      label: '访问-支付转化率',
      value: '指定时间段内（访客数/支付人数）*100'
    },
  ]
  // 流量
  const rateTimes = reactive([
    {
      name: '今日',
      value: 1
    },
    {
      name: '昨日',
      value: 2
    },
    {
      name: '近七日',
      value: 3
    }
  ])
  const rateText = [
    {
      id: 1,
      label: '浏览量',
      value:
        '指定时间段内用户访问店铺内所有商品详情的次数总和，包含广告链接进入和自然流量进入，一个用户访问多次计算为多次'
    },
    {
      id: 2,
      label: '被访问商品数',
      value: '指定时间段内商品详情有用户浏览行为的商品数量'
    },
    {
      id: 3,
      label: '访问-下单转化率',
      value: '指定时间段内（访客数/下单人数）*100'
    },
    {
      id: 4,
      label: '访问-支付转化率',
      value: '指定时间段内（访客数/支付人数）*100'
    }
  ]
  const btnsList = [
    {
      btnType: 1,
      label: '全部渠道'
    },
    {
      btnType: 2,
      label: '广点通'
    }
  ]
  const shops = [
    {
      desc: '支付金额(元)',
      num: 0,
      ratio: '',
      is_up: 0
    },
    {
      desc: '支付订单数(个)',
      num: 0,
      ratio: '',
      is_up: 0
    },
    {
      desc: '广告消耗',
      num: 0,
      ratio: '',
      is_up: 0
    },
    {
      desc: 'ROI',
      num: 0,
      ratio: '',
      is_up: 0
    },
    {
      desc: '平均客单(元)',
      num: 0,
      ratio: '',
      is_up: 0
    },
    {
      desc: '退款金额(元)',
      num: 0,
      ratio: '',
      is_up: 0
    },
    {
      desc: '退款单数(个)',
      num: 0,
      ratio: '',
      is_up: 0
    },
    {
      desc: '退款率',
      num: 0,
      ratio: '',
      is_up: 0
    },
    {
      desc: '浏览量',
      num: 0,
      ratio: '',
      is_up: 0
    },
    {
      desc: '被访问商品数',
      num: 0,
      ratio: '',
      is_up: 0
    },
    {
      desc: '访问-下单转化率',
      num: 0,
      ratio: '',
      is_up: 0
    }, 
    {
      desc: '访问-支付转化率',
      num: 0,
      ratio: '',
      is_up: 0
    }
  ]
  const searchData = reactive([
    {
      field: 'department_ids',
      type: 'department',
      value: undefined,
      span: 6,
      props: {
        placeholder: '请选择部门',
        treeDefaultExpandAll: true
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'select',
      field: 'media_type',
      value: undefined,
      props: {
        placeholder: '请选择广告平台',
        options: [
          {
            value: -1,
            label: '全部'
          },
           {
            value: 0,
            label: '自然流量'
          },
          {
            value: 1,
            label: '广点通'
          },
          {
            value: 4,
            label: '磁力引擎'
          },
           {
            value: 6,
            label: '巨量引擎'
          },
          {
            value: 8,
            label: '超级汇川'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'select',
      field: 'product_type',
      value: null,
      props: {
        placeholder: '请选择商品类型',

        options: [
          {
            value: 0,
            label: '全部'
          },
          {
            value: 1,
            label: '小程序'
          },
          {
            value: 2,
            label: '拼多多'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
  ])
  return {
    shops,
    flows,
    shopTimes,
    rateTimes,
    shopText,
    rateText,
    btnsList,
    searchData
  }
}
