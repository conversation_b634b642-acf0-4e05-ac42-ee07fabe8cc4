<template>
  <div class="warp">
    <a-table
      sticky
      :columns="state.fixedColumns"
      :data-source="state.dataSource"
      :pagination="state.pagination"
      :loading="state.loading"
      :scroll="{ x: 2000 }"
      :sortDirections="['descend', 'ascend']"
      @change="pageChange"
      :showSorterTooltip="false"
      bordered
      size="small"
    >
      <template #headerCell="{ column }">
        <template v-if="headerCell.includes(column.dataIndex)">
          <div>
            <span>{{ column.title }}</span>
            <a-tooltip>
              <template #title>{{ column.text }}</template>
              <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
            </a-tooltip>
          </div>
        </template>
        <template v-if="column?.dataIndex === 'admin_name'">
          <a-space>
            <span>{{ column?.title }} </span>
            <span>
              <SetTableColumns
                class="cursor-pointer"
                v-model:data="state.fixedColumns"
                :column="newFixedColumns"
                :isChange="isChange"
              />
            </span>
          </a-space>
        </template>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'admin_name'">
          <div class="item-warp">
            <!-- <div class="item-warp-img">
              <img :src="requireImg('order/o8.png')" alt="" />
            </div> -->
            <div class="item-warp-content">
              <div>
                <a-tooltip>
                  <template #title>{{ record.admin_name || '--' }}</template>
                  <span class="item-warp-content-title">{{ record.admin_name || '--' }}</span>
                </a-tooltip>
              </div>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'department_name'">
          <div class="item-warp">
            <div class="item-warp-content">
              <div>
                <a-tooltip>
                  <template #title>{{ record.department_name || '--' }}</template>
                  <span class="item-warp-content-title">{{ record.department_name || '--' }}</span>
                </a-tooltip>
              </div>
            </div>
          </div>
        </template>

        <template v-if="column.dataIndex === 'cost'">
          <span>¥ {{ centsToYuan(record.cost) }}</span>
        </template>
        <template v-if="column.dataIndex === 'thousand_display_price'">
          <span>¥ {{ centsToYuan(record.thousand_display_price) }}</span>
        </template>
        <template v-if="column.dataIndex === 'click_order_amount_avg'">
          <span>¥ {{ centsToYuan(record.click_order_amount_avg) }}</span>
        </template>
        <template v-if="column.dataIndex === 'product_loss_amount'">
          <span>¥ {{ centsToYuan(record.product_loss_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_amount_avg'">
          <span>¥ {{ centsToYuan(record.order_amount_avg) }}</span>
        </template>
        <template v-if="column.dataIndex === 'conversions_cost'">
          <span>¥ {{ centsToYuan(record.conversions_cost) }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_amount'">
          <span>¥ {{ centsToYuan(record.order_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_amount_turnover'">
          <span>¥ {{ centsToYuan(record.order_amount_turnover) }}</span>
        </template>
        <template v-if="column.dataIndex === 'loss_roi'">
          <span class="loss_roi"> {{ record.loss_roi }}</span>
        </template>
        <template v-if="column.dataIndex === 'refund_amount'">
          <span>¥ {{ centsToYuan(record.refund_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'pay_time_refund_amount'">
          <span>¥ {{ centsToYuan(record.pay_time_refund_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_cost'">
          <span>¥ {{ centsToYuan(record.order_cost) }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_turnover_cost'">
          <span>¥ {{ centsToYuan(record.order_turnover_cost) }}</span>
        </template>

        <template v-if="column.dataIndex === 'refund_rate'">
          <span>{{ record.refund_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'pay_time_refund_rate'">
          <span>{{ record.pay_time_refund_rate }}%</span>
        </template>

        <template v-if="column.dataIndex === 'conversions_rate'">
          <span>{{ record.conversions_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'ctr'">
          <span>{{ record.ctr }}%</span>
        </template>

        <template v-if="column.dataIndex === 'video_outer_play3s_rate'">
          <span>{{ record.video_outer_play3s_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'video_outer_play25_rate'">
          <span>{{ record.video_outer_play25_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'video_outer_play50_rate'">
          <span>{{ record.video_outer_play50_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'video_outer_play75_rate'">
          <span>{{ record.video_outer_play75_rate }}%</span>
        </template>
      </template>

      <template #summary>
        <a-table-summary fixed v-if="state.dataSource.length">
          <a-table-summary-row>
            <template v-for="(item, index) in state.fixedColumns">
              <template v-if="['admin_name'].includes(item.dataIndex)">
                <a-table-summary-cell :index="index">总计：{{ state.pagination.total }}</a-table-summary-cell>
              </template>
              <template v-else
                ><a-table-summary-cell :index="index">
                  <span v-if="percentData.includes(item.dataIndex)">{{ state?.sum[item.dataIndex] }}%</span>
                  <span v-else>{{ setFider(setFilder, item.dataIndex) }}</span>
                </a-table-summary-cell></template
              >
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </a-table>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  import { get_statement_user_latitude, get_after_sale_dimension } from '../index.api'
  import { requireImg, centsToYuan } from '@/utils'
  import datas from '../data'
  import moment from 'moment'
  import { useApp } from '@/hooks'
  import { cloneDeep } from 'lodash-es'
  const { isMobile } = useApp()
  const emits = defineEmits(['eventBtn', 'getDimension'])
  const { setFilder, percentData, headerCell } = datas()

  const after_sale_dimension = ref(1)
  const newFixedColumns = ref([])

  const fixedColumns = ref([
    {
      title: '姓名',
      dataIndex: 'admin_name',
      disabled: true,
      fixed: isMobile.value ? false : 'left',
      width: 140
    },
    // {
    //   title: '所属部门',
    //   dataIndex: 'department_name',
    //   width: 160
    // },
    {
      title: '总花费',
      dataIndex: 'cost',
      width: 160,
      text: '广告投放总共付出的费用成本',
      sorter: true
    },
    {
      title: '展示数',
      dataIndex: 'view_count',
      width: 120,
      sorter: true
    },
    {
      title: '千次展示均价',
      dataIndex: 'thousand_display_price',
      width: 140,
      text: '总消耗/曝光量*1000',
      sorter: true
    },
    {
      title: '点击数',
      dataIndex: 'valid_click_count',
      width: 100,
      text: '有效且被计费的点击次数',
      sorter: true
    },
    {
      title: '点击率',
      dataIndex: 'ctr',
      width: 100,
      text: '点击量/曝光量*100%',
      sorter: true
    },
    {
      title: '平均点击单价',
      dataIndex: 'click_order_amount_avg',
      width: 140,
      sorter: true
    },
    {
      title: '平均客单价',
      dataIndex: 'order_amount_avg',
      width: 140,
      text: '支付总金额/支付订单数',
      sorter: true
    },
    {
      title: '转化数',
      dataIndex: 'conversions_count',
      width: 100,
      sorter: true
    },
    {
      title: '转化成本',
      dataIndex: 'conversions_cost',
      width: 120,
      text: '总消耗/转化数',
      sorter: true
    },
    {
      title: '转化率',
      dataIndex: 'conversions_rate',
      width: 100,
      text: '转化数/点击量',
      sorter: true
    },
    {
      title: '支付订单数',
      dataIndex: 'order_count',
      width: 140,
      text: '支付成功的订单数量',
      sorter: true
    },
    {
      title: '支付总金额',
      dataIndex: 'order_amount',
      width: 140,
      text: '支付成功订单的总金额',
      sorter: true
    },
    {
      title: '支付ROI',
      dataIndex: 'roi',
      width: 110,
      sorter: true
    },
    {
      title: '成交订单数',
      dataIndex: 'order_count_turnover',
      width: 140,
      text: '支付订单数-退款量',
      sorter: true
    },
    {
      title: '成交总金额',
      dataIndex: 'order_amount_turnover',
      width: 140,
      text: '支付总金额-退款总金额',
      sorter: true
    },
    {
      title: '盈亏ROI',
      dataIndex: 'loss_roi',
      width: 120,
      text: '（总盈亏-成交总金额）/总消耗',
      sorter: true
    },
    {
      title: '总盈亏',
      dataIndex: 'product_loss_amount',
      width: 100,
      text: '成交总金额-总消耗*盈亏ROI',
      sorter: true
    },
    {
      title: '成交ROI',
      dataIndex: 'turnover_roi',
      width: 120,
      text: '成交总金额/总消耗',
      sorter: true
    },
    {
      title: '退单量',
      dataIndex: 'refund_count',
      width: 210,
      text: '指定时间段内完成退款的售后单数量（仅退款、退货退款 ），按照退款完成时间进行统计',
      sorter: true
    },
    {
      title: '退单量',
      dataIndex: 'pay_time_refund_count',
      width: 180,
      text: '指定时间段内完成退款的售后单数量（仅退款、退货退款 ），按照订单支付时间进行统计',
      sorter: true
    },
    {
      title: '退款总金额',
      dataIndex: 'refund_amount',
      width: 240,
      text: '指定时间段内完成退款的售后单退款金额总和（仅退款、退货退款 ），按照退款完成时间进行统计',
      sorter: true
    },
    {
      title: '退款总金额',
      dataIndex: 'pay_time_refund_amount',
      width: 220,
      text: '指定时间段内完成退款的售后单退款金额总和（仅退款、退货退款 ），按照订单支付时间进行统计',
      sorter: true
    },
    {
      title: '退单率',
      dataIndex: 'refund_rate',
      width: 210,
      text: '退款单数（退款完成时间）/支付订单数*100%',
      sorter: true
    },
    {
      title: '退单率',
      dataIndex: 'pay_time_refund_rate',
      text: '退款单数（支付时间）/支付订单数*100%',
      width: 200,
      sorter: true
    },
    {
      title: '播放量',
      dataIndex: 'video_outer_play_count',
      width: 100
    },
    {
      title: '3秒播放数',
      dataIndex: 'video_outer_play3s_count',
      width: 140
    },
    {
      title: '3秒播放率',
      dataIndex: 'video_outer_play3s_rate',
      width: 140
    },
    {
      title: '25%进度播放数',
      dataIndex: 'video_outer_play25_count',
      width: 160
    },
    {
      title: '25%进度播放率',
      dataIndex: 'video_outer_play25_rate',
      width: 160
    },
    {
      title: '50%进度播放数',
      dataIndex: 'video_outer_play50_count',
      width: 160
    },
    {
      title: '50%进度播放率',
      dataIndex: 'video_outer_play50_rate',
      width: 160
    },
    {
      title: '75%进度播放数',
      dataIndex: 'video_outer_play75_count',
      width: 160
    },
    {
      title: '75%进度播放率',
      dataIndex: 'video_outer_play75_rate',
      width: 160
    },

    {
      title: '下单成本',
      dataIndex: 'order_cost',
      width: 120
    },
    {
      title: '成交成本',
      dataIndex: 'order_turnover_cost',
      width: 120
    }
  ])

  const state = reactive({
    fixedColumns: cloneDeep(newFixedColumns),
    loading: true,
    dataSource: [],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 10,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    },
    sum: {},
    initQuery: {
      page: 1,
      page_size: 10,
      begin_time: moment().format('YYYY-MM-DD'),
      end_time: moment().format('YYYY-MM-DD'),
      field: undefined,
      after_sale_dimension: undefined,
      order: undefined
    },
    filtration: null
  })
  const setFider = (arr, key) => {
    if (arr.includes(key)) {
      return `¥ ${centsToYuan(state?.sum[key])}`
    } else {
      return state?.sum[key]
    }
  }
  const initPage = async (data) => {
    state.loading = true
    if (data) {
      tabColumns(data?.after_sale_dimension)
    }
    state.filtration = data ? data : state.filtration
    const result = await get_statement_user_latitude({ ...state.initQuery, ...state.filtration })
    state.dataSource = result?.data?.list || []
    state.pagination.pageSize = result?.data?.size || 10
    state.pagination.total = result?.data?.total_num || 0
    state.pagination.current = result?.data?.page || 1
    state.sum = result?.data?.sum || {}
    state.loading = false
  }

  function createHash(hashLength = 10) {
    return Array.from(Array(Number(hashLength) || 24), () => Math.floor(Math.random() * 36).toString(36)).join('')
  }
  const isChange = ref(createHash())

  const tabColumns = (type) => {
    type = type ? type : state.initQuery.after_sale_dimension
    const pay_time_arr = ['pay_time_refund_rate', 'pay_time_refund_count', 'pay_time_refund_amount']
    const refund_time_arr = ['refund_rate', 'refund_count', 'refund_amount']
    const filterArr = type == 1 ? pay_time_arr : refund_time_arr
    newFixedColumns.value = fixedColumns.value.filter((item) => !filterArr.includes(item.dataIndex))
    state.fixedColumns = newFixedColumns.value
    isChange.value = createHash()
  }

  const pageChange = (data, filters, sorter) => {
    if (sorter?.order) {
      state.initQuery.field = sorter.field
      state.initQuery.order = sorter.order === 'ascend' ? 'asc' : 'desc'
    } else {
      state.initQuery.field = undefined
      state.initQuery.order = undefined
    }
    state.initQuery.page = data.current
    state.initQuery.page_size = data.pageSize
    state.pagination.current = data.current
    if (state.filtration) {
      state.filtration.page = data.current
    }

    initPage()
  }
  onMounted(async () => {
    const data = await get_after_sale_dimension()
    state.initQuery.after_sale_dimension = data.data
    after_sale_dimension.value = data.data
    tabColumns(after_sale_dimension.value)
    emits('getDimension', data.data)

    initPage()
  })
  defineExpose({
    initPage
  })
</script>

<style scoped lang="scss">
  @import '../../../../../assets/css/mixin_scss_fn';
  .warp {
    :deep(.ant-table-column-sorter) {
      color: #939599;
    }
    :deep(.ant-table-wrapper .ant-table-column-sorters:hover .ant-table-column-sorter) {
      color: #939599;
    }
    :deep(.ant-table-wrapper .ant-table) {
      border-radius: 4px !important;
      overflow: hidden;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table-thead > tr > th) {
      font-weight: 400;
      background: #f8f8f9;
      color: #313233;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-tbody > tr > td) {
      vertical-align: middle;
      color: #313233;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table.ant-table-bordered .ant-table-tbody > tr > td) {
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td) {
      background-color: #f7f9fc;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-summary td) {
      background: #f7f9fc;
      border-color: #e8e9ec !important;
    }
  }
  .item-warp {
    width: 100%;
    display: flex;
    .item-warp-img {
      width: 15px;
      height: 15px;
      margin-top: 3px;
      margin-right: 5px;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
    .item-warp-content {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
      .item-warp-content-title {
        @include text_overflow(1);
      }
      .item-warp-content-code {
        color: var(--text-color-5);
      }
    }
  }
  .actions-warp {
    margin-bottom: 0px !important;
  }
  .loss_roi {
    color: #1677ff;
  }
  :deep(.ant-table-sticky-scroll) {
    display: none;
  }
</style>
