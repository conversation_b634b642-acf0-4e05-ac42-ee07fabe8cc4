import { requireImg } from '@/utils'
import moment from 'moment'
import dayjs from 'dayjs'
import { ref } from 'vue'
export default function datas() {
  const disabledDate = (current: any) => {
    const today = dayjs()
    const thirtyDaysAgo = today.subtract(30, 'day')
    const decemberTwentyFirst = dayjs('2023-12-1').startOf('day')
    current = current.startOf('day')
    return (
      current.isBefore(decemberTwentyFirst, 'day') ||
      current.isBefore(thirtyDaysAgo, 'day') ||
      current.isAfter(today, 'day')
    )
  }
  const searchList = [
    {
      type: 'select',
      field: 'media_types',
      value: undefined,
      props: {
        placeholder: '请选择广告平台',
        mode: 'multiple',
        options: [
          {
            value: 1,
            label: '广点通'
          },
          {
            value: 4,
            label: '磁力引擎'
          },
          {
            value: 6,
            label: '巨量引擎'
          },
          {
            value: 0,
            label: '自然流量'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'select',
      field: 'product_type',
      value: null,
      props: {
        placeholder: '请选择商品类型',

        options: [
          {
            value: -1,
            label: '全部'
          },
          {
            value: 1,
            label: '小程序'
          },
          {
            value: 2,
            label: '拼多多'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'product_code',
      value: undefined,
      props: {
        placeholder: '请输入商品ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'product_name',
      value: undefined,
      props: {
        placeholder: '请输入商品名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      field: 'category_id',
      type: 'cascader',
      value: [],
      span: 6,
      props: {
        placeholder: '请选择商品类目',
        options: [],
        filterable: true,
        fieldNames: {
          label: 'name',
          value: 'id'
        },
        changeOnSelect: true
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'product_remark',
      value: undefined,
      props: {
        placeholder: '请输入商品备注'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    // {
    //   field: 'type',
    //   type: 'select',
    //   value: 1,
    //   props: {
    //     options: [
    //       {
    //         label: '广点通',
    //         value: 1
    //       }
    //     ],
    //     placeholder: '请选择媒体类型',
    //     allowClear: false
    //   },
    //   layout: {
    //     xs: 24,
    //     sm: 12,
    //     md: 8,
    //     lg: 8,
    //     xl: 8,
    //     xxl: 4
    //   }
    // },
    {
      type: 'admin',
      field: 'admin_id',
      value: null,
      props: {
        placeholder: '请选择负责人'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'date',
      field: 'created_at',
      value: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
      range: 'NearlyThirty',
      props: {
        placeholder: ['开始时间', '结束时间'],
        disabledDate: false,
        allowClear: false
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'select',
      field: 'after_sale_dimension',
      value: 1,
      props: {
        placeholder: '请选择统计维度',
        allowClear: false,
        options: [
          {
            value: 1,
            label: '退款完成时间'
          },
          {
            value: 2,
            label: '支付时间'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ]

  const tabs = [
    {
      id: 1,
      icon: requireImg('order/o8.png'),
      noicon: requireImg('order/star_drak.png'),
      title: '广点通'
    }
    // {
    //   id: 2,
    //   icon: requireImg('order/o1.png'),
    //   noicon: requireImg('order/star_bright.png'),
    //   title: '磁力引擎'
    // }
  ]

  const iconType = (type) => {
    let status = {
      4: requireImg('order/o1.png'),
      1: requireImg('order/o8.png')
    }
    return status[type]
  }

  const modalConfigData = {
    open: false,
    title: '',
    type: '',
    data: null
  }

  const modalTitle = {
    SetProfitLoss: '设置盈亏ROI',
    PeriodReport: '时段报表',
    Advertisement: '广告投放',
    ReplaceData: '替换链接',
    ReplaceRecord: '替换记录',
    ReplaceDetail: '替换记录详情',
    suggestion: '优化建议',

    ReplaceLink: '替换链接'
  }

  const setModalWidth = (type) => {
    if (['PeriodReport'].includes(type)) return '60%'
    if (['Advertisement', 'PromotedAccount'].includes(type)) return 1100
    if (['ReplaceLink'].includes(type)) return 800
    if (['ReplaceData'].includes(type)) return 660
    if (['suggestion'].includes(type)) return 680

    if (['ReplaceRecord'].includes(type)) return 900
    return 520
  }

  const adSearch = ref([
    {
      type: 'input.text',
      field: 'name',
      value: undefined,
      props: {
        placeholder: '请输入广告名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'input.text',
      field: 'ad_account_id',
      value: undefined,
      props: {
        placeholder: '请输入广告账户ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      field: 'jump_type',
      type: 'select',
      value: -1,
      span: 6,
      props: {
        options: [
          {
            label: '全部跳转类型',
            value: -1
          },
          {
            label: '小程序落地页',
            value: 0
          },
          {
            label: 'H5落地页',
            value: 1
          }
        ],
        placeholder: '请选择跳转类型'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      field: 'style_type',
      type: 'select',
      value: undefined,
      span: 6,
      props: {
        options: [
          {
            label: '大众版',
            value: 1
          },
          {
            label: '老年版',
            value: 2
          },
          {
            label: '淘宝版',
            value: 3
          },
          {
            label: '拼多多版',
            value: 4
          }
        ],
        mode: 'multiple',
        maxTagCount: 'responsive',
        showArrow: true,
        placeholder: '请选择落地页版本'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  ])

  const tableSummaryList = () => {
    const arr = []
    for (let i = 2; i < 27; i++) {
      arr.push(i)
    }
    return arr
  }

  const setFilder = [
    'order_amount',
    'order_amount_turnover',
    'cost',
    'thousand_display_price',
    'cpc',
    'product_loss_amount',
    'order_amount_avg',
    'conversions_cost',
    'refund_amount',
    'pay_time_refund_amount',
    'click_order_amount_avg',
    'not_callback_amount'
  ]
  const headerCell = [
    'refund_rate',
    'create_order_rate',
    'refund_amount',
    'pay_time_refund_amount',
    'refund_count',
    'conversions_rate',
    'conversions_cost',
    'order_amount_avg',
    'ctr',
    'arrival_rate',
    'pay_time_refund_rate',
    'pay_time_refund_count',
    'order_conversion_rate',
    'pay_conversion_rate',
    'thousand_display_price',
    'product_loss_amount',
    'product_loss_roi',
    'turnover_roi',
    'order_amount_turnover',
    'order_count_turnover',
    'order_amount',
    'order_count',
    'weixin_num',
    'mini_num',
    'platform_num',
    'complaint_rate',
    'not_callback_count',
    'not_callback_amount'
  ]

  return {
    searchList,
    tabs,
    iconType,
    modalConfigData,
    modalTitle,
    setModalWidth,
    adSearch,
    tableSummaryList,
    setFilder,
    headerCell
  }
}
