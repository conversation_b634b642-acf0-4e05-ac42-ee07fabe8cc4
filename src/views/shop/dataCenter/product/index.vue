<template>
  <DesTablePage class="page_main common_page_warp">
    <template #title>
      <div>商品报表</div>
    </template>
    <template #extra>
      <div>
        <a-button
          class="SvgIconicon-warp"
          size="small"
          style="height: 28px"
          @click="derive('invest_report_product_latitude_v2')"
        >
          <div class="flex flex-items-center">
            <SvgIcon icon="down" class="font-size-12px mr-4px" />
            <span>汇总导出</span>
          </div>
        </a-button>
        <a-button
          class="SvgIconicon-warp"
          size="small"
          style="height: 28px"
          @click="derive('invest_report_product_latitude_day_v2')"
        >
          <div class="flex flex-items-center">
            <SvgIcon icon="down" class="font-size-12px mr-4px" />
            <span>分日导出</span>
          </div>
        </a-button>
        <a-button
          v-if="state.exportdata.media_types?.includes(1)"
          class="SvgIconicon-warp"
          size="small"
          type="primary"
          style="height: 28px"
          @click="eventBtnItem({ type: 'ReplaceLink', data: '替换链接', row: undefined, batch: true })"
        >
          批量替换链接
        </a-button>
      </div>
    </template>
    <template #tableWarp>
      <SearchBaseLayout
        ref="searchFormDataRef"
        :data="searchList"
        @changeValue="changeValue"
        @selectedDate="selectedDate"
        @onOpenChange="onOpenChange"
        :btnNames="['refresh']"
        :actions="actions"
      />
      <div class="pb-4"></div>

      <Report ref="tableRef" v-if="tabs[state.curr].id === 1" @eventBtn="eventBtnItem" @getDimension="getDimension" />

      <a-modal
        :width="setModalWidth(state.modalConfigData.type)"
        centered
        :maskClosable="false"
        :destroyOnClose="true"
        :wrapClassName="[state.modalConfigData.type === 'suggestion' ? 'goods-suggestion' : '']"
        v-model:open="state.modalConfigData.open"
        @cancel="onCancel"
      >
        <template #title>
          <div class="flex-y-center justify-between">
            <div class="flex flex-items-baseline">
              {{ ['PeriodReport'].includes(state.modalConfigData.type) ? '时段报表' : state.modalConfigData.title }}
              <div v-if="['PeriodReport'].includes(state.modalConfigData.type)" class="font-size-12px font-400 flex">
                （商品名称：
                <a-tooltip popper-class="toolt" placement="topLeft" :overlayInnerStyle="{ maxWidth: '300px' }">
                  <template #title>{{ state.modalConfigData.data?.product_name }}</template>
                  <div class="c-#7A869F text_overflow max-w-270px mr-3px">
                    {{ state.modalConfigData.data?.product_name }}
                  </div>
                </a-tooltip>

                商品ID：
                <div class="c-#7A869F">{{ state.modalConfigData.data?.product_code }})</div>
              </div>
            </div>
            <a-date-picker
              v-if="['PeriodReport'].includes(state.modalConfigData.type)"
              v-model:value="state.preriodDate"
              class="mr-25px"
              @change="onChangeDate"
            />
          </div>
        </template>
        <template #footer>
          <a-button
            v-if="!['PeriodReport', 'suggestion', 'Advertisement'].includes(state.modalConfigData.type)"
            @click="closeModal"
            >取消</a-button
          >
          <a-button
            v-if="!['PeriodReport', 'suggestion', 'Advertisement'].includes(state.modalConfigData.type)"
            type="primary"
            @click="okModal"
            >确定</a-button
          >
        </template>
        <SetProfitLoss
          ref="formRef"
          v-if="['SetProfitLoss'].includes(state.modalConfigData.type)"
          :data="state.modalConfigData.data"
        />
        <PeriodReport
          ref="periodtRef"
          v-if="['PeriodReport'].includes(state.modalConfigData.type)"
          :data="state.modalConfigData.data"
        />
        <Advertisement
          v-if="['Advertisement'].includes(state.modalConfigData.type)"
          :data="state.modalConfigData.data"
          :date="state.date"
        />
        <PromotedAccount
          v-if="['PromotedAccount'].includes(state.modalConfigData.type)"
          :data="state.modalConfigData.data"
        />
        <ReplaceLink
          :item="state.modalConfigData.item"
          ref="replaceRef"
          :ids="state.checkIds"
          v-if="['ReplaceLink'].includes(state.modalConfigData.type)"
        />
        <ReplaceData
          :item="state.modalConfigData.item"
          ref="adRef"
          v-if="['ReplaceData'].includes(state.modalConfigData.type)"
        />
        <ReplaceRecord
          :item="state.modalConfigData.data"
          :type="3"
          ref="adRef"
          v-if="['ReplaceRecord'].includes(state.modalConfigData.type)"
        />
        <GoodsSuggestion
          v-if="['suggestion'].includes(state.modalConfigData.type)"
          type="report"
          :goodsDetail="state.modalConfigData.data"
          @event="closeModal"
        />
      </a-modal>
    </template>
  </DesTablePage>
</template>

<script setup>
  import Report from './components/Report.vue'
  import SetProfitLoss from './components/SetProfitLoss.vue'
  import PeriodReport from './components/PeriodReport.vue'
  import Advertisement from './components/Advertisement.vue'
  import PromotedAccount from './components/PromotedAccount.vue'
  import datas from './data.ts'

  import ReplaceLink from '../components/ReplaceLink.vue'
  import ReplaceData from '../components/ReplaceData.vue'
  import ReplaceRecord from '../components/ReplaceRecord.vue'
  import GoodsSuggestion from '@/views/shop/goods/goodList/components/GoodsSuggestion.vue'

  import { VerticalAlignBottomOutlined } from '@ant-design/icons-vue'
  import { Modal, message } from 'ant-design-vue'
  import { reactive, ref, createVNode } from 'vue'
  import { set_edit_conf_roi } from './index.api'
  import moment from 'moment'
  import { post_exportdata_create } from '../../order/batchShipment/index.api'
  import { setCategoryList } from '../../goods/goodList/index.api'
  import { chagne_link } from '../index.api'
  import { localStg } from '../../../../utils'
  import { useDownloadCenter } from '@/hooks'
  import dayjs from 'dayjs'

  const searchFormDataRef = ref()
  const after_sale_dimension = ref(1)
  const dates = ref()
  const { searchList, tabs, modalConfigData, modalTitle, setModalWidth } = datas()
  const { goCenter } = useDownloadCenter()
  const tableRef = ref(null)
  const formRef = ref(null)
  const periodtRef = ref(null)
  const replaceRef = ref(null)
  const state = reactive({
    curr: 0,
    modalConfigData,
    hourDate: '',
    preriodDate: dayjs(moment().format('YYYY-MM-DD')),
    exportdata: {
      begin_time: moment().format('YYYY-MM-DD'),
      end_time: moment().format('YYYY-MM-DD')
    },
    checkIds: [],
    date: {
      begin_time: moment().format('YYYY-MM-DD'),
      end_time: moment().format('YYYY-MM-DD')
    }
  })
  const selectItem = (index) => {
    state.curr = index
  }

  const eventBtnItem = (data) => {
    if (data?.batch) {
      state.checkIds = tableRef.value.getCheckIds()
      if (!state.checkIds.length) {
        return message.warning('请选择要替换的计划')
      }
    } else {
      state.checkIds = [data.data.product_id]
    }
    state.modalConfigData.type = data.type
    state.modalConfigData.data = data.data
    state.modalConfigData.title = modalTitle[data.type]
    state.modalConfigData.open = true
  }
  const okModal = async () => {
    if (['SetProfitLoss'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.onFinish()
      const result = await set_edit_conf_roi({
        id: state.modalConfigData.data.product_id,
        media_type: state.modalConfigData.data.media_type,
        loss_roi: +values.loss_roi
      })
      if (result.code === 0) {
        closeModal()
        tableRef.value.initPage({
          page: 1,
          begin_time: state.exportdata.begin_time,
          end_time: state.exportdata.end_time
        })
      }
    }
    if (['ReplaceLink'].includes(state.modalConfigData.type)) {
      const values = await replaceRef.value.validateFields()
      if (!values.ad_id) {
        return message.warning('请选择广告链接')
      }
      console.log(values, 'values')

      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '确认更换，更换后广告将重新进行审核?'),
        async onOk() {
          const res = await chagne_link({
            ad_id: values.ad_id,
            ad_url: values.ad_url,
            type: 3,
            ids: state.checkIds.join(',')
          })
          message.success(res.msg)
          closeModal()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    }
  }
  const closeModal = () => {
    state.modalConfigData.type = ''
    state.modalConfigData.data = null
    state.modalConfigData.title = ''
    state.modalConfigData.open = false
  }

  const changeValue = (values) => {
    if (values?.status) {
      const obj = JSON.parse(JSON.stringify(values.formData))
      delete obj.created_at
      if (values.type === 'refresh') {
        tableRef.value.initPage({
          begin_time: values?.formData?.created_at[0],
          end_time: values?.formData?.created_at[1],
          ...obj,
          media_types:
            values?.formData.media_types && values?.formData.media_types.length != 0
              ? values?.formData.media_types.join(',')
              : ''
        })
      } else {
        tableRef.value.initPage({
          begin_time: values?.formData?.created_at[0],
          end_time: values?.formData?.created_at[1],
          ...obj,
          media_types:
            values?.formData.media_types && values?.formData.media_types.length != 0
              ? values?.formData.media_types.join(',')
              : '',
          category_id: values.formData.category_id
            ? values.formData.category_id[values.formData.category_id.length - 1]
            : '',
          category_level: values.formData.category_id?.length,
          page: 1
        })
      }
      state.exportdata = {
        begin_time: values?.formData?.created_at[0],
        end_time: values?.formData?.created_at[1],
        ...obj,
        media_types:
          values?.formData.media_types && values?.formData.media_types.length != 0
            ? values?.formData.media_types.join(',')
            : ''
      }
      state.preriodDate = dayjs(values?.formData?.created_at[0])
    } else {
      // searchFormDataRef.value.formData.after_sale_dimension = 1
      searchFormDataRef.value.formData.after_sale_dimension = after_sale_dimension.value
      tableRef.value.initPage({
        page: 1,
        begin_time: moment().format('YYYY-MM-DD'),
        end_time: moment().format('YYYY-MM-DD'),
        type: 1
      })
      ;(values.formData.type = 1),
        (values.formData.created_at = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')])
      state.exportdata = {
        begin_time: moment().format('YYYY-MM-DD'),
        end_time: moment().format('YYYY-MM-DD'),
        type: 1
      }
    }
    state.date.begin_time = values.formData.created_at[0]
    state.date.end_time = values.formData.created_at[1]
  }
  let oldDate
  // 打开日期选择弹窗
  const onOpenChange = (open) => {
    if (open) {
      oldDate = JSON.parse(JSON.stringify(searchFormDataRef.value.formData.created_at))
      dates.value = []
      searchFormDataRef.value.formData.created_at = []
    } else {
      const hasDate = dates.value.every((item) => item)
      if (!dates.value.length || !hasDate) searchFormDataRef.value.formData.created_at = oldDate
    }
  }
  const onChangeDate = (date, dateString) => {
    console.log(date, 'onChangeDate')
    state.hourDate = dateString
    periodtRef.value.initPage(state.hourDate)
  }
  // 日期选择
  const selectedDate = (val) => {
    dates.value = val
  }
  const disabledDate = (current) => {
    const decemberTwentyFirst = dayjs('2023-12-1').startOf('day')
    if (current.isBefore(decemberTwentyFirst, 'day')) {
      return true
    }
    if (!dates.value || dates.value.length === 0) {
      return false
    }

    const tooLate = dates.value[0] && current.diff(dates.value[0], 'days') > 30
    const tooEarly = dates.value[1] && dayjs(dates.value[1]).diff(current, 'days') > 30
    return tooLate || tooEarly
  }
  const shopForm = searchList.find((item) => item.field == 'created_at')
  if (shopForm && shopForm.props) {
    shopForm.props.disabledDate = disabledDate
  }
  // 获取商品类目列表
  const getCategorlList = async () => {
    try {
      let res = await setCategoryList({})
      searchList.forEach((v) => {
        if (v.field == 'category_id') {
          v.props.options = res.data || []
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  getCategorlList()

  const getDimension = (data) => {
    searchFormDataRef.value.formData.after_sale_dimension = data
    after_sale_dimension.value = data
  }

  // 导出
  const derive = async (type) => {
    const result = await post_exportdata_create({
      type,
      params: JSON.stringify({
        shop_id: localStg.get('shopInfo').id,
        ...state.exportdata,
        product_code: state.exportdata?.product_code,
        category_id: state.exportdata?.category_id
          ? state.exportdata?.category_id[state.exportdata?.category_id.length - 1]
          : '',
        category_level: state.exportdata?.category_id?.length
      })
    })
    if (result.code === 0) {
      goCenter('DownloadCenter', 'DownloadCenter')
    }
  }
  const onCancel = () => {
    state.preriodDate = dayjs(searchFormDataRef.value.formData.created_at[0])
  }
</script>

<style scoped lang="scss">
  .SvgIconicon {
    width: 14px;
    height: 14px;
    margin-right: 6px;
    background: url('../../../../assets/images/bbimg/2.png') no-repeat 100% 100%;
  }
  // .SvgIconicon-warp:hover {
  //   .SvgIconicon {
  //     width: 14px;
  //     height: 14px;
  //     margin-right: 6px;
  //     background: url('../../../../assets/images/bbimg/1.png') no-repeat 100% 100%;
  //   }
  // }
  .tabs-btns {
    display: flex;
    .tabs-btns-item {
      margin-right: 10px;
      display: flex;
      align-items: center;
      .tabs-btns-item-img {
        margin-right: 10px;
        width: 15px;
        height: 15px;
      }
      .tabs-btns-item-line {
        width: 1px;
        background: var(--text-color-1);
        height: 60%;
        margin-right: 10px;
      }
      .actions {
        color: var(--primary-color);
      }
    }
  }
</style>
