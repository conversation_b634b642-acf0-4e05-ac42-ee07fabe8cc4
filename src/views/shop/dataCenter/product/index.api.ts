import http from '@/utils/request'

export const get_statement_product_latitude = (data: any) => {
  return http('get', `/public/ad/v2/statement-product-latitude`, data)
}

export const set_edit_conf_roi = (data: any) => {
  return http('post', `/merchant/product/edit-conf`, data)
}

export const get_ad_list = (data: any) => {
  return http('get', `/merchant/ad/list`, data)
}
export const get_mini_code = (data: any) => {
  return http('get', `/merchant/ad/mini-code`, data)
}
export const get_statement_product_latitude_chart = (data: any) => {
  return http('get', `/public/ad/v2/product-latitude-chart`, data)
}
export const get_report_info = (data: any) => {
  return http('get', `/merchant/product/report-info`, data)
}
export const get_after_sale_dimension = (data: any) => {
  return http('get', `/public/ad/after-sale-dimension/get`, data)
}

/**
 * 获取退款时间分布
 */
export const fetch_refund_time_report = (data: any) => {
  return http('get', `/public/product/refund_time_report`, data)
}

/**
 * 获取支付时间分布
 */
export const fetch_pay_time_report = (data: any) => {
  return http('get', `/public/product/pay_time_report`, data)
}
/**
 * 获取商品每小时订单数和金额
 */
export const fetch_order_num_report = (data: any) => {
  return http('get', `/public/product/order_num_report`, data)
}
/**
 * 推广账户
 */
export const fetchPromotedAccount = (data: any) => {
  return http('get', `/public/ad/v2/product_statement`, data)
}

/**
 * 获取广告列表
 */
export const getAddList = (data: any) => {
  return http('get', `/merchant/pdd/get-pdd-ad-list`, data)
}
