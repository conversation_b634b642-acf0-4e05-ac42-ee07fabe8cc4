<template>
  <DesTablePage class="page_main common_page_warp">
    <template #title> 计划报表 </template>
    <template #extra>
      <div class="flex flex-justify-between">
        <div class="tabs-btns"></div>
        <div>
          <a-button
            class="SvgIconicon-warp"
            size="small"
            style="height: 28px"
            @click="derive('invest_report_ad_latitude_v2')"
          >
            <div class="flex flex-items-center">
              <SvgIcon icon="down" class="font-size-12px mr-4px" />
              <span>汇总导出</span>
            </div>
          </a-button>
          <a-button
            class="SvgIconicon-warp"
            size="small"
            style="height: 28px"
            @click="derive('invest_report_ad_latitude_day_v2')"
          >
            <div class="flex flex-items-center">
              <SvgIcon icon="down" class="font-size-12px mr-4px" />
              <span>分日导出</span>
            </div>
          </a-button>
          <a-button
            v-if="state.exportdata.media_types?.includes(1)"
            class="SvgIconicon-warp"
            size="small"
            type="primary"
            style="height: 28px"
            @click="eventBtnItem({ type: 'ReplaceLink', data: '替换链接', row: undefined, batch: true })"
          >
            批量替换链接
          </a-button>

          <!--        <a-button @click="derive('invest_report_ad_latitude')">-->
          <!--          <template #icon> <VerticalAlignBottomOutlined /></template>-->
          <!--          汇总导出-->
          <!--        </a-button>-->
          <!--        <a-button @click="derive('invest_report_ad_latitude_day')">-->
          <!--          <template #icon> <VerticalAlignBottomOutlined /> </template>-->
          <!--          分日导出-->
          <!--        </a-button>-->
        </div>
      </div>
    </template>
    <template #tableWarp>
      <SearchBaseLayout
        ref="searchFormDataRef"
        :data="searchList"
        @changeValue="changeValue"
        @selectedDate="selectedDate"
        @onOpenChange="onOpenChange"
        :btnNames="['refresh']"
        class="mb-16px"
        :actions="{ foldNum: 0, layout: { xs: 24, sm: 12, md: 8, lg: 8, xl: 8, xxl: 6 } }"
      />

      <Report ref="reportRef" v-if="tabs[state.curr].id === 1" @eventBtn="eventBtnItem" @getDimension="getDimension" />

      <a-modal
        :width="setModalWidth(state.modalConfigData.type)"
        centered
        :maskClosable="false"
        :destroyOnClose="true"
        v-model:open="state.modalConfigData.open"
        @cancel="onCancel"
      >
        <template #title>
          <div class="flex-y-center justify-between mt--8px">
            <div>
              {{ ['PeriodReport'].includes(state.modalConfigData.type) ? '时段报表' : state.modalConfigData.title }}
              <span v-if="['PeriodReport'].includes(state.modalConfigData.type)" class="font-size-12px font-400"
                >（计划名称：<span class="c-#7A869F"> {{ state.modalConfigData.item?.ad_name }}</span> 计划ID：<span
                  class="c-#7A869F"
                >
                  {{ state.modalConfigData.item?.adgroup_id_str }})</span
                >
              </span>
            </div>

            <a-date-picker
              v-if="['PeriodReport'].includes(state.modalConfigData.type)"
              v-model:value="state.preriodDate"
              class="mr-25px"
              @change="onChangeDate"
            />
          </div>
        </template>
        <template #footer>
          <a-button v-if="!['PeriodReport'].includes(state.modalConfigData.type)" @click="closeModal">取消</a-button>
          <a-button v-if="!['PeriodReport'].includes(state.modalConfigData.type)" type="primary" @click="okModal"
            >确定</a-button
          >
        </template>
        <PeriodReport
          :item="state.modalConfigData.item"
          :preriodDate="state.preriodDate"
          ref="periodtRef"
          v-if="['PeriodReport'].includes(state.modalConfigData.type)"
        />
        <PromoteProduct
          :data="state.modalConfigData.item"
          v-if="['PromoteProduct'].includes(state.modalConfigData.type)"
        />
        <EditRatio
          ref="formRef"
          :data="state.modalConfigData.item"
          :type="state.modalConfigData.type"
          v-if="['OrderRatio', 'AmountRatio'].includes(state.modalConfigData.type)"
        />
        <ReplaceLink
          :item="state.modalConfigData.item"
          ref="replaceRef"
          :ids="state.checkIds"
          v-if="['ReplaceLink'].includes(state.modalConfigData.type)"
        />
        <ReplaceData
          :item="state.modalConfigData.item"
          ref="adRef"
          v-if="['ReplaceData'].includes(state.modalConfigData.type)"
        />
        <ReplaceRecord
          :item="state.modalConfigData.item"
          :type="2"
          ref="adRef"
          v-if="['ReplaceRecord'].includes(state.modalConfigData.type)"
        />
      </a-modal>
    </template>
  </DesTablePage>
</template>

<script setup lang="ts">
  import Report from './components/Report.vue'
  import PeriodReport from './components/PeriodReport.vue'
  import PromoteProduct from './components/PromoteProduct.vue'
  import EditRatio from './components/EditRatio.vue'

  import ReplaceLink from '../components/ReplaceLink.vue'
  import ReplaceData from '../components/ReplaceData.vue'
  import ReplaceRecord from '../components/ReplaceRecord.vue'

  import datas from './data.ts'
  import { VerticalAlignBottomOutlined } from '@ant-design/icons-vue'
  import { reactive, ref, createVNode } from 'vue'
  import moment from 'moment'
  import { post_exportdata_create } from '../../order/batchShipment/index.api'
  import { setCategoryList } from '../../goods/goodList/index.api'
  import { callback_setting } from './index.api.ts'
  import { chagne_link } from '../index.api'
  import { localStg } from '@/utils'
  import { useDownloadCenter } from '@/hooks'
  const { searchList, tabs, modalConfigData, modalTitle, setModalWidth } = datas()
  const { goCenter } = useDownloadCenter()
  import dayjs from 'dayjs'
  import { message, Modal } from 'ant-design-vue'
  const searchFormDataRef = ref()
  const after_sale_dimension = ref(1)
  const dates = ref()

  const state = reactive({
    curr: 0,
    preriodDate: dayjs(moment().format('YYYY-MM-DD')),
    hourDate: '',
    modalConfigData,
    checkIds: [],
    exportdata: {
      begin_time: moment().format('YYYY-MM-DD'),
      end_time: moment().format('YYYY-MM-DD')
    }
  })
  const formRef = ref<any>(null)
  const reportRef = ref(null)
  const periodtRef = ref(null)
  const replaceRef = ref(null)
  // 搜索
  const changeValue = (values) => {
    if (values?.status) {
      const obj = JSON.parse(JSON.stringify(values.formData))
      delete obj.created_at
      if (values.type === 'refresh') {
        reportRef.value.handleSearch({
          begin_time: values?.formData?.created_at[0],
          end_time: values?.formData?.created_at[1],
          ...obj,
          media_types:
            values?.formData.media_types && values?.formData.media_types.length != 0
              ? values?.formData.media_types.join(',')
              : ''
        })
      } else {
        reportRef.value.handleSearch({
          begin_time: values?.formData?.created_at[0],
          end_time: values?.formData?.created_at[1],
          ...obj,
          category_id: values.formData.category_id
            ? values.formData.category_id[values.formData.category_id.length - 1]
            : '',
          category_level: values.formData.category_id?.length,
          media_types:
            values?.formData.media_types && values?.formData.media_types.length != 0
              ? values?.formData.media_types.join(',')
              : '',
          page: 1
        })
      }
      state.exportdata = {
        begin_time: values?.formData?.created_at[0],
        end_time: values?.formData?.created_at[1],
        account_id: obj.account_id + '',
        ...obj,
        media_types:
          values?.formData.media_types && values?.formData.media_types.length != 0
            ? values?.formData.media_types.join(',')
            : ''
      }
      state.preriodDate = dayjs(values?.formData?.created_at[0])
    } else {
      // searchFormDataRef.value.formData.after_sale_dimension = 1
      searchFormDataRef.value.formData.after_sale_dimension = after_sale_dimension.value

      reportRef.value.handleSearch({
        page: 1,
        begin_time: moment().format('YYYY-MM-DD'),
        end_time: moment().format('YYYY-MM-DD')
      })
      ;(values.formData.created_at = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]),
        (state.exportdata = {
          begin_time: moment().format('YYYY-MM-DD'),
          end_time: moment().format('YYYY-MM-DD')
        })
    }
  }

  const selectItem = (index) => {
    state.curr = index
  }

  const getDimension = (data) => {
    searchFormDataRef.value.formData.after_sale_dimension = data
    after_sale_dimension.value = data
  }

  const eventBtnItem = (data) => {
    if (data?.batch) {
      state.checkIds = reportRef.value.getCheckIds()
      if (!state.checkIds.length) {
        return message.warning('请选择要替换的计划')
      }
    } else {
      state.checkIds = [data.row.adgroup_id]
    }

    state.modalConfigData.type = data.type
    state.modalConfigData.data = data.data
    state.modalConfigData.item = data.row
    state.modalConfigData.title = modalTitle[data.type]
    state.modalConfigData.open = true
  }
  const okModal = async () => {
    if (['AmountRatio', 'OrderRatio'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.onFinish()

      const params = ['AmountRatio'].includes(state.modalConfigData.type)
        ? {
            callback_money_switch: values.open ? 1 : 2,
            callback_amount_type: values.callback_amount_type,
            callback_amount_role: JSON.stringify(
              values.callback_amount_type == 3 || values.callback_amount_type == 4
                ? values.callback_amount_role
                : values.callback_amount_role_list
            ),
            op_type: state.modalConfigData.type == 'AmountRatio' ? 1 : 2,
            media_type: state.modalConfigData.item.media_type == 4 ? 0 : state.modalConfigData.item.media_type,
            adgroup_id: state.modalConfigData.item.adgroup_id_str
          }
        : {
            callback_order_switch: values.open ? 1 : 2,
            callback_type: values.callback_type,
            media_type: state.modalConfigData.item.media_type == 4 ? 0 : state.modalConfigData.item.media_type,
            callback_role: values.callback_role_list,
            callback_ratio: Number(values.callback_ratio),

            op_type: state.modalConfigData.type == 'AmountRatio' ? 1 : 2,
            adgroup_id: state.modalConfigData.item.adgroup_id_str
          }

      const result = await callback_setting(params)
      if (result.code === 0) {
        closeModal()
        reportRef.value.handleSearch({
          page: 1,
          ...searchFormDataRef.value.formData,
          begin_time: state.exportdata.begin_time,
          end_time: state.exportdata.end_time
        })
        message.success('操作成功')
      }
    } else if (['ReplaceLink'].includes(state.modalConfigData.type)) {
      const values = await replaceRef.value.validateFields()
      if (!values.ad_id) {
        return message.warning('请选择广告链接')
      }
      console.log(values, 'values')

      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '确认更换，更换后广告将重新进行审核?'),
        async onOk() {
          const res = await chagne_link({
            ad_id: values.ad_id,
            ad_url: values.ad_url,
            type: 2,
            ids: state.checkIds.join(',')
          })
          message.success(res.msg)
          closeModal()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } else {
      closeModal()
    }
  }
  const closeModal = () => {
    state.modalConfigData.type = ''
    state.modalConfigData.data = null
    state.modalConfigData.item = null
    state.modalConfigData.title = ''
    state.modalConfigData.open = false
  }
  let oldDate
  // 打开日期选择弹窗
  const onOpenChange = (open) => {
    if (open) {
      oldDate = JSON.parse(JSON.stringify(searchFormDataRef.value.formData.created_at))
      dates.value = []
      searchFormDataRef.value.formData.created_at = []
    } else {
      const hasDate = dates.value.every((item) => item)
      if (!dates.value.length || !hasDate) searchFormDataRef.value.formData.created_at = oldDate
    }
  }
  // 日期选择
  const selectedDate = (val) => {
    dates.value = val
  }
  const disabledDate = (current) => {
    const decemberTwentyFirst = dayjs('2023-12-1').startOf('day')
    if (current.isBefore(decemberTwentyFirst, 'day')) {
      return true
    }
    if (!dates.value || dates.value.length === 0) {
      return false
    }

    const tooLate = dates.value[0] && current.diff(dates.value[0], 'days') > 30
    const tooEarly = dates.value[1] && dayjs(dates.value[1]).diff(current, 'days') > 30
    return tooLate || tooEarly
  }
  const shopForm = searchList.find((item) => item.field == 'created_at')
  if (shopForm && shopForm.props) {
    shopForm.props.disabledDate = disabledDate
  }
  // 获取商品类目列表
  const getCategorlList = async () => {
    try {
      let res = await setCategoryList({})
      searchList.forEach((v) => {
        if (v.field == 'category_id') {
          v.props.options = res.data || []
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  getCategorlList()

  const onChangeDate = (date, dateString) => {
    console.log(date, 'onChangeDate')
    state.hourDate = dateString
    periodtRef.value.getList(state.hourDate)
  }
  // 导出
  const derive = async (type) => {
    const result = await post_exportdata_create({
      type,
      params: JSON.stringify({
        shop_id: localStg.get('shopInfo').id,
        ...state.exportdata,
        account_id: Number(state.exportdata?.account_id),
        adgroup_id: Number(state.exportdata?.adgroup_id),
        category_id: state.exportdata?.category_id
          ? state.exportdata?.category_id[state.exportdata?.category_id.length - 1]
          : '',
        category_level: state.exportdata?.category_id?.length
      })
    })
    if (result.code === 0) {
      goCenter('DownloadCenter', 'DownloadCenter')
    }
  }
  const onCancel = () => {
    state.preriodDate = dayjs(searchFormDataRef.value.formData.created_at[0])
  }
</script>

<style scoped lang="scss">
  .SvgIconicon {
    width: 14px;
    height: 14px;
    margin-right: 6px;
    background: url('../../../../assets/images/bbimg/2.png') no-repeat 100% 100%;
  }
  // .SvgIconicon-warp:hover {
  //   .SvgIconicon {
  //     width: 14px;
  //     height: 14px;
  //     margin-right: 6px;
  //     background: url('../../../../assets/images/bbimg/1.png') no-repeat 100% 100%;
  //   }
  // }

  .tabs-btns {
    display: flex;
    .tabs-btns-item {
      margin-right: 10px;
      display: flex;
      align-items: center;
      .tabs-btns-item-img {
        margin-right: 10px;
        width: 15px;
        height: 15px;
      }
      .tabs-btns-item-line {
        width: 1px;
        background: var(--text-color-1);
        height: 60%;
        margin-right: 10px;
      }
      .actions {
        color: var(--primary-color);
      }
    }
  }
</style>
