import { requireImg } from '@/utils'
import moment from 'moment'
import dayjs from 'dayjs'
export default function datas() {
  const headerCell = [
    'cost',
    'ad_thousand_display_price',
    'valid_click_count',
    'ctr',
    'arrival_rate',
    'order_conversion_rate',
    'pay_conversion_rate',
    'order_amount_avg',
    'conversions_cost',
    'conversions_rate',
    'order_count',
    'order_amount',
    'refund_count',
    'pay_time_refund_rate',
    'pay_time_refund_count',
    'order_count_turnover',
    'order_amount_turnover',
    'turnover_roi',
    'refund_rate',
    'refund_amount',
    'pay_time_refund_amount',
    'refund_rate',
    'order_callback_ratio',
    'amount_callback_ratio',
    'callback_cost',
    'callback_count',
    'callback_amount',
    'callback_roi',
    'callback_rate'
  ]

  const disabledDate = (current: any) => {
    const today = dayjs()
    const thirtyDaysAgo = today.subtract(30, 'day')
    const decemberTwentyFirst = dayjs('2023-12-1').startOf('day')
    current = current.startOf('day')
    return (
      current.isBefore(decemberTwentyFirst, 'day') ||
      current.isBefore(thirtyDaysAgo, 'day') ||
      current.isAfter(today, 'day')
    )
  }
  const searchList = [
    // {
    //   field: 'media_type',
    //   type: 'select',
    //   value: undefined,
    //   props: {
    //     options: [
    //       {
    //         label: '广点通',
    //         value: 1
    //       }
    //     ],
    //     placeholder: '请选择媒体类型'
    //   },
    //   layout: {
    //     xs: 24,
    //     sm: 12,
    //     md: 8,
    //     lg: 8,
    //     xl: 8,
    //     xxl: 6
    //   }
    // },
    {
      type: 'select',
      field: 'media_types',
      value: undefined,
      props: {
        placeholder: '请选择广告平台',
        mode: 'multiple',
        options: [
          {
            value: 1,
            label: '广点通'
          },
          {
            value: 4,
            label: '磁力引擎'
          },
          {
            value: 6,
            label: '巨量引擎'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'select',
      field: 'product_type',
      value: null,
      props: {
        placeholder: '请选择商品类型',

        options: [
          {
            value: 1,
            label: '小程序'
          },
          {
            value: 2,
            label: '拼多多'
          },
          {
            value: 3,
            label: '淘宝'
          },
          {
            value: 4,
            label: '京东'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'account_id',
      value: undefined,
      props: {
        placeholder: '请输入账户ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'account_name',
      value: undefined,
      props: {
        placeholder: '请输入账户名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'plan_goods',
      field: 'product_id',
      value: undefined,
      props: {
        placeholder: '请输入商品名称/ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      field: 'category_id',
      type: 'cascader',
      value: [],
      span: 6,
      props: {
        placeholder: '请选择商品类目',
        options: [],
        filterable: true,
        fieldNames: {
          label: 'name',
          value: 'id'
        },
        changeOnSelect: true
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'adgroup_id',
      value: undefined,
      props: {
        placeholder: '请输入计划ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'ad_name',
      value: undefined,
      props: {
        placeholder: '请输入计划名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'shop_ad_name',
      value: undefined,
      props: {
        placeholder: '请输入广告名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'select',
      field: 'landing_type',
      value: null,
      props: {
        placeholder: '请选择落地页类型',
        options: [
          {
            value: 1,
            label: '图文落地页'
          },
          {
            value: 2,
            label: '视频落地页'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'admin',
      field: 'admin_id',
      value: null,
      props: {
        placeholder: '请选择负责人'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'date',
      field: 'created_at',
      value: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
      range: 'NearlyThirty',
      props: {
        placeholder: ['开始时间', '结束时间'],
        disabledDate: false,
        allowClear: false
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'select',
      field: 'after_sale_dimension',
      value: 1,
      props: {
        placeholder: '请选择统计维度',
        allowClear: false,
        options: [
          {
            value: 1,
            label: '退款完成时间'
          },
          {
            value: 2,
            label: '支付时间'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ]

  const tabs = [
    {
      id: 1,
      icon: requireImg('order/o8.png'),
      noicon: requireImg('order/star_drak.png'),
      title: '广点通'
    }
    // {
    //   id: 2,
    //   icon: requireImg('order/o1.png'),
    //   noicon: requireImg('order/star_bright.png'),
    //   title: '磁力引擎'
    // }
  ]

  const iconType = (type: string | number) => {
    let status = {
      4: requireImg('order/o1.png'),
      1: requireImg('order/o8.png')
    }
    return status[type]
  }

  const modalConfigData = {
    open: false,
    title: '',
    type: '',
    data: null,
    item: null
  } as any

  const modalTitle = {
    PeriodReport: '时段报表',
    PromoteProduct: '推广商品',
    OrderRatio: ' 修改订单回传比例',
    AmountRatio: ' 修改金额回传比例',
    ReplaceData: '替换链接',
    ReplaceRecord: '替换记录',
    ReplaceDetail: '替换记录详情',
    ReplaceLink: '替换链接'
  }

  const setModalWidth = (type) => {
    if (['PeriodReport'].includes(type)) return '80%'
    if (['PromoteProduct', 'OrderRatio', 'AmountRatio'].includes(type)) return 800
    if (['ReplaceLink'].includes(type)) return 800
    if (['ReplaceData'].includes(type)) return 660
    if (['ReplaceRecord'].includes(type)) return 900
    return 520
  }

  const summaryFilder = [
    'cost',
    'view_count',
    'thousand_display_price',
    'valid_click_count',
    'conversions_count',
    'conversions_cost',
    'order_count',
    'order_amount',
    'order_count_turnover',
    'order_amount_turnover',
    'refund_rate',
    'refund_amount',
    'pay_time_refund_amount',
    'video_outer_play_count',
    'video_outer_play3s_count',
    'video_outer_play25_count',
    'video_outer_play50_count',
    'video_outer_play75_count',
    'callback_cost',
    'callback_amount'
  ]

  const setFilder = [
    'cost',
    'thousand_display_price',
    'click_order_amount_avg',
    'conversions_cost',
    'order_amount',
    'order_amount_turnover',
    'refund_amount',
    'pay_time_refund_amount',
    'ad_thousand_display_price',
    'order_amount_avg',
    'callback_cost',
    'callback_amount'
  ]
  // 要处理 % 的集合
  const setPercentFilder = [
    'ctr',
    'arrival_rate',
    'order_conversion_rate',
    'pay_conversion_rate',
    'conversions_rate',
    'refund_rate',
    'pay_time_refund_rate',
    'video_outer_play3s_rate',
    'video_outer_play25_rate',
    'video_outer_play50_rate',
    'video_outer_play75_rate',
    'callback_rate'
  ]
  // 要统计的字段
  const statisticsData = [
    'C',
    'click_order_amount_avg',
    'conversions_cost',
    'conversions_count',
    'conversions_rate',
    'cost',
    'ctr',
    'arrival_rate',
    'order_conversion_rate',
    'pay_conversion_rate',
    'order_amount',
    'order_amount_avg',
    'order_amount_turnover',
    'order_count',
    'order_count_turnover',
    'refund_amount',
    'pay_time_refund_amount',
    'refund_count',
    'refund_rate',
    'roi',
    'refund_count',
    'pay_time_refund_rate',
    'pay_time_refund_count',
    'thousand_display_price',
    'turnover_roi',
    'valid_click_count',
    'video_outer_play3s_count',
    'video_outer_play3s_rate',
    'video_outer_play25_count',
    'video_outer_play25_rate',
    'video_outer_play50_count',
    'video_outer_play50_rate',
    'video_outer_play75_count',
    'video_outer_play75_rate',
    'video_outer_play_count',
    'view_count',
    'ad_thousand_display_price',
    'callback_cost',
    'callback_count',
    'callback_amount',
    'callback_roi',
    'callback_rate'
  ]
  // 百分比的集合
  const percentData = [
    'ctr',
    'arrival_rate',
    'order_conversion_rate',
    'pay_conversion_rate',
    'conversions_rate',
    'refund_rate',
    'pay_time_refund_rate',
    'video_outer_play3s_rate',
    'video_outer_play25_rate',
    'video_outer_play50_rate',
    'video_outer_play75_rate',
    'callback_rate'
  ]

  const configuredStatusType = {
    AD_STATUS_NORMAL: '有效',
    AD_STATUS_SUSPEND: '暂停'
  }

  const promotedObjectType = {
    PROMOTED_OBJECT_TYPE_LINK: '网页',
    PROMOTED_OBJECT_TYPE_LINK_WECHAT: '品牌网页',
    PROMOTED_OBJECT_TYPE_ECOMMERCE: '商品推广',
    PROMOTED_OBJECT_TYPE_APP_ANDROID: 'Android应用',
    PROMOTED_OBJECT_TYPE_APP_IOS: 'IOS应用',
    PROMOTED_OBJECT_TYPE_APP_ANDROID_MYAPP: '应用宝推广',
    PROMOTED_OBJECT_TYPE_APP_ANDROID_UNION: 'Android应用',
    PROMOTED_OBJECT_TYPE_LOCAL_ADS_WECHAT: '本地门店',
    PROMOTED_OBJECT_TYPE_QQ_BROWSER_MINI_PROGRAM: '浏览器小程序',
    PROMOTED_OBJECT_TYPE_QQ_MESSAGE: 'QQ 消息',
    PROMOTED_OBJECT_TYPE_LEAD_AD: '销售线索'
  }
  const timeObj = {
    '0': '00:00',
    1: '01:00',
    2: '02:00',
    3: '03:00',
    4: '04:00',
    5: '05:00',
    6: '06:00',
    7: '07:00',
    8: '08:00',
    9: '09:00',
    10: '10:00',
    11: '11:00',
    12: '12:00',
    13: '13:00',
    14: '14:00',
    15: '15:00',
    16: '16:00',
    17: '17:00',
    18: '18:00',
    19: '19:00',
    20: '20:00',
    21: '21:00',
    22: '22:00',
    23: '23:00',
    24: '24:00'
  }
  return {
    timeObj,
    promotedObjectType,
    configuredStatusType,
    percentData,
    statisticsData,
    setPercentFilder,
    setFilder,
    summaryFilder,
    setModalWidth,
    modalTitle,
    modalConfigData,
    iconType,
    tabs,
    searchList,
    headerCell
  }
}
