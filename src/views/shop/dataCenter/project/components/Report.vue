<template>
  <div class="warp">
    <a-table
      sticky
      :columns="state.fixedColumns"
      :data-source="state.dataSource"
      :pagination="state.pagination"
      :loading="state.loading"
      :showSorterTooltip="false"
      :scroll="{ x: 2000 }"
      :sortDirections="['descend', 'ascend']"
      @change="pageChange"
      bordered
      size="small"
      :rowKey="(record) => record.adgroup_id"
      :row-selection="{
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange
      }"
    >
      <template #headerCell="{ column }">
        <template v-if="headerCell.includes(column.dataIndex)">
          <div>
            <span>{{ column.title }}</span>
            <a-tooltip>
              <template #title>{{ column.text }}</template>
              <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
            </a-tooltip>
          </div>
        </template>
        <template v-if="column?.dataIndex === 'actions'">
          <a-space>
            <span>{{ column?.title }} </span>
            <span>
              <SetTableColumns
                class="cursor-pointer"
                v-model:data="state.fixedColumns"
                :column="newFixedColumns"
                :isChange="isChange"
              />
            </span>
          </a-space>
        </template>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'account_name'">
          <div class="item-warp">
            <div class="item-warp-content">
              <div>
                <a-tooltip>
                  <template #title>{{ record.account_name || '--' }}</template>
                  <span class="item-warp-content-title">{{ record.account_name || '--' }}</span>
                </a-tooltip>
              </div>
              <div class="number-id">ID：{{ record.account_id || '--' }}</div>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'ad_name'">
          <div class="item-warp">
            <div class="item-warp-img flex">
              <img :src="requireImg('order/o8.png')" alt="" v-if="record.media_type == 1" />
              <img :src="requireImg('order/o6.png')" alt="" v-if="record.media_type == 6" />
              <img :src="requireImg('order/o1.png')" alt="" v-if="record.media_type == 4" />
              <img :src="requireImg('order/pdd_icon.png')" alt="" v-if="record.product_type == 2" />
            </div>
            <div class="item-warp-content">
              <div>
                <a-tooltip>
                  <template #title>
                    <div>{{ record.ad_name || '--' }}</div>
                    <div>计划ID：{{ record.adgroup_id_str || '--' }}</div>
                  </template>
                  <div class="item-warp-content-title">{{ record.ad_name || '--' }}</div>
                  <div class="item-warp-content-title number-id">ID：{{ record.adgroup_id_str || '--' }}</div>
                </a-tooltip>
              </div>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'goods_info'">
          <div class="item-warp" v-if="record.product_name">
            <div class="item-warp-goods-img">
              <a-image style="width: 34px; height: 34px; border-radius: 2px" :src="record.product_image"></a-image>
            </div>
            <div class="item-warp-content">
              <div>
                <a-tooltip>
                  <template #title>{{ record.product_name || '--' }}</template>
                  <span class="item-warp-content-title">{{ record.product_name || '--' }}</span>
                </a-tooltip>
              </div>
              <div class="number-id">ID：{{ record.product_code || '--' }}</div>
            </div>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="column.dataIndex === 'shop_ad_name'">
          <a-tooltip>
            <template #title>
              <div>{{ record.shop_ad_name || '--' }}</div>
            </template>
            <div class="text_overflow">{{ record.shop_ad_name || '--' }}</div>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'landing_type'">
          <div class="" v-if="record.product_type !== 2">
            <span class="primary-color" v-if="[1, 2].includes(record.landing_type)">{{
              record.landing_type == 1 ? '图文落地页' : '视频落地页'
            }}</span>
            <span v-else>--</span>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="column.dataIndex === 'order_callback_ratio'">
          <div
            class="primary-color cursor-pointer"
            @click="actionChange('OrderRatio', '', record)"
            v-if="record.media_type !== 1 || (record.media_type === 1 && shopInfo?.limit_callback === 1)"
          >
            <span v-if="record.ks_callback_setting.callback_order_switch == 1"> 已启用</span>
            <span v-else class="primary-color">未启用</span>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="column.dataIndex === 'amount_callback_ratio'">
          <div
            class="primary-color cursor-pointer"
            @click="actionChange('AmountRatio', '', record)"
            v-if="record.media_type !== 1 || (record.media_type === 1 && shopInfo?.limit_callback === 1)"
          >
            <span v-if="record.ks_callback_setting.callback_money_switch == 1">已启用</span>
            <span v-else class="primary-color">未启用</span>
          </div>
          <div v-else>--</div>
        </template>
        <!-- <template v-if="column.dataIndex === 'configured_status'">
          <span>{{ configuredStatusType[record.configured_status] }}</span>
        </template> -->
        <template v-if="setFilder.includes(column.dataIndex)">
          <span>¥ {{ centsToYuan(record[column.dataIndex]) }}</span>
        </template>
        <template v-if="setPercentFilder.includes(column.dataIndex)">
          <span>{{ record[column.dataIndex] }}%</span>
        </template>
        <template v-if="column.dataIndex === 'last_modified_time'">
          {{ formatDate(record.last_modified_time * 1000) }}
        </template>
        <template v-if="column.dataIndex === 'category_names'">
          <span>
            {{
              record.category_names.indexOf(',') > -1
                ? record.category_names.split(',').join('>>')
                : record.category_names
            }}</span
          >
        </template>
        <template v-if="column.dataIndex === 'actions'">
          <a-space :size="[0, 0]" class="actions-warp" wrap>
            <a-button style="padding-left: 0px" size="small" type="link" @click="updateData(record)">更新</a-button>
            <a-button
              style="padding-left: 0px"
              size="small"
              type="link"
              @click="actionChange('PromoteProduct', '', record)"
              >推广商品</a-button
            >
            <a-button
              style="padding-left: 0px"
              size="small"
              type="link"
              @click="actionChange('PeriodReport', '时段报表', record)"
              >时段报表</a-button
            >
            <a-button
              style="padding-left: 0px"
              size="small"
              type="link"
              v-if="record.media_type == 1"
              @click="actionChange('ReplaceLink', '替换链接', record)"
              >替换链接</a-button
            >
            <a-button
              style="padding-left: 0px"
              size="small"
              type="link"
              v-if="record.media_type == 1"
              @click="actionChange('ReplaceRecord', '替换记录', record)"
              >替换记录</a-button
            >
          </a-space>
        </template>
      </template>

      <template #summary v-if="state.dataSource.length">
        <a-table-summary fixed v-if="state.dataSource.length">
          <a-table-summary-row>
            <a-table-summary-cell style="left: 0px; position: sticky !important; z-index: 100"> </a-table-summary-cell>
            <template v-for="(item, index) in state.fixedColumns">
              <template v-if="['ad_name'].includes(item.dataIndex)">
                <a-table-summary-cell :index="index">总计: {{ state.pagination.total }}</a-table-summary-cell>
              </template>
              <template v-else-if="statisticsData.includes(item.dataIndex)">
                <template v-if="percentData.includes(item.dataIndex)">
                  <a-table-summary-cell :index="index"> {{ state?.sum[item.dataIndex] }}% </a-table-summary-cell>
                </template>
                <template v-else>
                  <a-table-summary-cell :index="index">
                    {{ setFider(setFilder, item.dataIndex) }}
                  </a-table-summary-cell>
                </template>
              </template>
              <template v-else>
                <a-table-summary-cell :index="index"></a-table-summary-cell>
              </template>
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, watch } from 'vue'
  import {
    get_statement_ad_latitude,
    get_statement_ad_latitude_hour,
    get_after_sale_dimension,
    manual_ad_sync
  } from '../index.api'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  import { centsToYuan, formatDate } from '@/utils'
  import { requireImg } from '@/utils'
  import datas from '../data'
  import moment from 'moment'
  import { useApp } from '@/hooks'
  import { cloneDeep } from 'lodash-es'
  const { isMobile, shopInfo } = useApp()
  const emits = defineEmits(['eventBtn', 'getDimension'])
  const { setFilder, setPercentFilder, headerCell, statisticsData, percentData } = datas()
  const setFider = (arr: any, key: any) => {
    if (arr.includes(key)) {
      return `¥ ${centsToYuan(state?.sum[key])}`
    } else {
      return state?.sum[key]
    }
  }
  const after_sale_dimension = ref(1)
  const newFixedColumns = ref([])

  const fixedColumns = ref([
    {
      title: '计划名称',
      dataIndex: 'ad_name',
      fixed: isMobile.value ? false : 'left',
      width: 220
    },
    {
      title: '账户名称',
      dataIndex: 'account_name',
      width: 200
    },
    {
      title: '商品信息',
      dataIndex: 'goods_info',
      width: 200
    },
    {
      title: '广告名称',
      dataIndex: 'shop_ad_name',
      width: 200
    },
    {
      title: '落地页类型',
      dataIndex: 'landing_type',
      width: 180
    },
    {
      title: '回传订单比例',
      dataIndex: 'order_callback_ratio',
      text: '账户报表和计划报表回传订单比例开关关闭时，使用创建广告链接时设置的回传比例；计划报表开启回传订单比例，优先使用计划报表比例',
      width: 140
    },
    {
      title: '回传金额比例',
      dataIndex: 'amount_callback_ratio',
      text: '账户报表和计划报表回传金额比例开关关闭时，使用创建广告链接时设置的回传金额比例；计划报表开启回传金额比例，优先使用计划报表比例',
      width: 140
    },
    {
      title: '总消耗',
      dataIndex: 'cost',
      width: 160,
      text: '广告投放总共付出的费用成本',
      sorter: true
    },
    {
      title: '展示数',
      dataIndex: 'view_count',
      width: 140,
      sorter: true
    },
    // {
    //   title: '计划状态',
    //   dataIndex: 'configured_status',
    //   width: 120
    // },
    // {
    //   title: '目标类型',
    //   dataIndex: 'promoted_object_type',
    //   width: 120
    // },
    {
      title: '千次展示均价',
      dataIndex: 'ad_thousand_display_price',
      width: 160,
      text: '总消耗/曝光量*1000',
      sorter: true
    },
    {
      title: '点击数',
      dataIndex: 'valid_click_count',
      width: 140,
      text: '有效且被计费的点击次数',
      sorter: true
    },
    {
      title: '点击率',
      dataIndex: 'ctr',
      width: 100,
      text: '点击量/曝光量*100%',
      sorter: true
    },
    {
      title: '抵达率',
      dataIndex: 'arrival_rate',
      width: 140,
      text: '小程序访客数/计划点击数',
      sorter: true
    },
    {
      title: '平均点击单价',
      dataIndex: 'click_order_amount_avg',
      width: 160,
      sorter: true
    },
    {
      title: '平均客单价',
      dataIndex: 'order_amount_avg',
      width: 160,
      text: '支付总金额/支付订单数',
      sorter: true
    },
    {
      title: '转化数',
      dataIndex: 'conversions_count',
      width: 140,
      sorter: true
    },
    {
      title: '转化成本',
      dataIndex: 'conversions_cost',
      width: 140,
      text: '总消耗/转化数',
      sorter: true
    },
    {
      title: '转化率',
      dataIndex: 'conversions_rate',
      width: 120,
      text: '转化数/点击量',
      sorter: true
    },
    {
      title: '下单转化率',
      dataIndex: 'order_conversion_rate',
      width: 120,
      text: '(下单人数/访客数)X 100%',
      sorter: true
    },
    {
      title: '支付订单数',
      dataIndex: 'order_count',
      width: 140,
      text: '支付成功的订单数量',
      sorter: true
    },
    {
      title: '支付总金额',
      dataIndex: 'order_amount',
      width: 140,
      text: '支付成功订单的总金额',
      sorter: true
    },
    {
      title: '支付ROI',
      dataIndex: 'roi',
      width: 120,
      sorter: true
    },
    {
      title: '支付转化率',
      dataIndex: 'pay_conversion_rate',
      width: 120,
      text: '(支付人数/访客数)X 100%',
      sorter: true
    },
    {
      title: '成交订单数',
      dataIndex: 'order_count_turnover',
      width: 140,
      text: '支付订单数-退款量',
      sorter: true
    },
    {
      title: '成交总金额',
      dataIndex: 'order_amount_turnover',
      width: 140,
      text: '支付总金额-退款总金额',
      sorter: true
    },
    {
      title: '成交ROI',
      dataIndex: 'turnover_roi',
      width: 140,
      text: '成交总金额/总消耗',
      sorter: true
    },
    {
      title: '退单量',
      dataIndex: 'refund_count',
      width: 210,
      text: '指定时间段内完成退款的售后单数量（仅退款、退货退款 ），按照退款完成时间进行统计',
      sorter: true
    },
    {
      title: '退单量',
      dataIndex: 'pay_time_refund_count',
      width: 180,
      text: '指定时间段内完成退款的售后单数量（仅退款、退货退款 ），按照订单支付时间进行统计',
      sorter: true
    },
    {
      title: '退款总金额',
      dataIndex: 'refund_amount',
      width: 240,
      text: '指定时间段内完成退款的售后单退款金额总和（仅退款、退货退款 ），按照退款完成时间进行统计',
      sorter: true
    },
    {
      title: '退款总金额',
      dataIndex: 'pay_time_refund_amount',
      width: 220,
      text: '指定时间段内完成退款的售后单退款金额总和（仅退款、退货退款 ），按照订单支付时间进行统计',
      sorter: true
    },
    {
      title: '退单率',
      dataIndex: 'refund_rate',
      width: 210,
      text: '退款单数（退款完成时间）/支付订单数*100%',
      sorter: true
    },
    {
      title: '退单率',
      dataIndex: 'pay_time_refund_rate',
      text: '退款单数（支付时间）/支付订单数*100%',
      width: 200,
      sorter: true
    },
    {
      title: '扣除回传后成本',
      dataIndex: 'callback_cost',
      width: 180,
      text: '除去扣除的回传订单，实际回传的成本',
      sorter: true
    },
    {
      title: '扣除回传后订单数',
      dataIndex: 'callback_count',
      width: 180,
      text: '除去扣除的回传订单，实际回传的订单数',
      sorter: true
    },
    {
      title: '扣除回传金额后的金额',
      dataIndex: 'callback_amount',
      width: 200,
      text: '除去扣除的回传订单，实际回传的订单金额',
      sorter: true
    },
    {
      title: '扣除回传后ROI',
      dataIndex: 'callback_roi',
      width: 180,
      text: '扣除回传金额后的金额/总消耗',
      sorter: true
    },
    {
      title: '实际回传率',
      dataIndex: 'callback_rate',
      text: '实际回传订单数/总订单数*100%',
      width: 140,
      sorter: true
    },
    {
      title: '播放量',
      dataIndex: 'video_outer_play_count',
      width: 140,
      sorter: true
    },
    {
      title: '3秒播放数',
      dataIndex: 'video_outer_play3s_count',
      width: 160,
      sorter: true
    },
    {
      title: '3秒播放率',
      dataIndex: 'video_outer_play3s_rate',
      width: 160,
      sorter: true
    },
    {
      title: '25%进度播放数',
      dataIndex: 'video_outer_play25_count',
      width: 160,
      sorter: true
    },
    {
      title: '25%进度播放率',
      dataIndex: 'video_outer_play25_rate',
      width: 160,
      sorter: true
    },
    {
      title: '50%进度播放数',
      dataIndex: 'video_outer_play50_count',
      width: 160,
      sorter: true
    },
    {
      title: '50%进度播放率',
      dataIndex: 'video_outer_play50_rate',
      width: 160,
      sorter: true
    },
    {
      title: '75%进度播放数',
      dataIndex: 'video_outer_play75_count',
      width: 160,
      sorter: true
    },
    {
      title: '75%进度播放率',
      dataIndex: 'video_outer_play75_rate',
      width: 160,
      sorter: true
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      width: 180
    },
    {
      title: '负责人',
      dataIndex: 'admin_name',
      width: 100
    },
    {
      title: '商品类目',
      dataIndex: 'category_names',
      width: 240
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 140,
      disabled: true,
      fixed: isMobile.value ? false : 'right'
    }
  ])
  const state = reactive({
    fixedColumns: cloneDeep(newFixedColumns),
    loading: true,
    dataSource: [],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    },
    sum: {},
    pageOption: {
      page: 1,
      page_size: 10
    },
    initQuery: {
      begin_time: moment().format('YYYY-MM-DD'),
      end_time: moment().format('YYYY-MM-DD'),
      field: undefined,
      after_sale_dimension: undefined,
      order: undefined
    },
    filtration: null,
    selectionRowsPlus: [],
    selectionItem: [], // 表格选择的Item
    selectedRowKeys: [],
    checked_list: []
  })

  function delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  function makeRequest(data) {
    // 模拟请求，这里可以替换为你的实际请求逻辑
    getTime(data)
  }
  let isOne = true
  async function makeRequestsInBatches(array, batchSize, interval) {
    let index = 0
    while (index < array.length && !isOne) {
      console.log('走这里 循环')
      const batch = array.slice(index, index + batchSize)

      const promises = batch.map((item) => makeRequest(item))
      const results = await Promise.all(promises)

      await delay(interval)

      index += batchSize
    }

    if (index < array.length) {
      const remainingBatch = array.slice(index)
      const remainingPromises = remainingBatch.map((item) => makeRequest(item))
      const remainingResults = await Promise.all(remainingPromises)
    }
    return index < array.length
  }
  async function batchRequests(array, batchSize, interval) {
    isOne = false
    return await makeRequestsInBatches([...array], batchSize, interval)
  }

  // 获取列表
  const getList = async (ai: any) => {
    try {
      isOne = true
      state.loading = !ai
      const result = await get_statement_ad_latitude({
        ...state.pageOption,
        ...state.initQuery,
        ...(state.filtration || {})
      })
      state.dataSource = result?.data?.list || []
      // let arr = []
      // state.dataSource.forEach((v) => {
      //   // 如果下单数 大于 转化数
      //   if (v.order_count > v.conversions_count) {
      //     arr.push(v.account_id)
      //   }
      // })
      state.pagination.pageSize = result?.data?.size || 10
      state.pagination.total = result?.data?.total_num || 0
      state.pagination.current = result?.data?.page || 1
      state.sum = result?.data?.sum || {}
      // if (arr.length != 0 && isOne && !ai) {
      //   if (!(await batchRequests(arr, 3, 500))) {
      //     // getList(true)
      //   }
      // }
    } catch (error) {
      console.error(error)
      state.dataSource = []
      state.pagination.pageSize = 10
      state.pagination.total = 0
      state.pagination.current = 1
      state.sum = {}
    } finally {
      state.loading = false
    }
  }

  // 请求时段报表
  const getTime = async (v) => {
    try {
      let res = await get_statement_ad_latitude_hour({ account_id: v })
      console.log(res, '222')
    } catch (error) {}
  }

  const pageChange = (data, filters, sorter) => {
    if (sorter?.order) {
      state.initQuery.field = sorter.field
      state.initQuery.order = sorter.order === 'ascend' ? 'asc' : 'desc'
    } else {
      state.initQuery.field = undefined
      state.initQuery.order = undefined
    }
    state.pageOption.page = data.current
    state.pageOption.page_size = data.pageSize
    state.pagination.current = data.current
    if (state.filtration) {
      state.filtration.page = data.current
    }

    getList()
  }

  // 操作时间
  const actionChange = (type, data, row) => {
    emits('eventBtn', { type, data, row })
  }

  function createHash(hashLength = 10) {
    return Array.from(Array(Number(hashLength) || 24), () => Math.floor(Math.random() * 36).toString(36)).join('')
  }
  const isChange = ref(createHash())
  const tabColumns = (type) => {
    type = type ? type : state.initQuery.after_sale_dimension
    const pay_time_arr = ['pay_time_refund_rate', 'pay_time_refund_count', 'pay_time_refund_amount']
    const refund_time_arr = ['refund_rate', 'refund_count', 'refund_amount']
    const filterArr = type == 1 ? pay_time_arr : refund_time_arr
    newFixedColumns.value = fixedColumns.value.filter((item) => !filterArr.includes(item.dataIndex))
    state.fixedColumns = newFixedColumns.value
    isChange.value = createHash()
  }

  const updateData = async (row) => {
    const params = { ...state.initQuery, ...state.filtration }
    await manual_ad_sync({
      account_id: row.account_id,
      adgroup_id: row.adgroup_id_str,
      date: params.begin_time,
      media_type: 1
    })
    getList()
  }

  // 手动触发搜索列表刷新
  const handleSearch = (params) => {
    if (params) {
      tabColumns(params?.after_sale_dimension)
    }
    state.filtration = params ? params : state.filtration
    getList()
  }
  onMounted(async () => {
    const data = await get_after_sale_dimension()
    state.initQuery.after_sale_dimension = data.data
    after_sale_dimension.value = data.data
    tabColumns(after_sale_dimension.value)
    emits('getDimension', data.data)
    getList()
  })

  const onSelectChange = (selectedRowKeys, selectedRows) => {
    state.selectedRowKeys = selectedRowKeys
  }

  const onSelect = (record, selected) => {
    console.log('onSelect', record, selected)
    selected
      ? state.selectionRowsPlus.push(record)
      : state.selectionRowsPlus.splice(
          state.selectionRowsPlus.findIndex((x) => x.adgroup_id === record.adgroup_id),
          1
        )
  }
  const onSelectAll = (selected, selectedRows, changeRows) => {
    console.log('onSelectAll', selected, selectedRows, changeRows)
    state.selectionRowsPlus = selected
      ? state.selectionRowsPlus.concat(changeRows)
      : state.selectionRowsPlus.filter((x) => !changeRows.find((i) => i.adgroup_id === x.adgroup_id))
  }
  const getCheckIds = () => {
    return state.selectionRowsPlus.map((item) => item.adgroup_id)
  }

  watch(
    () => state.selectionRowsPlus,
    () => {
      state.checked_list = state.selectionRowsPlus.map((v: any) => {
        return {
          id: v.adgroup_id
        }
      })
    },
    { deep: true }
  )
  // 向外暴露方法
  defineExpose({ handleSearch, getCheckIds })
</script>

<style scoped lang="scss">
  @import '../../../../../assets/css/mixin_scss_fn';
  .primary-color {
    color: var(--primary-color);
  }
  .warp {
    :deep(.ant-table-column-sorter) {
      color: #939599;
    }
    :deep(.ant-table-wrapper .ant-table-column-sorters:hover .ant-table-column-sorter) {
      color: #939599;
    }
    :deep(.ant-table-wrapper .ant-table) {
      border-radius: 4px !important;
      overflow: hidden;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table-thead > tr > th) {
      font-weight: 400;
      background: #f8f8f9;
      color: #313233;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-tbody > tr > td) {
      vertical-align: middle;
      color: #313233;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table.ant-table-bordered .ant-table-tbody > tr > td) {
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td) {
      background-color: #f7f9fc;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-summary td) {
      background: #f7f9fc;
      border-color: #e8e9ec !important;
    }
  }
  .item-warp {
    width: 100%;
    display: flex;
    .item-warp-img {
      margin-top: 3px;
      margin-right: 5px;
      img {
        display: block;
        width: 15px;
        height: 15px;
      }
    }
    .item-warp-goods-img {
      width: 34px;
      height: 34px;
      margin-top: 3px;
      margin-right: 5px;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
    .item-warp-content {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
      .item-warp-content-title {
        @include text_overflow(1);
      }
      .item-warp-content-code {
        color: var(--text-color-5);
      }
    }
  }
  .actions-warp {
    margin-bottom: 0px !important;
  }
  :deep(.ant-table-sticky-scroll) {
    display: none;
  }
</style>
