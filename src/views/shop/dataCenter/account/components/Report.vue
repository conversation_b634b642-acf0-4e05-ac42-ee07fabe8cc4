<template>
  <div class="warp">
    <a-table
      sticky
      :columns="state.fixedColumns"
      :data-source="state.dataSource"
      :pagination="state.pagination"
      :loading="state.loading"
      :scroll="{ x: 2000 }"
      @change="pageChange"
      :sortDirections="['descend', 'ascend']"
      :showSorterTooltip="false"
      bordered
      size="small"
      :rowKey="(record) => record.account_id"
      :row-selection="{
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange
      }"
    >
      <template #headerCell="{ column }">
        <template v-if="headerCell.includes(column.dataIndex)">
          <div>
            <span>{{ column.title }}</span>
            <a-tooltip>
              <template #title>{{ column.text }}</template>
              <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
            </a-tooltip>
          </div>
        </template>
        <template v-if="column?.dataIndex === 'actions'">
          <a-space>
            <span>{{ column?.title }} </span>
            <span>
              <SetTableColumns
                class="cursor-pointer"
                v-model:data="state.fixedColumns"
                :column="newFixedColumns"
                :isChange="isChange"
              />
            </span>
          </a-space>
        </template>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'account_name'">
          <div class="item-warp">
            <div class="item-warp-img">
              <img :src="requireImg('order/o8.png')" alt="" v-if="record.media_type == 1" />
              <img :src="requireImg('order/o6.png')" alt="" v-if="record.media_type == 6" />
              <img :src="requireImg('order/o1.png')" alt="" v-if="record.media_type == 4" />
            </div>
            <div class="item-warp-content">
              <div>
                <a-tooltip>
                  <template #title>{{ record.account_name || '--' }}</template>
                  <span class="item-warp-content-title">{{ record.account_name || '--' }}</span>
                </a-tooltip>
              </div>
              <div class="number-id">ID：{{ record.account_id || '--' }}</div>
            </div>
          </div>
        </template>
        <!-- <template v-if="setFilder.includes(column.dataIndex)">
        <span>¥ {{ centsToYuan(record[column.dataIndex]) }}</span>
      </template> -->
        <template v-if="column.dataIndex === 'order_callback_ratio'">
          <div
            class="primary-color cursor-pointer"
            @click="actionChange('OrderRatio', record)"
            v-if="record.media_type !== 1 || (record.media_type === 1 && shopInfo.limit_callback === 1)"
          >
            <span v-if="record.ks_callback_setting.callback_order_switch == 1"> 已启用</span>
            <span v-else class="primary-color">未启用</span>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="column.dataIndex === 'amount_callback_ratio'">
          <div
            class="primary-color cursor-pointer"
            @click="actionChange('AmountRatio', record)"
            v-if="record.media_type !== 1 || (record.media_type === 1 && shopInfo?.limit_callback === 1)"
          >
            <span v-if="record.ks_callback_setting.callback_money_switch == 1"> 已启用</span>
            <span v-else class="primary-color">未启用</span>
          </div>
          <div v-else>--</div>
        </template>
        <template v-if="column.dataIndex === 'ctr'">
          <span>{{ record.ctr }}%</span>
        </template>
        <template v-if="column.dataIndex === 'callback_cost'">
          <span>￥{{ centsToYuan(record.callback_cost) }}</span>
        </template>
        <template v-if="column.dataIndex === 'callback_amount'">
          <span>￥{{ centsToYuan(record.callback_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'callback_rate'">
          <span>{{ record.callback_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'callback_count'">
          <span>{{ record.callback_count }}</span>
        </template>
        <template v-if="column.dataIndex === 'callback_roi'">
          <span>{{ record.callback_roi }}</span>
        </template>

        <template v-if="column.dataIndex === 'arrival_rate'">
          <span>{{ record.arrival_rate }}%</span>
        </template>

        <template v-if="column.dataIndex === 'cost'">
          <span>¥ {{ centsToYuan(record.cost) }}</span>
        </template>
        <template v-if="column.dataIndex === 'thousand_display_price'">
          <span>¥ {{ centsToYuan(record.thousand_display_price) }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_amount_avg'">
          <span>¥ {{ centsToYuan(record.order_amount_avg) }}</span>
        </template>
        <template v-if="column.dataIndex === 'conversions_cost'">
          <span>¥ {{ centsToYuan(record.conversions_cost) }}</span>
        </template>
        <template v-if="column.dataIndex === 'loss_amount'">
          <span>¥ {{ centsToYuan(record.loss_amount) }}</span>
        </template>

        <template v-if="column.dataIndex === 'order_amount'">
          <span>¥ {{ centsToYuan(record.order_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_amount_turnover'">
          <span>¥ {{ centsToYuan(record.order_amount_turnover) }}</span>
        </template>
        <template v-if="column.dataIndex === 'account_loss_roi'">
          <span class="loss_roi"> {{ record.account_loss_roi }}</span>
        </template>
        <template v-if="column.dataIndex === 'roi'">
          <span class="loss_roi"> {{ record.roi }}</span>
        </template>
        <template v-if="column.dataIndex === 'account_turnover_roi'">
          <span class="loss_roi"> {{ record.account_turnover_roi }}</span>
        </template>
        <template v-if="column.dataIndex === 'account_loss_amount'">
          <span>¥ {{ centsToYuan(record.account_loss_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'refund_amount'">
          <span>¥ {{ centsToYuan(record.refund_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'pay_time_refund_amount'">
          <span>¥ {{ centsToYuan(record.pay_time_refund_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_cost'">
          <span>¥ {{ centsToYuan(record.order_cost) }}</span>
        </template>
        <template v-if="column.dataIndex === 'remarks'">
          <a-tooltip>
            <template #title>{{ record.remarks }}</template>
            <div class="zhremarks">{{ record.remarks || '--' }}</div>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'updated_at'">
          <span>{{ record.updated_at || '--' }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_turnover_cost'">
          <span>¥ {{ centsToYuan(record.order_turnover_cost) }}</span>
        </template>

        <template v-if="column.dataIndex === 'refund_rate'">
          <span>{{ record.refund_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'conversions_rate'">
          <span>{{ record.conversions_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'order_conversion_rate'">
          <span>{{ record.order_conversion_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'pay_conversion_rate'">
          <span>{{ record.pay_conversion_rate }}%</span>
        </template>

        <template v-if="column.dataIndex === 'pay_time_refund_rate'">
          <span>{{ record.pay_time_refund_rate }}%</span>
        </template>

        <template v-if="column.dataIndex === 'video_outer_play3s_rate'">
          <span>{{ record.video_outer_play3s_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'video_outer_play25_rate'">
          <span>{{ record.video_outer_play25_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'video_outer_play50_rate'">
          <span>{{ record.video_outer_play50_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'video_outer_play75_rate'">
          <span>{{ record.video_outer_play75_rate }}%</span>
        </template>

        <template v-if="column.dataIndex === 'actions'">
          <a-space :size="[0, 0]" class="actions-warp" wrap>
            <a-button style="padding-left: 0px" size="small" type="link" @click="updateData(record)">更新</a-button>

            <a-button style="padding-left: 0px" size="small" type="link" @click="actionChange('AddRemark', record)"
              >添加备注</a-button
            >
            <!--          <a-button size="small" type="link" @click="actionChange('SetForewarning', '设置账户预警')"-->
            <!--            >设置账户预警</a-button-->
            <!--          >-->
            <a-button style="padding-left: 0px" size="small" type="link" @click="actionChange('SetProfitLoss', record)"
              >设置盈亏ROI</a-button
            >
            <a-button style="padding-left: 0px" size="small" type="link" @click="actionChange('PeriodReport', record)"
              >时段报表</a-button
            >
            <a-button style="padding-left: 0px" size="small" type="link" @click="actionChange('PromoteProduct', record)"
              >推广商品</a-button
            >
            <a-button
              style="padding-left: 0px"
              size="small"
              type="link"
              v-if="record.media_type == 1"
              @click="actionChange('ReplaceLink', record)"
              >替换链接</a-button
            >
            <a-button
              style="padding-left: 0px"
              size="small"
              type="link"
              v-if="record.media_type == 1"
              @click="actionChange('ReplaceRecord', record)"
              >替换记录</a-button
            >
          </a-space>
        </template>
      </template>

      <template #summary>
        <a-table-summary fixed v-if="state.dataSource.length">
          <a-table-summary-row>
            <a-table-summary-cell style="left: 0px; position: sticky !important; z-index: 100"> </a-table-summary-cell>
            <template v-for="(item, index) in state.fixedColumns">
              <template v-if="['account_name'].includes(item.dataIndex)">
                <a-table-summary-cell :index="index">总计: {{ state.pagination.total }}</a-table-summary-cell>
              </template>
              <template
                v-else-if="
                  ![
                    'actions',
                    'admin_name',
                    'remarks',
                    'updated_at',
                    'order_callback_ratio',
                    'amount_callback_ratio'
                  ].includes(item.dataIndex)
                "
              >
                <template
                  v-if="
                    [
                      'ctr',
                      'arrival_rate',
                      'order_conversion_rate',
                      'pay_conversion_rate',
                      'conversions_rate',
                      'refund_rate',
                      'pay_time_refund_rate',
                      'video_outer_play3s_rate',
                      'video_outer_play25_rate',
                      'video_outer_play50_rate',
                      'video_outer_play75_rate',
                      'callback_rate'
                    ].includes(item.dataIndex)
                  "
                >
                  <a-table-summary-cell :index="index"> {{ state?.sum[item.dataIndex] }}% </a-table-summary-cell>
                </template>
                <template v-else>
                  <a-table-summary-cell :index="index">
                    {{ setFider(setFilder, item.dataIndex) }}
                  </a-table-summary-cell>
                </template>
              </template>

              <template v-else>
                <a-table-summary-cell :index="index"></a-table-summary-cell>
              </template>
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </a-table>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref, watch } from 'vue'
  import { centsToYuan } from '@/utils'
  import {
    get_statement_account_latitude,
    get_statement_account_latitude_hour,
    get_after_sale_dimension,
    manual_account_sync
  } from '../index.api'
  import moment from 'moment'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  import { requireImg } from '@/utils'
  import datas from '../data'
  import { useApp } from '@/hooks'
  import { cloneDeep } from 'lodash-es'

  const { isMobile, shopInfo } = useApp()
  const { setFilder, headerCell } = datas()
  const emits = defineEmits(['eventBtn', 'getDimension'])

  const setFider = (arr, key) => {
    if (arr.includes(key)) {
      return `¥ ${centsToYuan(state?.sum[key])}`
    } else {
      return state?.sum[key]
    }
  }

  const after_sale_dimension = ref(1)
  const newFixedColumns = ref([])
  const fixedColumns = ref([
    {
      title: '账户名称',
      dataIndex: 'account_name',
      fixed: isMobile.value ? false : 'left',
      width: 200
    },
    {
      title: '回传订单比例',
      dataIndex: 'order_callback_ratio',
      text: '账户报表和计划报表回传订单比例开关关闭时，使用创建广告链接时设置的回传比例；若计划报表开启设置回传订单比例，则优先使用计划报表比例，未设置则使用账户报表中比例',
      width: 160
    },
    {
      title: '回传金额比例',
      dataIndex: 'amount_callback_ratio',
      text: '账户报表和计划报表回传金额比例开关关闭时，使用创建广告链接时设置的回传金额；若计划报表开启设置回传订单金额，优先使用计划报表比例，未设置则使用账户报表中比例',
      width: 160
    },

    {
      title: '计划数量',
      dataIndex: 'ad_count',
      width: 160
    },
    {
      title: '总消耗',
      dataIndex: 'cost',
      width: 160,
      text: '广告投放总共付出的费用成本',
      sorter: true
    },
    {
      title: '展示数',
      dataIndex: 'view_count',
      width: 160,
      sorter: true
    },
    {
      title: '点击量',
      dataIndex: 'valid_click_count',
      width: 140,
      text: '有效且被计费的点击次数',
      sorter: true
    },
    {
      title: '千次展示均价',
      dataIndex: 'thousand_display_price',
      width: 160,
      text: '总消耗/曝光量*1000',
      sorter: true
    },
    {
      title: '点击率',
      dataIndex: 'ctr',
      width: 140,
      text: '点击量/曝光量*100%',
      sorter: true
    },
    {
      title: '抵达率',
      dataIndex: 'arrival_rate',
      width: 140,
      text: '小程序访客数/计划点击数',
      sorter: true
    },
    {
      title: '平均客单价',
      dataIndex: 'order_amount_avg',
      width: 160,
      text: '支付总金额/支付订单数',
      sorter: true
    },
    {
      title: '转化数',
      dataIndex: 'conversions_count',
      width: 140,
      sorter: true
    },
    {
      title: '转化成本',
      dataIndex: 'conversions_cost',
      width: 140,
      text: '总消耗/转化数',
      sorter: true
    },
    {
      title: '转化率',
      dataIndex: 'conversions_rate',
      width: 120,
      text: '转化数/点击量',
      sorter: true
    },
    {
      title: '下单转化率',
      dataIndex: 'order_conversion_rate',
      width: 120,
      text: '(下单人数/访客数)X 100%',
      sorter: true
    },
    {
      title: '支付订单数',
      dataIndex: 'order_count',
      width: 140,
      text: '支付成功的订单数量',
      sorter: true
    },
    {
      title: '支付总金额',
      dataIndex: 'order_amount',
      width: 140,
      text: '支付成功订单的总金额',
      sorter: true
    },
    {
      title: '支付ROI',
      dataIndex: 'roi',
      width: 140,
      text: '支付总金额/总消耗',
      sorter: true
    },
    {
      title: '支付转化率',
      dataIndex: 'pay_conversion_rate',
      width: 120,
      text: '(支付人数/访客数)X 100%',
      sorter: true
    },
    {
      title: '成交订单数',
      dataIndex: 'order_count_turnover',
      width: 140,
      text: '支付订单数-退款量',
      sorter: true
    },
    {
      title: '成交总金额',
      dataIndex: 'order_amount_turnover',
      width: 140,
      text: '支付总金额-退款总金额',
      sorter: true
    },
    {
      title: '成交ROI',
      dataIndex: 'account_turnover_roi',
      width: 140,
      text: '成交总金额/总消耗',
      sorter: true
    },
    {
      title: '盈亏ROI',
      dataIndex: 'account_loss_roi',
      width: 140,
      text: '（总盈亏-成交总金额）/总消耗',
      sorter: true
    },
    {
      title: '总盈亏',
      dataIndex: 'account_loss_amount',
      width: 140,
      text: '成交总金额-总消耗*盈亏ROI',
      sorter: true
    },
    {
      title: '退单量',
      dataIndex: 'refund_count',
      width: 210,
      text: '指定时间段内完成退款的售后单数量（仅退款、退货退款 ），按照退款完成时间进行统计',
      sorter: true
    },
    {
      title: '退单量',
      dataIndex: 'pay_time_refund_count',
      width: 180,
      text: '指定时间段内完成退款的售后单数量（仅退款、退货退款 ），按照订单支付时间进行统计',
      sorter: true
    },
    {
      title: '退款总金额',
      dataIndex: 'refund_amount',
      width: 240,
      text: '指定时间段内完成退款的售后单退款金额总和（仅退款、退货退款 ），按照退款完成时间进行统计',
      sorter: true
    },
    {
      title: '退款总金额',
      dataIndex: 'pay_time_refund_amount',
      width: 220,
      text: '指定时间段内完成退款的售后单退款金额总和（仅退款、退货退款 ），按照订单支付时间进行统计',
      sorter: true
    },
    {
      title: '退单率',
      dataIndex: 'refund_rate',
      width: 210,
      text: '退款单数（退款完成时间）/支付订单数*100%',
      sorter: true
    },
    {
      title: '退单率',
      dataIndex: 'pay_time_refund_rate',
      text: '退款单数（支付时间）/支付订单数*100%',
      width: 200,
      sorter: true
    },
    {
      title: ' 扣除回传后成本',
      dataIndex: 'callback_cost',
      text: '除去扣除的回传订单，实际回传的成本',
      width: 160
    },
    {
      title: ' 扣除回传后订单数',
      dataIndex: 'callback_count',
      text: '除去扣除的回传订单，实际回传的订单数',
      width: 160
    },
    {
      title: ' 扣除回传金额后的金额',
      dataIndex: 'callback_amount',
      text: '除去扣除的回传订单，实际回传的订单金额',
      width: 180
    },
    {
      title: ' 扣除回传后ROI',
      dataIndex: 'callback_roi',
      text: '扣除回传金额后的金额/总消耗',
      width: 160
    },
    {
      title: ' 实际回传率',
      dataIndex: 'callback_rate',
      text: '实际回传订单数/总订单数*100%',
      width: 160
    },
    {
      title: '播放量',
      dataIndex: 'video_outer_play_count',
      width: 160
    },
    {
      title: '3秒播放数',
      dataIndex: 'video_outer_play3s_count',
      width: 160
    },
    {
      title: '3秒播放率',
      dataIndex: 'video_outer_play3s_rate',
      width: 160
    },
    {
      title: '25%进度播放数',
      dataIndex: 'video_outer_play25_count',
      width: 160
    },
    {
      title: '25%进度播放率',
      dataIndex: 'video_outer_play25_rate',
      width: 160
    },
    {
      title: '50%进度播放数',
      dataIndex: 'video_outer_play50_count',
      width: 160
    },
    {
      title: '50%进度播放率',
      dataIndex: 'video_outer_play50_rate',
      width: 160
    },
    {
      title: '75%进度播放数',
      dataIndex: 'video_outer_play75_count',
      width: 160
    },
    {
      title: '75%进度播放率',
      dataIndex: 'video_outer_play75_rate',
      width: 160
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      width: 180
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      width: 150
    },
    {
      title: '下单成本',
      dataIndex: 'order_cost',
      width: 140,
      sorter: true
    },
    {
      title: '成交成本',
      dataIndex: 'order_turnover_cost',
      width: 140,
      sorter: true
    },
    {
      title: '负责人',
      dataIndex: 'admin_name',
      width: 120
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 220,
      disabled: true,
      fixed: isMobile.value ? false : 'right'
    }
  ])

  // 操作时间
  const actionChange = (type, data) => {
    emits('eventBtn', { type, data })
  }
  const state = reactive({
    loading: true,
    dataSource: [],
    fixedColumns: cloneDeep(newFixedColumns),
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 10,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    },
    sum: {},
    initQuery: {
      page: 1,
      page_size: 10,
      begin_time: moment().format('YYYY-MM-DD'),
      end_time: moment().format('YYYY-MM-DD'),
      field: undefined,
      after_sale_dimension: undefined,
      order: undefined
    },
    selectionRowsPlus: [],
    selectionItem: [], // 表格选择的Item
    selectedRowKeys: [],
    checked_list: [],
    filtration: null
  })

  function delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  function makeRequest(data) {
    // 模拟请求，这里可以替换为你的实际请求逻辑
    getTime(data)
  }
  let isOne = true
  async function makeRequestsInBatches(array, batchSize, interval) {
    let index = 0
    while (index < array.length && !isOne) {
      console.log('走这里 循环')
      const batch = array.slice(index, index + batchSize)

      const promises = batch.map((item) => makeRequest(item))
      const results = await Promise.all(promises)

      await delay(interval)

      index += batchSize
    }

    if (index < array.length) {
      const remainingBatch = array.slice(index)
      const remainingPromises = remainingBatch.map((item) => makeRequest(item))
      const remainingResults = await Promise.all(remainingPromises)
    }
    return index < array.length
  }
  async function batchRequests(array, batchSize, interval) {
    isOne = false
    return await makeRequestsInBatches([...array], batchSize, interval)
  }

  const initPage = async (data, ai) => {
    console.log(data, 'initPage')

    state.loading = true
    if (data) {
      tabColumns(data?.after_sale_dimension)
    }
    isOne = true
    state.loading = !ai
    state.filtration = data ? data : state.filtration
    const result = await get_statement_account_latitude({ ...state.initQuery, ...state.filtration })
    state.dataSource = result?.data?.list || []

    // let arr = []
    // state.dataSource.forEach((v) => {
    //   // 如果下单数 大于 转化数
    //   if (v.order_count > v.conversions_count) {
    //     arr.push(v.account_id)
    //   }
    // })
    state.pagination.pageSize = result?.data?.size || 10
    state.pagination.total = result?.data?.total_num || 0
    state.pagination.current = result?.data?.page || 1
    state.sum = result?.data?.sum || {}
    state.loading = false

    // if (arr.length != 0 && isOne && !ai) {
    //   if (!(await batchRequests(arr, 3, 500))) {
    //     initPage(null, true)
    //   }
    // }
  }
  const updateData = async (row) => {
    const params = { ...state.initQuery, ...state.filtration }

    await manual_account_sync({
      account_id: row.account_id,
      media_type: 1,
      date: params.begin_time
    })
    initPage()
  }

  // 请求时段报表
  const getTime = async (v) => {
    try {
      let res = await get_statement_account_latitude_hour({ account_id: v })
      console.log(res, '222')
    } catch (error) {}
  }

  function createHash(hashLength = 10) {
    return Array.from(Array(Number(hashLength) || 24), () => Math.floor(Math.random() * 36).toString(36)).join('')
  }
  const isChange = ref(createHash())

  const tabColumns = (type) => {
    type = type ? type : state.initQuery.after_sale_dimension
    const pay_time_arr = ['pay_time_refund_rate', 'pay_time_refund_count', 'pay_time_refund_amount']
    const refund_time_arr = ['refund_rate', 'refund_count', 'refund_amount']
    const filterArr = type == 1 ? pay_time_arr : refund_time_arr
    newFixedColumns.value = fixedColumns.value.filter((item) => !filterArr.includes(item.dataIndex))
    state.fixedColumns = newFixedColumns.value
    isChange.value = createHash()
  }

  const pageChange = (data, filters, sorter) => {
    if (sorter?.order) {
      state.initQuery.field = sorter.field
      state.initQuery.order = sorter.order === 'ascend' ? 'asc' : 'desc'
    } else {
      state.initQuery.field = undefined
      state.initQuery.order = undefined
    }
    state.initQuery.page = data.current
    state.initQuery.page_size = data.pageSize
    state.pagination.current = data.current
    if (state.filtration) {
      state.filtration.page = data.current
    }

    initPage()
  }
  onMounted(async () => {
    const data = await get_after_sale_dimension()
    state.initQuery.after_sale_dimension = data.data
    after_sale_dimension.value = data.data
    tabColumns(after_sale_dimension.value)
    emits('getDimension', data.data)
    initPage()
  })
  const onSelectChange = (selectedRowKeys, selectedRows) => {
    state.selectedRowKeys = selectedRowKeys
  }

  const onSelect = (record, selected) => {
    console.log('onSelect', record, selected)
    selected
      ? state.selectionRowsPlus.push(record)
      : state.selectionRowsPlus.splice(
          state.selectionRowsPlus.findIndex((x) => x.account_id === record.account_id),
          1
        )
  }
  const onSelectAll = (selected, selectedRows, changeRows) => {
    console.log('onSelectAll', selected, selectedRows, changeRows)
    state.selectionRowsPlus = selected
      ? state.selectionRowsPlus.concat(changeRows)
      : state.selectionRowsPlus.filter((x) => !changeRows.find((i) => i.account_id === x.account_id))
  }
  const getCheckIds = () => {
    return state.selectionRowsPlus.map((item) => item.account_id)
  }
  watch(
    () => state.selectionRowsPlus,
    () => {
      state.checked_list = state.selectionRowsPlus.map((v) => {
        return {
          id: v.account_id
        }
      })
    },
    { deep: true }
  )
  defineExpose({
    initPage,
    getCheckIds
  })
</script>

<style scoped lang="scss">
  @import './src/assets/css/mixin_scss_fn.scss';
  .warp {
    :deep(.ant-table-column-sorter) {
      color: #939599;
    }
    :deep(.ant-table-wrapper .ant-table-column-sorters:hover .ant-table-column-sorter) {
      color: #939599;
    }
    :deep(.ant-table-wrapper .ant-table) {
      border-radius: 4px !important;
      overflow: hidden;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table-thead > tr > th) {
      font-weight: 400;
      background: #f8f8f9;
      color: #313233;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-tbody > tr > td) {
      vertical-align: middle;
      color: #313233;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table.ant-table-bordered .ant-table-tbody > tr > td) {
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td) {
      background-color: #f7f9fc;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-summary td) {
      background: #f7f9fc;
      border-color: #e8e9ec !important;
    }
  }
  .item-warp {
    width: 100%;
    display: flex;
    .item-warp-img {
      width: 15px;
      height: 15px;
      margin-top: 3px;
      margin-right: 5px;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
    .item-warp-content {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
      .item-warp-content-title {
        @include text_overflow(1);
      }
      .item-warp-content-code {
        color: #939599;
      }
    }
  }
  .actions-warp {
    margin-bottom: 0px !important;
  }
  .zhremarks {
    @include text_overflow(2);
  }
  .loss_roi {
    color: #1677ff;
  }
  :deep(.ant-table-sticky-scroll) {
    display: none;
  }
  .primary-color {
    color: var(--primary-color);
  }
</style>
