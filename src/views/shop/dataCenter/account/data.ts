import { requireImg } from '@/utils'
import moment from 'moment'
import dayjs from 'dayjs'

// const disabledDate = (current: any) => {
//   const today = dayjs()
//   const thirtyDaysAgo = today.subtract(30, 'day')
//   const decemberTwentyFirst = dayjs('2023-12-1').startOf('day')
//   current = current.startOf('day')
//   return (
//     current.isBefore(decemberTwentyFirst, 'day') ||
//     current.isBefore(thirtyDaysAgo, 'day') ||
//     current.isAfter(today, 'day')
//   )
// }
export default function datas() {
  const searchList = [
    {
      type: 'select',
      field: 'media_types',
      value: undefined,
      props: {
        placeholder: '请选择广告平台',
        mode: 'multiple',
        options: [
          {
            value: 1,
            label: '广点通'
          },
          {
            value: 4,
            label: '磁力引擎'
          },
          {
            value: 6,
            label: '巨量引擎'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'account_id',
      value: undefined,
      props: {
        placeholder: '请输入账户ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'account_name',
      value: undefined,
      props: {
        placeholder: '请输入账户名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'admin',
      field: 'admin_id',
      value: null,
      props: {
        placeholder: '请选择负责人'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'input.text',
      field: 'remark',
      value: undefined,
      props: {
        placeholder: '请输入备注内容'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'date',
      field: 'created_at',
      value: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
      range: 'NearlyThirty',
      props: {
        placeholder: ['开始时间', '结束时间'],
        disabledDate: false,
        allowClear: false
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },
    {
      type: 'select',
      field: 'after_sale_dimension',
      value: 1,
      props: {
        placeholder: '请选择统计维度',
        allowClear: false,
        options: [
          {
            value: 1,
            label: '退款完成时间'
          },
          {
            value: 2,
            label: '支付时间'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ]

  const tabs = [
    {
      id: 1,
      icon: requireImg('order/o8.png'),
      noicon: requireImg('order/star_drak.png'),
      title: '广点通'
    }
    // {
    //   id: 2,
    //   icon: requireImg('order/o1.png'),
    //   noicon: requireImg('order/star_bright.png'),
    //   title: '磁力引擎'
    // }
  ]

  const iconType = (type: any) => {
    let status = {
      4: requireImg('bbimg/gd1.png'),
      1: requireImg('order/o8.png')
    }
    return status[type]
  }

  const modalConfigData = {
    open: false,
    title: '',
    type: '',
    data: null
  }

  const modalTitle = {
    AddRemark: '添加备注',
    SetForewarning: '设置账户预警',
    SetProfitLoss: '设置盈亏ROI',
    PeriodReport: '时段报表',
    PromoteProduct: '推广商品',
    OrderRatio: ' 修改订单回传比例',
    AmountRatio: ' 修改金额回传比例',
    ReplaceData: '替换链接',
    ReplaceRecord: '替换记录',
    ReplaceDetail: '替换记录详情',
    ReplaceLink: '替换链接'
  }
  const sourceType = {
    1: '小程序',
    2: '拼多多',
    3: '淘宝',
    4: '京东'
  }
  const setModalWidth = (type: string) => {
    if (['SetForewarning', 'PromoteProduct', 'OrderRatio', 'AmountRatio'].includes(type)) return 800
    if (['PeriodReport'].includes(type)) return '80%'
    if (['ReplaceLink'].includes(type)) return 800
    if (['ReplaceData'].includes(type)) return 660
    if (['ReplaceRecord'].includes(type)) return 900
    return 520
  }
  const headerCell = [
    'cost',
    'valid_click_count',
    'thousand_display_price',
    'ctr',
    'order_amount_avg',
    'conversions_cost',
    'conversions_rate',
    'order_count',
    'order_amount',
    'order_count_turnover',
    'order_amount_turnover',
    'account_turnover_roi',
    'roi',
    'account_loss_roi',
    'roi',
    'account_loss_amount',
    'refund_count',
    'refund_amount',
    'pay_time_refund_amount',
    'pay_time_refund_rate',
    'pay_time_refund_count',
    'refund_rate',
    'arrival_rate',
    'order_conversion_rate',
    'pay_conversion_rate',
    'order_callback_ratio',
    'amount_callback_ratio',
    'callback_cost',
    'callback_count',
    'callback_amount',
    'callback_roi',
    'callback_rate'
  ]
  const setFilder = [
    'cost',
    'loss_amount',
    'thousand_display_price',
    'order_amount_avg',
    'conversions_cost',
    'order_amount',
    'order_amount_turnover',
    'refund_amount',
    'pay_time_refund_amount',
    'order_cost',
    'order_turnover_cost',
    'account_loss_amount'
  ]
  const timeObj = {
    '0': '00:00',
    1: '01:00',
    2: '02:00',
    3: '03:00',
    4: '04:00',
    5: '05:00',
    6: '06:00',
    7: '07:00',
    8: '08:00',
    9: '09:00',
    10: '10:00',
    11: '11:00',
    12: '12:00',
    13: '13:00',
    14: '14:00',
    15: '15:00',
    16: '16:00',
    17: '17:00',
    18: '18:00',
    19: '19:00',
    20: '20:00',
    21: '21:00',
    22: '22:00',
    23: '23:00',
    24: '24:00'
  }
  return {
    searchList,
    tabs,
    sourceType,
    iconType,
    modalConfigData,
    modalTitle,
    setModalWidth,
    setFilder,
    timeObj,
    headerCell
  }
}
