<template>
  <div>
    <SearchBaseLayout
      style="margin-bottom: 24px"
      :data="state.searchConfig.data"
      @changeValue="changeValue"
      :actions="state.searchConfig.options"
      :btnNames="['export']"
    />

    <TableZebraCrossing :data="tableData" @change="pageChange">
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.dataIndex === 'status'">
          <div>{{ state.status[scope.record.status] }}</div>
        </template>
        <template v-if="scope.column.dataIndex === 'money'">
          <div v-if="scope.record.type == 1">{{ scope.record.money }}</div>
          <div v-else>{{ scope.record.refund_money }}</div>
        </template>

        <template v-if="scope.column.dataIndex === 'type'">
          <div class="flex">
            <div>{{ state.type[scope.record.type] }}</div>
          </div>
        </template>
      </template>
    </TableZebraCrossing>
  </div>
</template>

<script setup lang="tsx">
  import useData from '../src/record'
  const { state, init, pageChange, tableData, changeValue } = useData()
  init()
</script>

<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';
  .text_overflow {
    @include text_overflow(1);
    white-space: wrap;
  }
</style>
