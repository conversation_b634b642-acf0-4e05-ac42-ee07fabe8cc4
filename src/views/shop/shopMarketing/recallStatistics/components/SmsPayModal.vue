<template>
  <div>
    <div>选择套餐（0.045/条）</div>

    <a-radio-group v-model:value="state.select" size="large" class="list mt-20px mb-50px" @change="onselect">
      <a-radio-button :value="item" v-for="(item, index) in state.numList" :key="index" class="item"
        >{{ item }}条</a-radio-button
      >
    </a-radio-group>
    <div class="flex justify-between flex-items-center">
      <div>
        总计： <span class="color-red">{{ totalPrice }}元</span>
      </div>
      <div>
        <a-button @click="close">取消</a-button>
        <a-button type="primary" @click="submit">确认</a-button>
      </div>
    </div>

    <a-modal v-model:open="state.visible" :footer="null" destroyOnClose @cancel="onClosePay" centered>
      <div class="flex flex-items-center justify-center mt-20px">
        <img src="@/assets/images/freight/w_pay.png" alt="" class="w-14px h-12px mr-4px" />
        微信支付
        <span class="color-red">{{ totalPrice }}元</span>
      </div>

      <div class="img-box bg-#FAFBFC flex flex-center">
        <img :src="state.payUrl" v-if="state.payUrl" alt="" class="w-152px h-152px" />
        <a-spin v-else />
      </div>
      <div class="flex flex-justify-end">
        <a-button @click="onClosePay" type="primary">关闭</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="tsx">
  import { reactive, computed, ref } from 'vue'

  import { smsPay, isPay } from '../index.api'
  import { message } from 'ant-design-vue'
  const emits = defineEmits(['close'])

  let timer: any = ref(null)
  const state = reactive({
    numList: [300, 500, 1000, 1500, 2000, 3000, 5000, 10000],
    select: 0,
    payUrl: '',
    visible: false,
    id: ''
  })
  const onselect = (e: any) => {
    console.log(e, 'hhhhhhhh')

    state.select = e.target.value
  }
  const totalPrice: any = computed(() => {
    return state.select * 0.045
  })
  const close = () => {
    emits('close')
  }
  const submit = async () => {
    if (!totalPrice.value || !state.select) {
      return
    }
    try {
      let res = await smsPay({ num: state.select, money: totalPrice.value * 100 })
      state.id = res.data.out_trade_no
      state.payUrl = res.data.image

      if (res.data.out_trade_no) {
        state.visible = true
        // emits('close')
        //查询是否支付
        timer.value = setInterval(() => {
          paySuccess(res.data.out_trade_no)
        }, 2000)
      }
    } catch (error) {}
  }
  //订单是否支付
  const paySuccess = async (id: string) => {
    let res = await isPay({ order_no: state.id })
    if (res.data.is_pay) {
      clearInterval(timer.value)
      state.visible = false
      message.success('支付成功')
      emits('close')
    } else {
      console.log('未支付')
    }
  }
  const onClosePay = () => {
    state.visible = false
    clearInterval(timer.value)
  }
</script>

<style lang="scss" scoped>
  //   @import './src/assets/css/mixin_scss_fn.scss';

  .list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    .item {
      // padding: 10px;
      background: #f9f9f9;
      // border-radius: 6px;
      text-align: center;
    }
  }
  .img-box {
    width: 176px;
    height: 176px;
    margin: 16px auto;
  }
</style>
