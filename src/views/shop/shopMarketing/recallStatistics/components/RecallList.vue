<template>
  <div>
    <SearchBaseLayout
      style="margin-bottom: 24px"
      :data="state.searchConfig.data"
      @changeValue="changeValue"
      :btnNames="['export']"
      :actions="state.searchConfig.options"
    />

    <TableZebraCrossing :data="tableData" @change="pageChange">
      <template #headerCell="{ scope }">
        <div v-if="scope.column.tips">
          <span>{{ scope.column.title }}</span>
          <a-tooltip>
            <template #title>{{ scope.column.tips }}</template>
            <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
          </a-tooltip>
        </div>
      </template>
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.dataIndex === 'product_name'">
          <div class="flex goods_info">
            <div class="img">
              <a-image
                style="width: 40px; height: 40px; border-radius: 6px"
                :src="scope.record.product_img"
                fit="fill"
              />
            </div>
            <div class="ml-5px">
              <a-tooltip placement="topLeft">
                <template #title>{{ scope.record.product_name }}</template>
                <div class="text">
                  {{ scope.record.product_name }}
                </div>
              </a-tooltip>
              <!-- goods_info_data_number -->
              <span class="number-id">商品ID：{{ scope.record.product_code }}</span>
            </div>
          </div>
        </template>
        <template v-if="scope.column.dataIndex === 'recall_num'">
          <a-button type="link" @click="goOrderPage(scope.record, 1)">{{ scope.record.recall_num }}</a-button>
        </template>
        <template v-if="scope.column.dataIndex === 'repeat_order_num'">
          <a-button type="link" @click="goOrderPage(scope.record, 2)">{{ scope.record.repeat_order_num }}</a-button>
        </template>
        <template v-if="scope.column.dataIndex === 'new_product_order_num'">
          <a-button type="link" @click="goOrderPage(scope.record, 3)">{{
            scope.record.new_product_order_num
          }}</a-button>
        </template>
        <template v-if="scope.column.dataIndex === 'new_product_order_money'">
          {{ centsToYuan(scope.record.new_product_order_money) }}
        </template>
        <template v-if="scope.column.dataIndex === 'repeat_order_money'">
          {{ centsToYuan(scope.record.repeat_order_money) }}
        </template>

        <template v-if="scope.column.dataIndex === 'recall_money'">
          <div>{{ centsToYuan(scope.record.recall_money) }}</div>
        </template>
        <template v-if="scope.column.dataIndex === 'handle'">
          <a-button type="link" @click="goOrderPage(scope.record)">查看订单</a-button>
        </template>
      </template>
    </TableZebraCrossing>
  </div>
</template>

<script setup lang="tsx">
  import useData from '../src/recallData'
  import { useRouter } from 'vue-router'
  import { centsToYuan } from '@/utils'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  const router = useRouter()
  const { state, init, pageChange, tableData, changeValue } = useData()
  const goOrderPage = (item: any, type: any) => {
    const href = router.resolve({
      name: 'ShopOrderQuery',
      query: {
        productId: item.product_code,
        sms_source: type,
        created_at: state.params.created_at
      }
    })
    window.open(href.href, '_blank')
  }
  init()
</script>

<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';
  .text {
    @include text_overflow(1);
    white-space: wrap;
  }

  :deep(img) {
    max-width: initial !important;
  }
</style>
