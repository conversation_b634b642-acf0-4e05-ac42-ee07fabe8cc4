<template>
  <div class="notify-modal-wrapper">
    <a-form
      class="notify-form-wrapper"
      :model="state.form"
      ref="ruleForm"
      :rules="rules"
      :labelCol="{ style: 'width: 110px' }"
      autocomplete="off"
    >
      <a-form-item label="策略名称" name="plan_name">
        <a-input
          placeholder="请输入策略名称"
          v-model:value="state.form.plan_name"
          :disabled="item?.id"
          show-count
          :maxlength="30"
        />
      </a-form-item>

      <a-form-item label="描述" name="description">
        <a-textarea
          class="flex-1"
          placeholder="请输入策略描述，最多200字"
          v-model:value="state.form.description"
          show-count
          :rows="5"
          :maxlength="200"
        />
      </a-form-item>
      <a-form-item label="策略类型" name="plan_type" required>
        <a-radio-group v-model:value="state.form.plan_type" @change="onChangeType" :disabled="item?.id">
          <a-radio :value="1">催付款</a-radio>
          <a-radio :value="2">复购</a-radio>
          <a-radio :value="3">上新</a-radio>
          <a-radio :value="5" v-if="shopInfo?.online_course_history == 1">在线课程</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="通知范围" name="send_resource_type" required v-if="[2, 3].includes(state.form.plan_type)">
        <a-radio-group v-model:value="state.form.send_resource_type" :disabled="item?.id">
          <a-radio :value="1">
            <div class="flex-y-center">
              新订单
              <a-tooltip placement="top" trigger="hover" width="380">
                <template #title> 策略生成后所产生的订单 </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-tooltip>
            </div>
          </a-radio>
          <a-radio :value="2">
            <div class="flex-y-center">
              历史订单
              <a-tooltip placement="top" trigger="hover" width="380">
                <template #title> 策略生成前所产生的订单 </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-tooltip></div
          ></a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        label="订单范围"
        :rules="[{ required: true, message: '请选择开始和结束时间' }]"
        v-if="state.form.send_resource_type == 2 && [2, 3].includes(state.form.plan_type)"
        name="resource_date"
      >
        <a-range-picker
          v-model:value="state.form.resource_date"
          class="picker"
          show-time
          :disabled="item?.id"
          valueFormat="YYYY-MM-DD HH:mm:ss"
          @change="changeDate"
          :disabled-date="disabledRangeDate"
          :disabled-time="disabledDateRangeTime"
          :placeholder="
            [2].includes(state.form.plan_type)
              ? ['确认收货开始时间', '确认收货结束时间']
              : ['下单开始时间', '下单结束时间']
          "
        />
        <!-- v-if="state.sms_count < state.order_count" -->
        <template #extra>
          订单数量{{ state.order_count }}条
          <span class="c-#FF4D4F" v-if="state.sms_count < state.order_count"
            >短信余额{{ state.sms_count }}条，余额条数不足，<span class="c-primary cursor-pointer" @click="openNewPage"
              >请充值>></span
            >
          </span>
        </template>
      </a-form-item>
      <a-form-item
        label="发送时机"
        :rules="[{ required: true, message: '请选择发送时机' }]"
        v-if="state.form.send_resource_type == 2 && [2, 3].includes(state.form.plan_type)"
        name="after_time"
      >
        <a-date-picker
          show-time
          :disabled="item?.id"
          :showNow="false"
          :allowClear="false"
          v-model:value="state.form.after_time"
          format="YYYY-MM-DD HH:mm:ss"
          valueFormat="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择时间"
          :disabled-date="disabledDate"
          :disabled-time="disabledDateTime"
        />
        <template #extra> 晚22:00至早8:00不支持发送营销短信 </template>
      </a-form-item>
      <a-form-item label="发送时机" v-if="[5].includes(state.form.plan_type)">
        <span>支付完成后发送短信</span>
      </a-form-item>
      <a-form-item
        label="发送时机"
        v-if="state.form.send_resource_type == 1 && state.form.plan_type !== 5"
        required
        class="mb-0!"
      >
        <a-row>
          <div class="flex">
            <div class="flex" v-if="state.form.plan_type == 1">
              <span class="ml-8px mr-8px line-height-32px">下单</span>
              <a-form-item
                name="after_time_hour"
                :rules="[
                  {
                    required: true,
                    validator: (_: any, value: any) => validatorNumber(state.form.after_time_hour),
                    trigger: ['change', 'blur']
                  }
                ]"
              >
                <a-input-number
                  v-model:value="state.form.after_time_hour"
                  :min="0"
                  :precision="0"
                  :controls="false"
                  style="width: 50px"
                  :disabled="item?.id"
                />
              </a-form-item>
              <span class="ml-8px mr-8px line-height-32px">小时</span>
              <a-form-item
                name="after_time_minute"
                :rules="[
                  {
                    required: true,
                    validator: (_: any, value: any) => validatorNumber(state.form.after_time_minute),
                    trigger: ['change', 'blur']
                  }
                ]"
              >
                <a-input-number
                  v-model:value="state.form.after_time_minute"
                  min="3"
                  max="59"
                  :precision="0"
                  :controls="false"
                  style="width: 50px"
                  :disabled="item?.id"
                />
              </a-form-item>
              <span class="ml-8px mr-8px line-height-32px">分钟后未支付发送短信</span>
            </div>
          </div>
          <div class="flex" v-if="[2, 3].includes(state.form.plan_type)">
            <span class="ml-8px mr-8px line-height-32px"
              >{{ state.form.plan_type == 3 ? '购买商品后' : '商品收货后' }}
            </span>
            <a-form-item
              name="after_time_day"
              :rules="[
                {
                  required: true,
                  validator: (_: any, value: any) => validatorNumber(state.form.after_time_day),
                  trigger: ['change', 'blur']
                }
              ]"
            >
              <a-input-number
                v-model:value="state.form.after_time_day"
                min="0"
                :precision="0"
                :controls="false"
                style="width: 50px"
                class="mr-8px"
                :disabled="item?.id"
              />
            </a-form-item>

            <span class="ml-8px mr-8px line-height-32px">天发送短信</span>
          </div>
        </a-row>
      </a-form-item>
      <a-form-item label="降价区间设置" name="reduction" v-if="state.form.plan_type == 1">
        <div class="flex-y-center item" v-for="(item, index) in state.form.price_reduction_configs" :key="index">
          <a-form-item
            class="w-120px"
            :name="'price' + index + '_min_price'"
            :rules="{
              validator: (_: any, __: string, callback: Function) => {
                if (!item.min_price) {
                  callback('请输入最小金额')
                }
                if (item.max_price && item.min_price && item.min_price >= item.max_price) {
                  callback('金额不能重复或交叉')
                }
                if (
                  item.min_price &&
                  index > 0 &&
                  item.min_price < Number(state.form?.price_reduction_configs?.[index - 1]?.max_price)
                ) {
                  callback('金额不能重复或交叉')
                }
                callback()
              },
              trigger: ['change', 'blur']
            }"
          >
            <a-input-number
              v-model:value="item.min_price"
              :min="0.01"
              placeholder="最小金额"
              :precision="2"
              :max="99999999"
              addonAfter="元"
              @blur="resetFieldCallbackList(state.form.price_reduction_configs.length)"
            >
            </a-input-number>
          </a-form-item>
          <div class="flex-none ml-4px mr-4px mt--16px c-#85878a">-</div>
          <a-form-item
            class="w-120px"
            :name="'price' + index + '_max_price'"
            :rules="{
              validator: (_: any, __: string, callback: Function) => {
                if (!item.max_price) {
                  callback('请输入最大金额')
                }
                if (item.max_price && item.min_price && item.max_price <= item.min_price) {
                  callback('金额不能重复或交叉')
                }
                let nextMin =
                  index + 1 <= (state.form.price_reduction_configs || [])?.length
                    ? state.form.price_reduction_configs?.[index + 1]?.min_price
                    : undefined
                if (nextMin && item.max_price && nextMin < Number(item.max_price)) {
                  callback('金额不能重复或交叉')
                }
                callback()
              },
              trigger: ['blur', 'change']
            }"
          >
            <a-input-number
              :min="0.01"
              v-model:value="item.max_price"
              placeholder="最大金额"
              :precision="2"
              :max="99999999"
              @blur="resetFieldCallbackList(state.form.price_reduction_configs.length, 'price')"
              addonAfter="元"
            >
            </a-input-number>
          </a-form-item>
          <div class="flex-none ml-4px mr-4px mt--16px c-#85878a">的降价</div>
          <a-form-item
            class="w-170px"
            :name="'price' + index + '_cut_pricen'"
            :rules="{
              validator: (_: any, __: string, callback: Function) => {
                if (!item.reduction && item.reduction !== 0) {
                  callback('请输入降价金额')
                }
                if (item.reduction > item.min_price) {
                  callback('降价金额不能大于起始金额')
                }
                callback()
              },
              trigger: ['change', 'blur']
            }"
          >
            <a-input-number
              v-model:value="item.reduction"
              placeholder="降价金额"
              :min="0.01"
              style="width: 130px"
              :precision="2"
              addonAfter="元"
            >
            </a-input-number>
          </a-form-item>
          <MinusCircleOutlined
            class="font-size-16px c-#85878a cursor-pointer ml-4px mt--16px"
            @click="deleteItem(index)"
          />
        </div>
        <a-button type="link" class="p-0! h-auto!" @click="addItem()">添加阶梯</a-button>
      </a-form-item>

      <a-form-item label="适用范围" name="apply_product" required>
        <a-radio-group v-model:value="state.form.apply_product" @change="onChangeType" :disabled="item?.id">
          <a-radio :value="1">全部商品</a-radio>
          <a-radio :value="2">指定商品</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        label="应用商品"
        :rules="[{ required: true, message: '请选择商品' }]"
        name="product_ids"
        v-if="state.form.apply_product == 2"
      >
        <a-select
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          v-model:value="state.form.product_ids"
          :options="state.goodsList"
          mode="multiple"
          @focus="getProductLibrary"
          @search="getProductLibrary"
          @change="onChangeType"
          :filter-option="handleAdFilterOption"
          placeholder="请选择商品"
        ></a-select>
      </a-form-item>

      <a-form-item label="短信模板名称" name="sms_temp_id">
        <a-select
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          v-model:value="state.form.sms_temp_id"
          :options="state.tempList"
          @change="onChangeTemp"
          placeholder="请选择模板名称"
        ></a-select>
      </a-form-item>

      <a-form-item label="短信内容" name="sms_content">
        <a-textarea
          id="textarea"
          ref="text_content"
          class="flex-1"
          placeholder=""
          v-model:value="state.form.sms_content"
          disabled
          :rows="5"
          :maxlength="200"
        />
        <!-- <template #extra>
          <span v-if="state.showTips" class="c-#FF4D4F"
            >该模板内包含有【立减金额】，请先确认【立减金额】再选用模板</span
          >
        </template> -->
      </a-form-item>
      <a-form-item label="有效期" name="plan_time_type" class="mb-0!" required>
        <div>
          <a-radio-group v-model:value="state.form.plan_time_type">
            <a-radio :value="1">长期有效</a-radio>
            <a-radio :value="2">短期有效</a-radio>
          </a-radio-group>
        </div>
      </a-form-item>
      <a-form-item
        class="ml-102px"
        :rules="[{ required: true, message: '请选择开始和结束时间' }]"
        v-if="state.form.plan_time_type == 2"
        name="timePick"
      >
        <a-range-picker
          v-model:value="state.form.timePick"
          class="picker"
          valueFormat="YYYY-MM-DD"
          :allowClear="false"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </a-form-item>
    </a-form>

    <div class="fooler_btn">
      <div>
        <AButton :mr="30" @click="close">取消</AButton>
        <AButton type="primary" @click="submitForm" :loading="state.loading">保存</AButton>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import useData from './useData'
  import { isNumber } from 'lodash-es'
  import moment from 'moment'
  import { QuestionCircleFilled, MinusCircleOutlined } from '@ant-design/icons-vue'
  import { useApp } from '@/hooks'
  const { shopInfo } = useApp()
  import router from '@setting/router'
  const emit = defineEmits(['event'])
  const props = defineProps({
    item: {
      type: Object,
      deflaut: () => {}
    },
    dialogType: {
      type: String
    }
  })
  const validatorNumber = (value: any) => {
    if (!isNumber(value)) {
      return Promise.reject('请输入')
    }
    return Promise.resolve()
  }
  const typeOptions = [
    { label: '催付款', value: 1 },
    { label: '复购', value: 2 },
    { label: '上新', value: 3 }
  ]
  const disabledDate = (current) => {
    // Can not select days before today and today
    return current && current < moment()
  }
  const handleAdFilterOption = (input, option) => {
    const lowerInput = input.toLowerCase()
    return (
      option.title.toLowerCase().includes(lowerInput) ||
      option.code.toLowerCase().includes(lowerInput) ||
      option.title.includes(input) ||
      option.code.includes(input)
    )
  }
  const disabledDateTime = (date) => {
    if (date && date.isSame(moment(), 'day')) {
      const now = moment()
      const hours = now.hours()
      const minutes = now.minutes()
      const seconds = now.seconds()

      return {
        // 禁用当前小时及之前的小时
        disabledHours: () => {
          const disabledHours = Array.from({ length: 24 }, (_, i) => i).slice(0, hours)
          return disabledHours.concat(
            Array.from({ length: 24 }, (_, i) => i).slice(22, 24),
            Array.from({ length: 24 }, (_, i) => i).slice(0, 8)
          )
        },
        // 禁用当前小时的分钟
        disabledMinutes: (selectedHour) => {
          if (selectedHour < hours) {
            return Array.from({ length: 60 }, (_, i) => i) // 禁用当前小时之前的所有分钟
          } else if (selectedHour === hours) {
            return Array.from({ length: 60 }, (_, i) => i).slice(0, minutes) // 禁用当前小时的当前分钟及之前的分钟
          }
          return [] // 其他小时不禁用
        },
        // 禁用当前分钟的秒数
        disabledSeconds: (selectedHour, selectedMinute) => {
          if (selectedHour < hours || (selectedHour === hours && selectedMinute < minutes)) {
            return Array.from({ length: 60 }, (_, i) => i) // 禁用当前小时之前的所有秒
          } else if (selectedHour === hours && selectedMinute === minutes) {
            return Array.from({ length: 60 }, (_, i) => i).slice(0, seconds) // 禁用当前秒及之前的秒
          }
          return [] // 其他分钟不禁用
        }
      }
    }
    return {
      disabledHours: () =>
        Array.from({ length: 24 }, (_, i) => i)
          .slice(22, 24)
          .concat(Array.from({ length: 24 }, (_, i) => i).slice(0, 8)),
      disabledMinutes: () => [],
      disabledSeconds: () => []
    }
  }

  const {
    state,
    rules,
    resetFieldCallbackList,
    addItem,
    deleteItem,
    openNewPage,
    onChangeTemp,
    changeDate,
    close,
    submitForm,
    onChangeType,
    disabledRangeDate,
    disabledDateRangeTime,
    text_content,
    getProductLibrary,
    ruleForm
  } = useData(emit, props)
</script>
<style lang="scss" scoped>
  .notify-modal-wrapper {
    margin-right: -20px;
    .notify-form-wrapper {
      max-height: 500px;
      overflow: auto;
      padding-right: 20px;
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        /**/
      }
      &::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 2px;
      }
      &::-webkit-scrollbar-thumb {
        background: #c5c5c5;
        border-radius: 10px;
        display: block;
        padding-left: 30px;
      }
    }
    .form-item.no-mb {
      margin-bottom: 0;
    }
    .c-primary {
      color: var(--primary-color);
    }
    .fooler_btn {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 12px;
      margin-right: 20px;
    }

    .btn_list {
      display: flex;
      flex-wrap: wrap;

      .ant-btn {
        padding: 4px 8px;
        font-size: 12px;
        line-height: 12px;
        height: auto;
        .anticon {
          padding: 0;
        }
        &.content_btn {
          border: 1px solid #edf2ff;
          color: #1677ff;
          margin-right: 8px;
          margin-bottom: 12px;
        }
      }
    }
  }
</style>
