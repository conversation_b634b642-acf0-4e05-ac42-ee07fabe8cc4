<template>
  <div class="notify-modal-wrapper">
    <a-descriptions
      :column="1"
      class="custom-desc bg-#F5F6F8 border-rd-8px p-16px"
      :labelStyle="{ color: '#636363', minWidth: '100px' }"
      :contentStyle="{ color: '#313233' }"
    >
      <a-descriptions-item label="策略名称">{{ state.info.plan_name }}</a-descriptions-item>
      <a-descriptions-item label="描述">{{ state.info.description }}</a-descriptions-item>
      <a-descriptions-item label="策略类型">
        <div>
          {{ state.info.plan_type == 1 ? '催付款' : state.info.plan_type == 2 ? '复购' : '上新' }}
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="发送时机" v-if="[1].includes(state.info.send_resource_type)"
        ><div v-if="state.info.plan_type == 1 && state.info.send_resource_type == 1">
          下单{{ state.info.hour_time }}小时{{ state.info.minutes_time }}分钟后未支付发送短信
        </div>
        <div v-if="state.info.plan_type == 2 && state.info.send_resource_type == 1">
          商品收货后{{ state.info.after_time }}天发送短信
        </div>
        <div v-if="state.info.plan_type == 3 && state.info.send_resource_type == 1">
          购买商品后{{ state.info.after_time }}天发送短信
        </div>
      </a-descriptions-item>
      <a-descriptions-item
        label="降价区间"
        v-if="state.info.price_reduction_configs?.length && state.info.plan_type == 1"
      >
        <div class="flex-col">
          <div v-for="item in state.info.price_reduction_configs">
            {{ item.min_price }}元 - {{ item.max_price }}元，降价{{ item.reduction }}元
          </div>
        </div>
      </a-descriptions-item>

      <a-descriptions-item label="通知范围" v-if="[2, 3].includes(state.info.plan_type)">{{
        state.info.send_resource_type == 1 ? '新订单' : '历史订单'
      }}</a-descriptions-item>
      <a-descriptions-item
        label="订单范围"
        v-if="[2, 3].includes(state.info.plan_type) && state.info.send_resource_type == 2"
        >{{ state.info.resource_start_at + '-' + state.info.resource_end_at }}</a-descriptions-item
      >
      <a-descriptions-item
        label="发送时机"
        v-if="[2, 3].includes(state.info.plan_type) && state.info.send_resource_type == 2"
        >{{ state.info.after_time || '-' }}</a-descriptions-item
      >
      <a-descriptions-item label="适用范围">{{
        state.info.apply_product == 1 ? '全部商品' : '指定商品'
      }}</a-descriptions-item>
      <a-descriptions-item label="短信模板名称">
        {{ state.info.sms_temp_name }} <span class="tag_active" v-if="item?.sms_temp_is_del">模板已删除</span>
      </a-descriptions-item>
      <a-descriptions-item label="短信内容">{{ state.info.sms_content }}</a-descriptions-item>
      <a-descriptions-item label="有效期" class="pb-0!">
        <div v-if="state.info.plan_time_type == 1">长期有效</div>
        <div v-else>{{ state.info.plan_start_time }}-{{ state.info.plan_end_time }}</div>
        {{
      }}</a-descriptions-item>
    </a-descriptions>

    <a-descriptions
      :column="2"
      class="custom-desc bg-#F5F6F8 border-rd-8px p-16px mt-24px"
      :labelStyle="{ color: '#636363' }"
      :contentStyle="{ color: '#313233' }"
    >
      <a-descriptions-item label="所属公司">{{ state.info.company_name }}</a-descriptions-item>
      <a-descriptions-item label="所属店铺">{{ state.info.shop_name }}</a-descriptions-item>
      <a-descriptions-item label="创建人" class="pb-0!">{{ state.info.admin_name }}</a-descriptions-item>
      <a-descriptions-item label="创建时间" class="pb-0!">{{ state.info.created_at }}</a-descriptions-item>
    </a-descriptions>
  </div>
</template>
<script setup lang="ts">
  import { reactive } from 'vue'
  import { get_plan_info } from '../index.api'
  import moment from 'moment'
  const props = defineProps({
    item: {
      type: Object,
      deflaut: () => {}
    }
  })
  const state = reactive({
    info: {}
  })
  const getInfo = async () => {
    const { data } = await get_plan_info(props.item.id)
    state.info = data
    state.info.hour_time = data.after_time[1]
    state.info.minutes_time = data.after_time[2]
    state.info.after_time =
      data.send_resource_type == 2 && [2, 3].includes(data.plan_type)
        ? moment.unix(data.after_time[0]).format('YYYY-MM-DD HH:mm:ss')
        : data.after_time[0]
    console.log(data, 'state.info.minutes_time ')
  }
  getInfo()
</script>
<style lang="scss" scoped>
  .tag_active {
    background: #fff5f5;
    border: 1px solid #f37878;
    color: #e63030;
    border-radius: 2px;
    line-height: 1.2;
    font-size: 12px;
    margin-left: 6px;
    padding: 2px 4px;
  }
</style>
