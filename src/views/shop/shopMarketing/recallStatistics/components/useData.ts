import { ref, reactive, watchEffect } from 'vue'
import { useRouter } from 'vue-router'
import { create_plan, update_plan, getSmsList, get_plan_info } from '../index.api'
import { setProductList } from '@/views/shop/goods/goodList/index.api'
import { get_temp_list } from '@/views/shop/shopMarketing/msgTemp/index.api'
import { get_order_list_count } from '@/views/shop/order/order_list/index.api'
import moment from 'moment'
import { debounce, isString } from 'lodash-es'
import { message } from 'ant-design-vue'
export default function useData(emit, props) {
  const router = useRouter()
  const ruleForm = ref(null)
  const text_content = ref(null)
  const validMsg = (rule, value) => {
    if (!value) return Promise.reject('请输入通知内容')
    if (!value.endsWith('拒收请回复R')) {
      return Promise.reject('短信后缀必须添加拒收请回复R')
    } else {
      console.log(value, '3333')
      return Promise.resolve()
    }
  }
  const rules = reactive({
    type: [{ required: true, trigger: ['change', 'blur'] }],
    library_id: [{ required: true, message: '请选择商品库', trigger: ['change', 'blur'] }],
    plan_name: [{ required: true, message: '请输入模板名称', trigger: ['change', 'blur'] }],
    sms_temp_id: [{ required: true, trigger: ['change', 'blur'] }]
  })

  const state = reactive<any>({
    form: {
      plan_name: undefined,
      plan_type: 1,
      apply_product: 1,
      plan_time_type: 1,
      send_resource_type: 1,
      after_time_hour: 0,
      after_time_minute: 3,
      after_time_day: 0,
      description: undefined,
      price_reduction_configs: [],
      sms_content: undefined
    },
    order_count: 0,
    sms_count: 0,
    showTips: false,
    msgOptions: [],
    tempList: [],
    libraryList: [],
    loading: false,
    btnList: [],
    eventInfo: undefined
  })

  const onChangeTemp = () => {
    const currentIndex = state.tempList.findIndex((temp) => temp.id === state.form.sms_temp_id)
    state.form.sms_content = state.tempList[currentIndex].sms_content
  }

  // 商品列表
  const getProductLibrary = debounce(async (val) => {
    try {
      let keyword = val && isString(val) ? val : undefined
      const resp = await setProductList({ page: 1, page_size: 100, on_sale: 1, name_code: keyword })
      state.goodsList = (resp.data?.list || []).map((v) => {
        return {
          ...v,
          label: v.title,
          value: v.id
        }
      })
    } catch (error) {
      console.error(error)
    }
  }, 300)
  const getTempList = async () => {
    try {
      let params = {
        sms_status: 2,
        page: 1,
        page_size: 1000
      }
      const res = await get_temp_list(params)
      state.tempList = (res.data.list || []).map((item) => {
        return {
          ...item,
          label: item.sms_temp_name,
          value: item.id
        }
      })
    } catch (error) {}
  }

  const initData = async () => {
    getProductLibrary()
    await getTempList()
    const { data: smsData } = await getSmsList()
    state.sms_count = smsData.left_num
    if (props.item) {
      const { data } = await get_plan_info(props.item.id)
      state.info = data
      const hasTempId = state.tempList.some((item) => item.value === data.sms_temp_id)
      const oldOrder = data.send_resource_type == 2 && [2, 3].includes(data.plan_type)

      state.form = {
        ...data,
        after_time: oldOrder ? moment.unix(data.after_time[0]).format('YYYY-MM-DD HH:mm:ss') : undefined,
        resource_date:
          data.resource_start_at && data.resource_end_at ? [data.resource_start_at, data.resource_end_at] : undefined,
        sms_temp_id: hasTempId ? data.sms_temp_id : undefined,
        sms_content: hasTempId ? data.sms_content : undefined,
        price_reduction_configs: data.price_reduction_configs || [],
        after_time_day: data.send_resource_type == 1 ? data.after_time[0] : undefined,
        after_time_hour: data.after_time[1],
        after_time_minute: data.after_time[2],
        product_ids: data.product_ids ? data.product_ids.split(',').map((id) => Number(id)) : [],
        timePick: data.plan_start_time && data.plan_end_time ? [data.plan_start_time, data.plan_end_time] : []
      }
      if (oldOrder) {
        changeDate([data.resource_start_at, data.resource_end_at])
      }
    }
  }
  initData()

  //保存
  const submitForm = async () => {
    try {
      await ruleForm.value.validate()
      save()
    } catch (error) {
      console.error('表单验证失败:', error)
      return // 如果验证失败，返回
    }
  }
  const save = async () => {
    try {
      if (
        state.form.plan_type == 1 &&
        state.form.sms_content.includes('{立减金额}') &&
        !state.form.price_reduction_configs.length
      ) {
        return message.warning('该模板内包含有【立减金额】，请先确认【立减金额】再选用模板')
      }

      state.form.after_time_day = state.form.plan_type != 1 ? state.form.after_time_day : 0
      state.form.after_time_hour = state.form.plan_type == 1 ? state.form.after_time_hour : 0
      state.form.after_time_minute = state.form.plan_type == 1 ? state.form.after_time_minute : 0

      let params = {
        ...state.form,
        after_time:
          state.form.send_resource_type == 1
            ? [state.form.after_time_day, state.form.after_time_hour, state.form.after_time_minute]
            : [moment(state.form.after_time).unix(), 0, 0],
        resource_start_at: state.form.resource_date ? state.form.resource_date[0] : undefined,
        resource_end_at: state.form.resource_date ? state.form.resource_date[1] : undefined,
        product_ids: state.form.product_ids ? state.form.product_ids.join(',') : undefined,
        plan_start_time: (state.form.timePick && state.form.timePick.length && state.form.timePick[0]) || '',
        plan_end_time: (state.form.timePick && state.form.timePick.length && state.form.timePick[1]) || ''
      }
      console.log(params, 'params.')

      let res
      if (props.item) {
        params.id = props.item.id
        res = await update_plan(params)
      } else {
        res = await create_plan(params)
      }
      if (res.code == 0) {
        message.success('保存成功')
        emit('event', { cmd: 'submit' })
      } else {
        message.warning(res.msg || res.data)
        return
      }
    } catch (err) {
      console.log('err', err)
    }
  }
  //取消
  const close = () => {
    emit('event', { cmd: 'close', status: true })
  }

  // 价格区间校验
  const resetFieldCallbackList = (len: number) => {
    let price_arr = []

    for (let i = 0; i < len; i++) {
      price_arr.push('price' + i + '_min_price')
      price_arr.push('price' + i + '_max_price')
    }
    ruleForm.value.validateFields(price_arr)
  }
  // 添加
  const addItem = () => {
    let list = state.form.price_reduction_configs
    if (list.length > 4) {
      return
    }

    if (list.length && list?.some((it) => !it.min_price || !it.max_price || (!it.reduction && it.reduction !== 0)))
      return message.warning('请先填写完金额区间及降价金额')
    let item = { min_price: undefined, max_price: undefined, reduction: undefined }
    list?.push(item)
  }
  // 删除
  const deleteItem = (index: number) => {
    let list = state.form.price_reduction_configs
    list?.splice(index, 1)
  }
  const onChangeType = () => {
    if (state.form.send_resource_type == 2 && [2, 3].includes(state.form.plan_type) && state.form.resource_date) {
      changeDate(state.form.resource_date)
    }
  }
  const handleSearchOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }
  const changeDate = async (values) => {
    console.log(values, 'values')
    // 上新 created_at
    // 复购 complete_time
    const date_range = `${values[0]}_${values[1]}`
    const ids =
      state.form.apply_product == 2 && state.form.product_ids?.length ? state.form.product_ids.join(',') : undefined
    const data =
      state.form.plan_type == 2
        ? await get_order_list_count({
            complete_time: date_range,
            parent_order_num: state.form.send_resource_type == 2 ? 'empty' : '',
            product_ids: ids
          })
        : await get_order_list_count({
            created_at: date_range,
            product_ids: ids,
            parent_order_num: state.form.send_resource_type == 2 ? 'empty' : ''
          })
    state.order_count = data.data.count['0'] || 0
  }
  const openNewPage = () => {
    let query = { type: 1 }
    console.log(router, 'query')

    const href = router.resolve({
      path: '/shop/shopOrder/RecallStatistics',
      query: query
    })
    window.open(href.href, '_blank')
  }

  const disabledRangeDate = (current) => {
    // 禁用今天之后的日期
    return current && current > moment().endOf('day')
  }
  const disabledDateRangeTime = (date) => {
    if (date && date.isSame(moment(), 'day')) {
      const hours = moment().hours()
      const minutes = moment().minutes()
      const seconds = moment().seconds()

      return {
        disabledHours: () => [...Array(24)].map((_, i) => i).slice(hours + 1),
        disabledMinutes: (selectedHour) =>
          selectedHour === hours ? [...Array(60)].map((_, i) => i).slice(minutes + 1) : [],
        disabledSeconds: (selectedHour, selectedMinute) =>
          selectedHour === hours && selectedMinute === minutes ? [...Array(60)].map((_, i) => i).slice(seconds + 1) : []
      }
    }
    return {}
  }
  return {
    state,
    rules,
    submitForm,
    close,
    text_content,
    ruleForm,
    onChangeTemp,
    changeDate,
    openNewPage,
    resetFieldCallbackList,
    addItem,
    onChangeType,
    disabledRangeDate,
    disabledDateRangeTime,
    deleteItem,
    getProductLibrary,
    handleSearchOption
  }
}
