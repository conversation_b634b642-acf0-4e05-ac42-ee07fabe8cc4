<template>
  <div>
    <SearchBaseLayout
      :data="state.searchConfig.data"
      @changeValue="changeValue"
      :actions="state.searchConfig.options"
    />
    <a-button class="btn mt-16px" type="primary" @click="handleActions('add')">新增策略</a-button>

    <TableZebraCrossing :data="tableData" @change="pageChange">
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.dataIndex === 'description'">
          <a-tooltip placement="top" :title="scope.record.description" trigger="hover">
            <div class="text_overflow" style="cusor: pointer">{{ scope.record.description }}</div>
          </a-tooltip>
        </template>
        <template v-if="scope.column.dataIndex === 'send_resource_type'">
          {{
            [1, 5].includes(scope.record.plan_type)
              ? '--'
              : scope.record.send_resource_type == 1
                ? '新订单'
                : '历史订单'
          }}
        </template>

        <template v-if="scope.column.dataIndex === 'plan_type'">
          {{
            scope.record.plan_type == 1
              ? '催付款'
              : scope.record.plan_type == 2
                ? '复购'
                : scope.record.plan_type === 5
                  ? '在线课程'
                  : '上新'
          }}
        </template>
        <template v-if="scope.column.dataIndex === 'apply_product'">
          <div v-if="scope.record.apply_product == 1">全部商品</div>
          <div v-else>
            <a-tooltip placement="top" :title="scope.record.product_lib_names" trigger="hover">
              <div class="text_overflow3" style="cusor: pointer">{{ scope.record.product_lib_names }}</div>
            </a-tooltip>
          </div>
        </template>
        <template v-if="scope.column.dataIndex === 'plan_time_type'">
          <div v-if="scope.record.plan_time_type == 1">长期有效</div>
          <div v-else>{{ scope.record.plan_start_time }}-{{ scope.record.plan_end_time }}</div>
        </template>
        <template v-if="scope.column.dataIndex === 'plan_name'">
          <a-tooltip placement="top" :title="scope.record.plan_name" trigger="hover">
            <div class="primary-color text_overflow cursor-pointer" @click="handleActions('detail', scope.record)">
              {{ scope.record.plan_name }}
            </div>
          </a-tooltip>
        </template>
        <template v-if="scope.column.dataIndex === 'sms_temp_name'">
          <div class="flex">
            <a-tooltip placement="top" :title="scope.record.sms_temp_name" trigger="hover">
              <div class="text_overflow" :class="[scope.record.sms_temp_is_del == 1 ? 'c-#AFAFAF' : '']">
                {{ scope.record.sms_temp_name }}
              </div>
            </a-tooltip>
            <a-tooltip>
              <template #title>模板已被删除</template>
              <ExclamationCircleOutlined v-if="scope.record.sms_temp_is_del" class="c-#e63030 ml-4px" />
            </a-tooltip>
          </div>
        </template>
        <template v-if="scope.column.dataIndex === 'send_count'">
          <a-button type="link" class="p-0! h-auto" @click="handleActions('list', scope.record)">{{
            scope.record.send_count
          }}</a-button>
        </template>
        <template v-if="scope.column.dataIndex === 'handle'">
          <a-button type="link" class="p-0! h-auto" @click="handleActions('edit', scope.record)">编辑</a-button>
          <a-popconfirm title="您确定要删除吗？" placement="top" @confirm="handleActions('delete', scope.record)">
            <a-button class="p-0! h-auto" type="link" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </TableZebraCrossing>
    <a-modal
      v-model:open="state.dialog.visible"
      :footer="null"
      :width="state.dialog.width"
      :title="state.dialog.title"
      destroyOnClose
      centered
    >
      <EditStrategy v-if="['add', 'edit'].includes(state.dialog.type)" :item="state.dialog.data" @event="onEvent" />
      <StrategyDetail v-if="state.dialog.type == 'detail'" :item="state.dialog.data" />
      <SendList v-if="['list'].includes(state.dialog.type)" type="plan" source="dialog" :item="state.dialog.data" />
    </a-modal>
  </div>
</template>

<script setup lang="tsx">
  import useData from '../src/strategy'
  import EditStrategy from './EditStrategy.vue'
  import SendList from './SendList.vue'
  import StrategyDetail from './StrategyDetail.vue'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

  const props = defineProps(['source'])
  const { state, init, pageChange, tableData, onEvent, handleActions, changeValue } = useData(props.source)

  init()
</script>

<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';
  .text {
    @include text_overflow(2);
    white-space: wrap;
  }
  .primary-color {
    color: var(--primary-color);
  }
</style>
