<template>
  <div>
    <SearchBaseLayout
      style="margin-bottom: 24px"
      :data="state.searchConfig.data"
      @changeValue="changeValue"
      :actions="state.searchConfig.options"
      :btnNames="['export']"
    />
    <div>
      当前接收异常<span class="c-#fe4d4f"> {{ state.failCount }}</span
      >条
    </div>
    <TableZebraCrossing :data="tableData" @change="pageChange">
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.dataIndex === 'product_name'">
          <div class="w240px">
            <div class="flex">
              <a-image
                :src="scope.record.product_img"
                :fallback="errorImg"
                style="width: 40px; height: 40px; border-radius: 2px"
              ></a-image>
              <div class="m-l-5px">
                <a-tooltip placement="topLeft">
                  <template #title>{{ scope.record.product_name || '--' }}</template>
                  <div class="text_overflow w-160px line-height-20px">
                    {{ scope.record.product_name || '--' }}
                  </div>
                </a-tooltip>
                <!-- <div class="cellos-item_style">
                      <a-tooltip placement="topLeft">
                        <template #title>{{ scope.record.product_sku || '--' }}</template>
                        <span class="text_overflow w-160px block">{{ scope.record.product_sku || '--' }}</span>
                      </a-tooltip>
                    </div> -->
                <div class="cellos-item_style line-height-20px">
                  <a-tooltip placement="top">
                    <template #title>{{ scope.record.product_code || '--' }}</template>
                    <div class="c-#626366 font-size-12px">商品落地页ID：{{ scope.record.product_code || '--' }}</div>
                  </a-tooltip>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-if="scope.column.dataIndex === 'order'">
          <div class="min-w-240px">订单号：{{ scope.record.order_sn }}</div>
          <div
            class="font-size-12px mt-3px"
            :style="{
              color: orderType(scope.record.order_status)?.color
            }"
          >
            {{ orderType(scope.record.order_status)?.text || '--' }}
          </div>
        </template>
        <template v-if="scope.column.dataIndex === 'plan_type'">
          <a-tooltip placement="top" :title="scope.record.sms_plan_name" trigger="hover">
            <div class="text_overflow" style="cusor: pointer">{{ scope.record.sms_plan_name }}</div>
          </a-tooltip>
          <!-- {{ scope.record.plan_type == 1 ? '催付款' : scope.record.plan_type == 2 ? '复购' : '上新' }} -->
          {{ planObj[scope.record.plan_type] }}
        </template>
        <template v-if="scope.column.dataIndex === 'template'">
          <a-tooltip placement="top" :title="scope.record.sms_temp_name" trigger="hover">
            <div class="text_overflow" style="cusor: pointer">{{ scope.record.sms_temp_name }}</div>
          </a-tooltip>
        </template>
        <template v-if="scope.column.dataIndex === 'content'">
          <a-tooltip placement="top" :title="scope.record.content" trigger="hover">
            <div class="text_overflow3">
              {{ scope.record.content }}
            </div>
          </a-tooltip>
        </template>
        <template v-if="scope.column.dataIndex === 'operate_type'">
          <div v-if="scope.record.operate_type == 3">外呼发送</div>
          <div v-else>
            <div v-if="scope.record.plan_type == 101">召回发送</div>
            <div v-else>{{ operateType[scope.record.operate_type] }}</div>
          </div>
        </template>
        <template v-if="scope.column.dataIndex === 'status'">
          <div>{{ send_status[scope.record.status] }}</div>
        </template>
        <template v-if="scope.column.dataIndex === 'receive_name'">
          <div>{{ scope.record.receive_name }}</div>

          <div v-if="scope.record.receive_phone">
            {{ scope.record.receive_phone }}
            <EyeOutlined
              class="pl-1 cursor-pointer"
              v-if="scope.record.showPhone === 1"
              @click="getPhone(scope.record, { type: 3, id: scope.record.id + '', is_show: 0 })"
            />
            <SvgIcon
              v-else
              :id="`initVerifyCopy${scope.index}`"
              class="pl-1 cursor-pointer"
              icon="eye"
              @click="getPhone(scope.record, { type: 3, id: scope.record.id + '', is_show: 1 })"
            />
          </div>
        </template>
        <template v-if="scope.column.dataIndex === 'handle'">
          <!-- v-if="scope.record.status ==3" -->
          <a-popconfirm title="您确定要重新发送吗？" placement="top" @confirm="againSend(scope.record)">
            <template v-if="[3, 5].includes(scope.record.status)">
              <a-button type="link" class="p-0">{{ scope.record.status == 3 ? '重新发送' : '再次发送' }} </a-button>
            </template>
          </a-popconfirm>
          <a-button type="link" v-if="scope.record.status == 4" class="p-0" disabled>重新发送中 </a-button>
        </template>
      </template>
    </TableZebraCrossing>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      :maskClosable="false"
      destroyOnClose
    >
      <div
        v-if="state.dialog.type == 'slidingId'"
        id="slidingId"
        style="position: relative; height: 32px; overflow: hidden"
      ></div>
    </a-modal>
  </div>
</template>

<script setup lang="tsx">
  import useData from '../src/send'
  import { EyeOutlined } from '@ant-design/icons-vue'
  import { localStg } from '@/utils'
  import { reactive, watch } from 'vue'
  import { useAuth, useVerifyCopy } from '@/hooks'
  import SliderVerification from '@/components/ui/common/FunCom/SliderVerification/index'
  import { order_get_order_phone } from '@/views/shop/order/order_list/index.api'
  const { initVerifyCopy, isNvcPass, nvc, isModal, setIsModal } = useVerifyCopy(success, phoneCode)
  const props = defineProps(['source', 'type', 'item'])
  const planObj = {
    1: '催付款',
    2: '复购',
    3: '上新',
    101: '',
    5: '在线课程'
  }
  const operateType = {
    1: '自动发送',
    2: '手动发送',
    3: '召回发送'
  }
  const send_status = {
    1: '--',
    2: '发送成功',
    3: '接收异常',
    4: '--',
    5: '接收异常'
  }
  const { state, init, pageChange, tableData, orderType, againSend, changeValue } = useData(
    props.source,
    props.type,
    props.item
  )
  init()
  const getPhone = async (row, data) => {
    if (data.is_show === 1) {
      await initVerifyCopy()
      state.yzData = { ...data, nvc: nvc.value }
      state.row = row
      const result = await order_get_order_phone(state.yzData)
      const status = isNvcPass(result, 'slidingId')
      if (status) {
        row.showPhone = data.is_show
        row.receive_phone = result.data.phone
      }
    } else {
      const result = await order_get_order_phone({ ...data })
      row.showPhone = data.is_show
      row.receive_phone = result.data.phone
    }
  }
  async function success(data) {
    try {
      const result = await order_get_order_phone({ ...state.yzData, nvc: data })
      const status = isNvcPass(result, 'slidingId')
      if (status) {
        setIsModal()
        state.tableConfig.list.forEach((item) => {
          if (item.id === state.row.id) {
            item.showPhone = 1
            item.receive_phone = result.data.phone
          }
        })
      }
    } catch (e) {
      console.log(e)
    }
  }
  function phoneCode() {
    SliderVerification({
      handleOk(data) {
        return okphone(data)
      }
    })
  }
  const okphone = async (data) => {
    const result = await order_get_order_phone({ ...state.yzData, captcha: data.code })
    if (result.code === 0) {
      tableData.dataSource.forEach((item) => {
        if (item.id === state.row.id) {
          item.showPhone = 1
          item.receive_phone = result.data.phone
          localStg.remove('countdown')
        }
      })
    } else {
      message.warning(result.msg)
      return true
    }
  }

  watch(
    () => isModal.value,
    (val) => {
      if (val) {
        state.dialog = { title: '验证', visible: true, width: 300, type: 'slidingId' }
        setTimeout(() => {
          window.nvc.getNC({
            renderTo: 'slidingId'
          })
        }, 100)
      } else {
        state.dialog.visible = false
        setIsModal()
      }
    }
  )
</script>

<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';
  .text_overflow3 {
    @include text_overflow(3);
    white-space: wrap;
  }
</style>
