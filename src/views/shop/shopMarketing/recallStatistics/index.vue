<template>
  <div>
    <CardBaseLayout>
      <template #title>
        <div>短信统计 <span class="text-13px font-400 ml-20px">短信费用0.045元/条</span></div>
      </template>
      <template #content>
        <div class="comp_data">
          <div v-for="item in state.smsCount" class="data_item">
            <div class="item_title flex justify-between">
              <span>{{ item.label }}{{ item.unit_text }}</span>
              <a-dropdown :overlayStyle="{ width: '180px' }" v-if="item.children">
                <a class="ant-dropdown-link" @click.prevent>
                  <MoreOutlined class="ml-5px" />
                </a>
                <template #overlay>
                  <a-menu v-if="item.children">
                    <a-menu-item v-for="it in item.children">
                      <div class="flex-y-center justify-between">
                        <div>
                          <a href="javascript:;">{{ it.name }}</a>
                        </div>

                        <span class="ml-5px">{{ it.count }}</span>
                      </div>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
            <div class="item_price">{{ item.num }}</div>
            <div class="pay" v-if="item.label == '余额'" @click="payHandle">{{ '去充值>' }}</div>
            <div class="pay" v-if="item.label == '可用短信模板'" @click="routerPush({ name: 'MsgTemp' })">
              {{ '去管理>' }}
            </div>
          </div>
        </div>

        <a-modal v-model:open="state.dialog.visible" :footer="null" :title="state.dialog.title" destroyOnClose centered>
          <SmsPayModal @close="close" />
        </a-modal>
      </template>
    </CardBaseLayout>
    <a-card class="mt-16px">
      <a-tabs v-model:activeKey="state.activeKey" :destroyInactiveTabPane="true" @change="tabpanChange">
        <a-tab-pane :key="0" tab="发送明细">
          <SendList />
        </a-tab-pane>
        <a-tab-pane :key="1" tab="营销统计">
          <RecallList />
        </a-tab-pane>
        <a-tab-pane :key="2" tab="对账明细">
          <PaymentRecords />
        </a-tab-pane>
        <a-tab-pane :key="4" tab="发送策略">
          <StrategyList />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>
<script setup lang="tsx">
  defineOptions({ name: 'RecallStatistics' })
  import { onMounted, reactive, watch } from 'vue'
  import { useRoute } from 'vue-router'
  // const router = useRouter()
  import { useRouter } from '@/hooks'
  const { routerPush } = useRouter()
  import { QuestionCircleOutlined, MoreOutlined } from '@ant-design/icons-vue'
  import SendList from './components/SendList.vue'
  import StrategyList from './components/StrategyList.vue'

  import RecallList from './components/RecallList.vue'
  import PaymentRecords from './components/PaymentRecords.vue'
  import SmsPayModal from './components/SmsPayModal.vue'
  import { getSmsList } from './index.api'
  const route = useRoute()
  const state = reactive({
    activeKey: 0,
    smsCount: [
      {
        label: '余额',
        unit_text: '(条)',
        num: 0
      },
      {
        label: '发送数量',
        unit_text: '(条)',
        num: 0
      },
      {
        label: '召回成功数量',
        unit_text: '(笔)',
        num: 0
      },
      {
        label: '召回成功金额',
        unit_text: '(元)',
        num: 0
      },
      {
        label: '可用短信模板',
        unit_text: '',
        num: 0
      }
    ],
    dialog: {
      width: 500,
      title: '充值',
      visible: false
    }
  })
  const getCount = async () => {
    try {
      let { data } = await getSmsList()
      state.smsCount = [
        {
          label: '余额',
          unit_text: '(条)',
          num: data.left_num
        },
        {
          label: '发送数量',
          unit_text: '(条)',
          children: [
            {
              name: '催付款',
              count: data.recall_send_num
            },
            {
              name: '复购',
              count: data.repeat_send_num
            },
            {
              name: '上新',
              count: data.new_product_send_num
            }
          ],
          num: data?.total_num
        },
        {
          label: '召回成功数量',
          unit_text: '(笔)',
          children: [
            {
              name: '催付款',
              count: data.recall_num
            },
            {
              name: '复购',
              count: data.repeat_order_num
            },
            {
              name: '上新',
              count: data.new_product_order_num
            }
          ],
          num: data.new_product_order_num + data.repeat_order_num + data.recall_num
        },
        {
          label: '召回成功金额',
          unit_text: '(元)',
          children: [
            {
              name: '催付款',
              count: data.recall_money
            },
            {
              name: '复购',
              count: data.repeat_order_money
            },
            {
              name: '上新',
              count: data.new_product_order_money
            }
          ],
          num: (data.recall_money + data.repeat_order_money + data.new_product_order_money).toFixed(2)
        },
        {
          label: '可用短信模板',
          unit_text: '',
          num: data.temp_num
        }
      ]
    } catch (err) {
      console.log('err', err)
    }
  }
  const tabpanChange = (e: number) => {
    state.activeKey = e
  }
  const payHandle = () => {
    state.dialog.visible = true
  }
  const close = () => {
    state.dialog.visible = false
    getCount()
  }
  watch(
    () => route.query.isRecharge,
    (newval: any) => {
      if (newval) {
        state.dialog.visible = JSON.parse(newval)
      }
    },
    {
      immediate: true
    }
  )
  onMounted(() => {
    getCount()
    if (route.query?.type) {
      payHandle()
    }
  })
</script>
<style scoped lang="scss">
  @import './src/assets/css/mixin_scss_fn.scss';
  .comp_data {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    .data_item {
      background: #f5f7f9;
      border-radius: 4px;
      padding: 10px 10px 10px 10px;
      box-sizing: border-box;
      flex-shrink: 0;
      position: relative;
      //   cursor: pointer;

      .item_title {
        font-size: 14px;
        color: #636e95;
        line-height: 20px;
        margin-bottom: 7px;
      }

      .item_price {
        font-size: 20px;
        font-weight: 600;
        color: #242f57;
        line-height: 28px;
        margin-bottom: 10px;
      }
      .pay {
        color: #1677ff;
        text-align: right;
        cursor: pointer;
        position: absolute;
        right: 10px;
        bottom: 10px;
      }
      .item_num {
        font-size: 16px;
        color: #8a8a8a;
        display: flex;
        align-items: center;

        .num {
          color: #e63030;
          display: flex;
          align-items: center;
          line-height: 1;
          &.down {
            color: #539a28;
          }
        }
      }
    }
  }
  :deep(.ant-tabs .ant-tabs-tab) {
    padding-top: 0;
  }
</style>
