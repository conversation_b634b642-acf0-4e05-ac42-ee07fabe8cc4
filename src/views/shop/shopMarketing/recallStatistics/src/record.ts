import { reactive } from 'vue'
import { getPaymentList } from '../index.api'
import { exportCreate } from '@/api/common'
import { useDownloadCenter } from '@/hooks'

const searchList = [
  {
    type: 'select',
    field: 'type',
    value: undefined,
    props: {
      placeholder: '请选择类型',
      showArrow: true,
      options: [
        {
          value: 1,
          label: '充值'
        },
        {
          value: 2,
          label: '退款'
        }
      ]
    },
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 4
    }
  },
  {
    type: 'select',
    field: 'status',
    value: undefined,
    props: {
      placeholder: '请选择状态',
      showArrow: true,
      options: [
        {
          label: '待支付',
          value: 1
        },
        {
          label: '成功',
          value: 2
        }
      ]
    },
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 4
    }
  },
  {
    type: 'date',
    field: 'created_at',
    value: undefined,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 4
    }
  }
]

const columns = [
  {
    dataIndex: 'money',
    key: 'money',
    title: '金额（元）',
    width: 160
  },
  {
    dataIndex: 'num',
    key: 'num',
    title: '短信数量（条）',
    width: 160
  },
  {
    dataIndex: 'type',
    key: 'type',
    title: '类型',
    width: 120
  },
  {
    dataIndex: 'status',
    key: 'status',
    title: '状态',
    width: 120
  },
  {
    dataIndex: 'pay_time',
    key: 'pay_time',
    title: '时间',
    width: 160
  }
]

export default function useData() {
  const { goCenter } = useDownloadCenter()
  const state = reactive({
    searchConfig: {
      data: searchList,
      options: {
        foldNum: 0,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      }
    },
    dialog: {
      visible: false,
      title: '',
      width: 600,
      type: '',
      data: {}
    },
    params: {
      page: 1,
      page_size: 10,
      pay_time: ''
    },
    activeKey: 1,
    status: <any>{
      1: '待支付',
      2: '成功',
      3: '成功',
      4: '失败',
      5: '退款中'
    },
    type: <any>{
      1: '充值',
      2: '退款'
    }
  })
  const tableData = reactive({
    bordered: true,
    scroll: {
      scrollToFirstRowOnChange: true
    },
    dataSource: [],
    loading: false,
    columns,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total: number) => `共${total}条数据`
    }
  })
  const getList = async () => {
    try {
      let res = await getPaymentList(state.params)
      tableData.dataSource = res.data.list || []
      tableData.pagination.total = res.data.total || 0
      tableData.pagination.current = res.data.page || 1
    } catch (err) {
      console.log('err', err)
    }
  }
  const pageChange = (pagination: { current: number; pageSize: number }) => {
    state.params.page = pagination.current
    state.params.page_size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }
  const changeValue = (v: any) => {
    console.log('v', v)
    const [start_time, end_time] = v.formData.created_at || []
    const pay_time = start_time && end_time ? `${start_time} 00:00:00_${end_time} 23:59:59` : null
    state.params = {
      ...state.params,
      ...v.formData,
      pay_time: pay_time
    }
    delete state.params.created_at
    if (v.type === 'export') {
      handleExportCreate()
      return false
    }

    state.params.page = 1
    console.log('state.params', state.params)
    getList()
  }
  //导出
  const handleExportCreate = async () => {
    try {
      let params = {
        ...state.params
      }
      await exportCreate({
        type: 'order_pay_list_export',
        params: JSON.stringify(params)
      })
      goCenter('DownloadCenter', 'DownloadCenter')
    } catch (error) {
      console.log(error)
    }
  }

  const init = () => {
    getList()
  }
  return {
    state,
    tableData,
    pageChange,
    init,
    changeValue
  }
}
