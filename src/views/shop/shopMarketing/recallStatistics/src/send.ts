import { get_send_list, resend_sms, fail_count } from '../index.api'
import { reactive } from 'vue'
// import { useRoute } from 'vue-router'
import { exportCreate } from '@/api/common'

import { useDownloadCenter } from '@/hooks'

// const route = useRoute()
// console.log(route, 'routerouteroute1111')

const columns = [
  {
    dataIndex: 'product_name',
    key: 'product_name',
    title: '商品信息',
    fixed: 'left',
    width: 245
  },
  {
    dataIndex: 'order',
    key: 'order',
    title: '订单信息',
    width: 250
  },
  {
    dataIndex: 'operate_type',
    key: 'operate_type',
    title: '发送类型',
    width: 100
  },

  {
    dataIndex: 'plan_type',
    key: 'plan_type',
    title: '策略',
    width: 160
  },
  {
    dataIndex: 'template',
    key: 'template',
    title: '模板名称',
    width: 160
  },
  {
    dataIndex: 'content',
    key: 'content',
    title: '发送内容',
    width: 240
  },
  {
    dataIndex: 'sms_count',
    key: 'sms_count',
    title: '计费条数',
    width: 100
  },
  {
    dataIndex: 'admin_name',
    key: 'admin_name',
    title: '发送人',
    width: 120
  },
  {
    dataIndex: 'created_at',
    key: 'created_at',
    title: '发送时间',
    width: 180
  },
  {
    dataIndex: 'status',
    key: 'status',
    title: '发送状态',
    width: 100
  },
  {
    dataIndex: 'receive_name',
    key: 'receive_name',
    title: '接收人信息',
    width: 140
  },
  {
    dataIndex: 'handle',
    key: 'handle',
    title: '操作',
    width: 100,
    fixed: 'right'
  }
]

export default function useData(source, type, searchItem) {
  const { goCenter } = useDownloadCenter()
  const searchList = [
    {
      type: 'input.text',
      field: 'order_sn',
      value: null,
      label: '名称',
      props: {
        placeholder: '请输入订单编号'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    },
    {
      type: 'input.text',
      field: 'product_select',
      value: undefined,
      props: {
        placeholder: '请输入商品名称/ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    },
    {
      type: 'select',
      field: 'operate_type',
      value: undefined,
      props: {
        placeholder: '请选择发送类型',
        mode: 'multiple',
        showArrow: true,
        options: [
          {
            value: 1,
            label: '自动发送'
          },
          {
            value: 2,
            label: '手动发送'
          },
          // {
          //   value: 3,
          //   label: '外呼发送'
          // },
          {
            value: -1,
            label: '召回发送'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    },
    {
      type: 'select',
      field: 'status',
      value: undefined,
      props: {
        placeholder: '请选择发送状态',
        showArrow: true,
        options: [
          {
            value: 3,
            label: '接收异常'
          },
          {
            value: 2,
            label: '发送成功'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    },
    {
      type: 'input.text',
      field: 'sms_plan_name',
      value: undefined,
      props: {
        placeholder: '请输入策略名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    },
    {
      type: 'select',
      field: 'plan_type',
      value: undefined,
      props: {
        placeholder: '请选择策略类型',
        mode: 'multiple',
        showArrow: true,
        options: [
          {
            value: 1,
            label: '催付款'
          },
          {
            value: 2,
            label: '复购'
          },
          {
            value: 3,
            label: '上新'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    },

    {
      type: 'input.text',
      field: 'sms_temp_name',
      value: undefined,
      props: {
        placeholder: '请输入模板名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    },
    {
      type: 'input.text',
      field: 'receiver',
      value: undefined,
      props: {
        placeholder: '请输入接收人姓名/手机号'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    },
    {
      type: 'date',
      field: 'created_at',
      value: undefined,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    }
  ]
  const state = reactive({
    searchConfig: {
      data: searchList,
      options: {
        foldNum: 0,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      }
    },
    failCount: 0,
    dialog: {
      visible: false,
      title: '',
      width: 600,
      type: '',
      data: {}
    },
    params: {
      sms_temp_id: type == 'sms' ? searchItem?.id : undefined,
      sms_plan_id: type == 'plan' ? searchItem?.id : undefined,
      page: 1,
      page_size: 10
    },
    activeKey: 1
  })
  const tableData = reactive({
    bordered: true,
    scroll: {
      scrollToFirstRowOnChange: true,
      y: source == 'dialog' ? 360 : null,
      x: '1600'
    },
    dataSource: [],
    loading: false,
    columns,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total: number) => `共${total}条数据`
    }
  })
  const getList = async () => {
    try {
      let res = await get_send_list(state.params)
      tableData.dataSource = res.data.list || []
      tableData.pagination.total = res.data.total || 0
      tableData.pagination.current = res.data.page || 1
      if (type == 'plan') {
        if (tableData.dataSource.length >= 4) {
          tableData.scroll.y = 370
        } else {
          delete tableData.scroll.y
        }
      }
    } catch (err) {
      console.log('err', err)
    }
  }
  const pageChange = (pagination: { current: number; pageSize: number }) => {
    state.params.page = pagination.current
    state.params.page_size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }
  const changeValue = (v: any) => {
    console.log('v', v)
    state.params = {
      ...state.params,
      ...v.formData,
      plan_type: v.formData.plan_type ? v.formData.plan_type.join(',') : '',
      operate_type: v.formData.operate_type ? v.formData.operate_type.join(',') : '',
      start_time: v.formData.created_at ? v.formData.created_at[0] : '',
      end_time: v.formData.created_at ? v.formData.created_at[1] : ''
    }

    if (v.type === 'export') {
      handleExportCreate()
      return false
    }

    state.params.page = 1
    console.log('state.params', state.params)
    getList()
    getFailCount()
  }

  //导出
  const handleExportCreate = async () => {
    try {
      let params = {
        ...state.params
      }
      await exportCreate({
        type: 'sms_send_log',
        params: JSON.stringify(params)
      })
      goCenter('DownloadCenter', 'DownloadCenter')
    } catch (error) {
      console.log(error)
    }
  }
  const getFailCount = async () => {
    try {
      let res = await fail_count(state.params)
      state.failCount = res.data.total || 0
    } catch (error) {}
  }
  const againSend = async (row) => {
    await resend_sms({ id: row.id })
    getList()
  }
  const orderType = (type) => {
    let status = {
      1: {
        color: '#e77316',
        text: '待付款'
      },
      2: {
        color: '#FE4042',
        text: '待发货'
      },
      3: {
        color: '#118BCE',
        text: '待收货'
      },
      4: {
        color: '#626366',
        text: '已完成'
      },
      '-1': {
        color: '#404040',
        text: '已取消'
      },
      6: {
        color: '#e63030',
        text: '售后中'
      }
    }
    return status[type]
  }
  const init = () => {
    getList()
    getFailCount()
  }
  // 获取手机号

  return {
    state,
    tableData,
    pageChange,
    init,
    againSend,
    orderType,
    changeValue
  }
}
