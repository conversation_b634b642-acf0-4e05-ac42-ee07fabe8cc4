import { reactive } from 'vue'
import { get_plan_list, delete_plan } from '../index.api'
import { get_temp_list } from '@/views/shop/shopMarketing/msgTemp/index.api'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
const route = useRoute()

const columns = [
  {
    dataIndex: 'plan_name',
    key: 'plan_name',
    title: '策略名称',
    width: 200
  },
  {
    dataIndex: 'description',
    key: 'description',
    title: '描述',
    width: 220
  },
  {
    dataIndex: 'plan_type',
    key: 'plan_type',
    title: '策略',
    width: 80
  },
  {
    dataIndex: 'send_resource_type',
    key: 'send_resource_type',
    title: '通知范围',
    width: 100
  },

  {
    dataIndex: 'apply_product',
    key: 'apply_product',
    title: '适用范围',
    width: 180
  },
  {
    dataIndex: 'sms_temp_name',
    key: 'sms_temp_name',
    title: '短信模板名称',
    width: 160
  },
  {
    dataIndex: 'plan_time_type',
    key: 'plan_time_type',
    title: '有效期',
    width: 220
  },
  {
    dataIndex: 'admin_name',
    key: 'admin_name',
    title: '创建人',
    width: 120
  },
  {
    dataIndex: 'created_at',
    key: 'created_at',
    title: '创建时间',
    width: 180
  },
  {
    dataIndex: 'send_count',
    key: 'send_count',
    title: '发送数量',
    width: 100
  },
  {
    dataIndex: 'handle',
    key: 'handle',
    title: '操作',
    fixed: 'right',
    width: 120
  }
]

export default function useData(source) {
  const searchList = [
    {
      type: 'input.text',
      field: 'plan_name',
      value: null,
      label: '名称',
      props: {
        placeholder: '请输入策略名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    },
    {
      type: 'select',
      field: 'plan_type',
      value: null,
      props: {
        placeholder: '请选择策略',
        options: [
          {
            value: 1,
            label: '催付款'
          },
          {
            value: 2,
            label: '复购'
          },
          {
            value: 3,
            label: '上新'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    },

    {
      type: 'select',
      field: 'sms_temp_id',
      value: null,
      props: {
        placeholder: '请选择模板',
        options: []
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    },
    {
      type: 'admin',
      field: 'admin_id',
      value: null,
      props: {
        placeholder: '请选择创建人'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    },
    {
      type: 'date',
      field: 'created_at',
      value: undefined,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: source == 'dialog' ? 6 : 4
      }
    }
  ]
  const state = reactive({
    searchConfig: {
      data: searchList,
      options: {
        foldNum: 0,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    },
    dialog: {
      visible: false,
      title: '',
      width: 600,
      type: '',
      data: {}
    },
    params: {
      page: 1,
      page_size: 10
    },
    activeKey: 1
  })
  const tableData = reactive({
    bordered: true,
    scroll: {
      scrollToFirstRowOnChange: true,
      x: '1600'
    },
    dataSource: [],
    loading: false,
    columns,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total: number) => `共${total}条数据`
    }
  })
  const getList = async () => {
    try {
      let res = await get_plan_list(state.params)
      tableData.dataSource = res.data.list || []
      tableData.pagination.total = res.data.total || 0
      tableData.pagination.current = res.data.page || 1
    } catch (err) {
      console.log('err', err)
    }
  }
  const handleActions = (type, data) => {
    switch (type) {
      case 'add':
      case 'edit':
        state.dialog = {
          visible: true,
          title: type === 'edit' ? '编辑发送策略' : '新增发送策略',
          width: 680,
          type,
          data
        }
        break
      case 'delete':
        onDelete(data)
        break
      case 'list':
        state.dialog = {
          visible: true,
          title: '发送明细',
          width: 1100,
          type,
          data
        }
        break
      case 'detail':
        state.dialog = {
          visible: true,
          title: '策略详情',
          width: 600,
          type,
          data
        }
        break
    }
  }
  const pageChange = (pagination: { current: number; pageSize: number }) => {
    state.params.page = pagination.current
    state.params.page_size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }

  const onDelete = async (row) => {
    try {
      await delete_plan(row.id)
      getList()
      message.success('删除成功')
    } catch (error) {
      console.error(error)
    }
  }
  const changeValue = (v: any) => {
    console.log('v', v)
    state.params = {
      ...state.params,
      ...v.formData,
      start_time: v.formData.created_at ? v.formData.created_at[0] : '',
      end_time: v.formData.created_at ? v.formData.created_at[1] : ''
    }

    state.params.page = 1
    console.log('state.params', state.params)
    getList()
  }
  async function getTempList() {
    try {
      let { data } = await get_temp_list({ page: 1, page_size: 999 })
      state.searchConfig.data.forEach((v) => {
        if (v.field == 'sms_temp_id') {
          ;(data.list || []).forEach((item) => {
            v.props.options.push({
              value: item.id,
              label: item.sms_temp_name
            })
          })
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  const init = () => {
    getList()
    getTempList()
  }
  const onEvent = (values) => {
    switch (values.cmd) {
      case 'close':
        state.dialog.visible = false
        break
      case 'edit':
      case 'submit':
        state.dialog.visible = false
        getList()
        break
    }
  }
  return {
    state,
    tableData,
    pageChange,
    onEvent,
    init,
    handleActions,
    changeValue
  }
}
