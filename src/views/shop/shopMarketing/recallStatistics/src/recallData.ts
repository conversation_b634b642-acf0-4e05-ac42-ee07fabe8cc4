import { reactive } from 'vue'
import { getRecallList } from '../index.api'
import { exportCreate } from '@/api/common'

import { useDownloadCenter } from '@/hooks'

const searchList = [
  {
    type: 'input.text',
    field: 'product_code',
    value: null,
    label: '名称',
    props: {
      placeholder: '请输入商品名称/ID'
    },
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 4
    }
  },
  {
    type: 'date',
    field: 'created_at',
    value: undefined,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 4
    }
  }
]

const columns = [
  {
    dataIndex: 'product_name',
    key: 'product_name',
    title: '商品信息',
    fixed: 'left',
    width: 260
  },
  {
    dataIndex: 'send_num',
    key: 'send_num',
    title: '催付款发送数量',
    tips: '手动降价+催付款策略降价发送短信数量之和',
    width: 160
  },
  {
    dataIndex: 'recall_num',
    key: 'recall_num',
    title: '催付款召回数量',
    tips: '手动降价+催付款策略降价发送短信后支付成功的订单数量',
    width: 160
  },
  {
    dataIndex: 'recall_money',
    key: 'recall_money',
    title: '催付款召回金额',
    tips: '召回订单实付金额之和',
    width: 160
  },
  {
    dataIndex: 'repeat_send_num',
    key: 'repeat_send_num',
    title: '复购发送数量',
    tips: '复购策略发送短信数量',
    width: 160
  },
  {
    dataIndex: 'repeat_order_num',
    key: 'repeat_order_num',
    title: '复购订单数量',
    tips: '通过复购短信，支付成功的订单数量',
    width: 160
  },
  {
    dataIndex: 'repeat_order_money',
    key: 'repeat_order_money',
    title: '复购订单金额',
    tips: '复购订单实付金额之和',
    width: 160
  },
  {
    dataIndex: 'new_product_send_num',
    key: 'new_product_send_num',
    title: '上新发送数量',
    tips: '上新策略发送短信数量',
    width: 160
  },
  {
    dataIndex: 'new_product_order_num',
    key: 'new_product_order_num',
    title: '上新订单数量',
    tips: '通过上新短信，支付成功的订单数量',
    width: 160
  },
  {
    dataIndex: 'new_product_order_money',
    key: 'new_product_order_money',
    title: '上新订单金额',
    tips: '上新订单实付金额之和',
    width: 160
  }
  // {
  //   dataIndex: 'handle',
  //   key: 'handle',
  //   fixed: 'right',
  //   title: '操作',
  //   width: 160
  // }
]

export default function useData() {
  const { goCenter } = useDownloadCenter()
  const state = reactive({
    searchConfig: {
      data: searchList,
      options: {
        foldNum: 0,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      }
    },
    dialog: {
      visible: false,
      title: '',
      width: 600,
      type: '',
      data: {}
    },
    params: {
      page: 1,
      size: 10
    },
    activeKey: 1
  })
  const tableData = reactive({
    bordered: true,
    scroll: {
      scrollToFirstRowOnChange: true,
      x: 1600
    },
    dataSource: [],
    loading: false,
    columns,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total: number) => `共${total}条数据`
    }
  })

  const handleActions = (type, data) => {
    state.dialog = {
      visible: true,
      title: type === 'edit' ? '编辑' : '新增',
      width: 600,
      type,
      data
    }
  }
  const getList = async () => {
    try {
      let res = await getRecallList(state.params)
      tableData.dataSource = res.data.list || []

      tableData.pagination.total = res.data.total || 0
      tableData.pagination.current = res.data.page || 1
    } catch (err) {
      console.log('err', err)
    }
  }
  const pageChange = (pagination: { current: number; pageSize: number }) => {
    state.params.page = pagination.current
    state.params.size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }
  const changeValue = (v: any) => {
    console.log('v', v)
    state.params = {
      ...state.params,
      ...v.formData,
      created_at: v.formData.created_at ? v.formData.created_at.join('_') : null
    }
    if (v.type === 'export') {
      handleExportCreate()
      return false
    }

    state.params.page = 1
    console.log('state.params', state.params)
    getList()
  }
  //导出
  const handleExportCreate = async () => {
    try {
      let params = {
        ...state.params
      }
      await exportCreate({
        type: 'recall_sms_stat',
        params: JSON.stringify(params)
      })
      goCenter('DownloadCenter', 'DownloadCenter')
    } catch (error) {
      console.log(error)
    }
  }

  const init = () => {
    getList()
  }
  return {
    state,
    tableData,
    pageChange,
    init,
    handleActions,
    changeValue
  }
}
