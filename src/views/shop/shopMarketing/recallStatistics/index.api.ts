import http from '@/utils/request.js'

/**
 * 召回统计
 */
export const getSmsList = (data?: any) => {
  return http('get', `/public/sms/sms-count`, data)
}

/**
 * 召回记录
 */
export const getRecallList = (data?: any) => {
  return http('get', `/public/sms/sms-recall-list`, data)
}

/**
 * 购买记录
 */
export const getPaymentList = (data?: any) => {
  return http('get', `/public/sms-pay-list/list`, data)
}

/**
 * 短信支付
 */
export const smsPay = (data?: any) => {
  return http('post', `/public/sms-pay-list/pay`, data)
}

/**
 * 订单是否支付
 */
export const isPay = (data?: any) => {
  return http('get', `/public/sms-pay-list/is-pay`, data)
}

/**
 * 新建策略
 */
export const create_plan = (data?: any) => {
  return http('post', `/merchant/shop-sms-plan-send/create`, data)
}
/**
 * 更新策略
 */
export const update_plan = (data?: any) => {
  return http('post', `/merchant/shop-sms-plan-send/update`, data)
}

/**
 * 策略列表
 */
export const get_plan_list = (data?: any) => {
  return http('post', `/merchant/shop-sms-plan-send/list`, data)
}
/**
 * 删除策略
 */
export const delete_plan = (data?: any) => {
  return http('post', `/merchant/shop-sms-plan-send/delete/${data}`, {})
}

/**
 * 策略详情
 */
export const get_plan_info = (data?: any) => {
  return http('get', `/merchant/shop-sms-plan-send/info/${data}`, {})
}
/**
 * 策略列表
 */
export const get_send_list = (data?: any) => {
  return http('post', `/merchant/shop-sms-send-log/list`, data)
}
/**
 * 重新发送
 */
export const resend_sms = (data?: any) => {
  return http('post', `/merchant/shop-sms-send-log/resend`, data)
}

/**
 * 失败数量
 */
export const fail_count = (data?: any) => {
  return http('post', `/merchant/shop-sms-send-log/fail-count`, data)
}
