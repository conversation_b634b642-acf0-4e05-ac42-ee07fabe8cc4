<template>
  <div>
    <TableZebraCrossing :data="state.tableConfigOptions" class="mb-24px" @change="pageChange">
      <template #bodyCell="{ scope: { record, column } }">
        <template v-if="column.key === 'remark'">
          <a-tooltip placement="topLeft">
            <template #title>
              {{ record.remark }}
            </template>
            <span class="text_overflow_row1">
              {{ record.remark }}
            </span>
          </a-tooltip>
        </template>
      </template>
    </TableZebraCrossing>
  </div>
</template>
<script setup lang="ts">
  import { reactive } from 'vue'
  import { get_video_log } from '../index.api'
  const props = defineProps(['item'])
  const state = reactive({
    params: {
      page: 1,
      page_size: 10
    },
    tableConfigOptions: {
      bordered: true,
      loading: false,
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 'max-content'
      },
      dataSource: [],
      columns: [
        {
          dataIndex: 'created_at',
          key: 'created_at',
          title: '操作时间',
          width: 230
        },
        {
          dataIndex: 'author_name',
          key: 'author_name',
          title: '操作人',
          width: 160
        },
        {
          dataIndex: 'remark',
          key: 'remark',
          title: '操作内容',
          width: 260
        }
      ],
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 10,
        size: 'small',
        showTotal: (total: String | Number) => `共${total}条数据`
      }
    }
  })
  const pageChange = (pagination: any) => {
    state.params.page = pagination.current
    state.params.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }
  const getList = async () => {
    try {
      let res = await get_video_log({ id: props.item.id })
      state.tableConfigOptions.dataSource = res.data.list || []
      state.tableConfigOptions.pagination.total = res.data.total || 0
      state.tableConfigOptions.pagination.current = state.params.page || 0
    } catch (err) {
      console.log(err)
    }
  }
  getList()
</script>
