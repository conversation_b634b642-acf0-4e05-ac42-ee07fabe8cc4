import moment from 'moment'
export default function datas() {
  // 搜索组件 配置数据
  const SEARCH_CONFIG_DATA = {
    data: [
      {
        type: 'input.text',
        field: 'sms_temp_name',
        value: undefined,
        props: {
          placeholder: '请输入模板名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },

      {
        type: 'admin',
        field: 'admin_id',
        value: undefined,
        props: {
          placeholder: '请选择创建人'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      },

      {
        type: 'date',
        field: 'created_at',
        value: [],
        props: {
          placeholder: ['开始时间', '结束时间']
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4
        }
      }
    ],
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  }
  // 按钮单选操作数据
  const BUTTON_RADIO_GROUP = {
    value: -1,
    list: [
      {
        value: -1,
        label: '全部',
        total: 0
      },
      {
        value: 1,
        label: '待审核',
        total: 0
      },
      {
        value: 2,
        label: '审核通过',
        total: 0
      },
      {
        value: 3,
        label: '审核不通过',
        warn: true,
        total: 0
      }
    ]
  }

  const statusType = (type) => {
    let status = {
      1: {
        text: '待审核',
        color: '#E77316'
      },
      2: {
        text: '审核通过',
        color: '#313233'
      },
      3: {
        text: '审核不通过',
        color: '#E63030'
      }
    }
    return status[type]
  }

  // 表格
  const columns = [
    {
      title: '模板名称',
      key: 'sms_temp_name',
      dataIndex: 'sms_temp_name',
      fixed: 'left',
      width: 180
    },
    {
      title: '描述',
      dataIndex: 'sms_description',
      key: 'sms_description',
      width: 200
    },
    {
      title: '短信内容',
      key: 'sms_content',
      dataIndex: 'sms_content',
      width: 300
    },
    {
      title: '创建人',
      key: 'admin_name',
      dataIndex: 'admin_name',
      width: 120
    },
    {
      title: '创建时间',
      key: 'created_at',
      dataIndex: 'created_at',
      width: 180
    },

    {
      title: '审核状态',
      key: 'statuss',
      dataIndex: 'statuss',
      width: 160
    },
    {
      title: '累计发送数量',
      key: 'num',
      dataIndex: 'num',
      width: 120
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      width: 90
    }
  ]

  const modalConfigTitle = (type) => {
    let modalConfig = {
      edit: {
        title: '编辑短信模板',
        width: 590
      },
      add: {
        title: '新增短信模板',
        width: 590
      },
      list: {
        title: '发送明细',
        width: 1100
      },
      audit: {
        title: '短信模板审核',
        width: 800
      }
    }
    return modalConfig[type]
  }

  return {
    SEARCH_CONFIG_DATA,
    BUTTON_RADIO_GROUP,
    statusType,
    columns,
    modalConfigTitle
  }
}
