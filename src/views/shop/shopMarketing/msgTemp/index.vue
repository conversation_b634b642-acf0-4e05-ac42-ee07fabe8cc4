<template>
  <div>
    <div class="model_block">
      <a-breadcrumb separator="/">
        <a-breadcrumb-item>
          <router-link :to="{ path: '/mall/shop_marketing/RecallStatistics' }">短信统计</router-link>
        </a-breadcrumb-item>
        <a-breadcrumb-item>短信模板管理</a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    <CardBaseLayout>
      <!-- <template #title>
        <div class="justify-between flex-y-center mb-16px">
          
        </div>
      </template> -->

      <template #content>
        <a-button class="btn" type="primary" @click="handleActions('add')">新增</a-button>
        <SearchBaseLayout
          class="mt-18px"
          :data="state.searchConfig.data"
          ref="searchFormDataRef"
          @changeValue="changeValue"
          :actions="state.searchConfig.options"
        />

        <ButtonRadioGroup :data="state.actionConfig" @changeValue="changeBtnType" />
        <!-- <DescTableLayout :data="state.tableConfig"> -->

        <!-- :row-selection="{
            onSelect: onSelect,
            onSelectAll: onSelectAll,
            selectedRowKeys: state.selectedRowKeys,
            onChange: onSelectChange
          }" -->
        <TableZebraCrossing :data="state.tableConfig" @change="pageChange">
          <template #bodyCell="{ scope, data }">
            <template v-if="scope.column.dataIndex === 'sms_temp_name'">
              <a-tooltip placement="top" :title="scope.record.sms_temp_name" trigger="hover">
                <div class="text_overflow3">
                  {{ scope.record.sms_temp_name }}
                </div>
              </a-tooltip>
            </template>
            <template v-if="scope.column.dataIndex === 'sms_description'">
              <a-tooltip placement="top" :title="scope.record.sms_description" trigger="hover">
                <div class="text_overflow3">
                  {{ scope.record.sms_description }}
                </div>
              </a-tooltip>
            </template>
            <template v-if="scope.column.dataIndex === 'sms_content'">
              <a-tooltip placement="top" :title="scope.record.sms_content" trigger="hover">
                <div class="text_overflow3">
                  {{ scope.record.sms_content }}
                </div>
                <div v-if="scope.record.product_code">商品ID：{{ scope.record.product_code }}</div>
              </a-tooltip>
            </template>
            <template v-if="scope.column.key === 'num'">
              <a-button type="link" class="p-0! h-auto" @click="handleActions('list', scope.record)">{{
                scope.record.send_count
              }}</a-button>
            </template>

            <template v-if="scope.column.key === 'createdAt'">
              <div>{{ secToTime(scope.record.created_at) }}</div>
            </template>
            <template v-if="scope.column.key === 'statuss'">
              <a-space size="small">
                <div class="status_label" :style="{ color: statusType(scope.record.sms_status)?.color }">
                  {{ statusType(scope.record.sms_status)?.text }}
                </div>
                <a-tooltip>
                  <template #title>{{ scope.record.refund }}</template>
                  <ExclamationCircleOutlined
                    v-if="scope.record.refund && scope.record.sms_status == 3"
                    class="c-#e63030 ml-4px"
                  />
                </a-tooltip>
              </a-space>
            </template>

            <template v-if="scope.column.key === 'action'">
              <a-space direction="vertical" align="start" class="action-group">
                <div v-if="[1, 3].includes(scope.record.sms_status)">
                  <a-button type="link" size="small" class="p-0! h-auto" @click="handleActions('edit', scope.record)"
                    >编辑</a-button
                  >
                </div>
                <a-button type="link" size="small" class="p-0! h-auto" @click="onDel(scope.record)">删除</a-button>
              </a-space>
            </template>
          </template>
        </TableZebraCrossing>
        <!-- </DescTableLayout> -->
      </template>
    </CardBaseLayout>
    <a-modal
      v-model:open="state.modalConfig.isVisible"
      :title="state.modalConfig.title"
      :width="state.modalConfig.width"
      centered
      destroyOnClose
    >
      <template #footer>
        <a-button v-if="['pass', 'noPass'].includes(state.modalConfig.warpKey)" @click="modalCancel">取消</a-button>
        <a-button v-if="['pass', 'noPass'].includes(state.modalConfig.warpKey)" type="primary" @click="modalOk"
          >确定</a-button
        >
      </template>
      <SendList
        v-if="['list'].includes(state.modalConfig.warpKey)"
        type="sms"
        source="dialog"
        :item="state.modalConfig.data"
      />
      <EditTemp
        v-if="['add', 'edit'].includes(state.modalConfig.warpKey)"
        :item="state.modalConfig.data"
        @event="onEvent"
      />
      <AuditTemp v-if="['audit'].includes(state.modalConfig.warpKey)" :item="state.modalConfig.data" @event="onEvent" />
    </a-modal>
  </div>
</template>
<script setup lang="tsx">
  defineOptions({ name: 'MsgTemp' })
  import SendList from '../recallStatistics/components/SendList.vue'
  import EditTemp from './components/EditTemp.vue'
  import AuditTemp from './components/AuditTemp.vue'
  import { setWechatConfAllList } from '@/api/common'
  import { onMounted, reactive, createVNode, watch, ref } from 'vue'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { useRouter, useRoute } from 'vue-router'
  import { message, Modal } from 'ant-design-vue'
  import { get_temp_list, get_temp_list_count, check_temp, delete_temp } from './index.api'
  import datas from './data'
  import { secToTime } from '@/utils'

  import moment from 'moment'
  const { SEARCH_CONFIG_DATA, BUTTON_RADIO_GROUP, statusType, columns, modalConfigTitle } = datas()
  const router = useRouter()
  const route = useRoute()
  const state = reactive({
    initParams: {
      page: 1,
      page_size: 10,
      sms_status: -1,
      created_at: ''
    },
    checked_list: [],
    selectionRowsPlus: [],
    selectionItem: [], // 表格选择的Item
    selectedRowKeys: [],
    searchConfig: SEARCH_CONFIG_DATA,
    actionConfig: BUTTON_RADIO_GROUP,
    tableConfig: {
      loading: false,
      size: 'small',
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 1200
      },
      columns: columns,
      dataSource: [],
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 10,
        size: 'small',
        showTotal: (total) => `共${total}条数据`
      }
    },
    selectTable: [],
    allSelectTable: [],
    indeterminate: false,
    modalConfig: {
      isVisible: false,
      title: '',
      width: 800,
      warpKey: '',
      data: {},
      form: {
        resp: ''
      }
    }
  })
  const searchFormDataRef = ref(null)
  // 搜索
  const changeValue = (data) => {
    console.log(data, 'datadata')
    // if (data.formData && data.formData.created_at) {
    state.initParams.start_time = data.formData.created_at ? data.formData.created_at[0] : ''
    state.initParams.end_time = data.formData.created_at ? data.formData.created_at[1] : ''
    // }
    if (data.status) {
      state.initParams = { ...state.initParams, ...data.formData, page: 1 }
      state.initParams.page = 1
    } else {
      state.initParams = {
        ...state.initParams,
        ...data.formData,
        page: 1,
        page_size: 10
      }
    }
    initPageData(state.initParams, true)
  }
  // 按钮操作
  const changeBtnType = (data, flag) => {
    state.actionConfig.value = data.value
    state.initParams.page = 1
    state.initParams.sms_status = data.value

    initPageData(state.initParams)
  }

  onMounted(() => {
    initPageData(state.initParams)
  })

  const initPageData = async (data, flag) => {
    try {
      state.tableConfig.loading = true
      const result = await get_temp_list(data)
      const params = {
        ...data,
        sms_status: -1
      }
      const resultCount = await get_temp_list_count(params)
      if (result.code === 0 && resultCount.code === 0) {
        if (result.data) {
          state.tableConfig.dataSource = (result.data.list || []).map((item) => {
            return {
              ...item,
              product_code:
                item.sms_config.OtherProductLink && Object.keys(item.sms_config.OtherProductLink).length
                  ? Object.values(item.sms_config.OtherProductLink)
                      .map((product) => product.product_code)
                      .join(', ')
                  : ''
            }
          })

          state.tableConfig.pagination.current = result.data?.page || 1
          state.tableConfig.pagination.total = result.data?.total || 0
          // 加载当前页选中的key
          // if (isArray(state.selectionRowsPlus)) {
          //   const selectKeys = state.selectionRowsPlus.map((v) => v.id)
          //   state.selectedRowKeys = state.tableConfig.dataSource
          //     .filter((v) => selectKeys.includes(v.id))
          //     .map((v) => v.id)
          // }
          if (Object.keys(resultCount.data).length > 0) {
            state.actionConfig.list.forEach((item) => {
              item.total = resultCount.data[item?.value] || 0
            })
          } else {
            state.actionConfig.list.forEach((item) => {
              item.total = 0
            })
          }
        } else {
          state.tableConfig.dataSource = []
          state.tableConfig.pagination.total = 0
        }
      }
    } catch (error) {
      console.error(error)
    } finally {
      if (flag) {
        state.selectedRowKeys = []
        state.selectionRowsPlus = []
      }
      state.tableConfig.loading = false
    }
  }
  const pageChange = (pagination) => {
    console.log(pagination, 'paginationpagination')

    state.initParams.page = pagination.current
    state.initParams.page_size = pagination.pageSize
    state.tableConfig.pagination.pageSize = pagination.pageSize
    initPageData(state.initParams)
  }
  async function getWechatConfAllList() {
    try {
      let { data } = await setWechatConfAllList({ page: 1, page_size: 999, status: 1 })
      state.searchConfig.data.forEach((v) => {
        if (v.field == 'app_id') {
          ;(data.list || []).forEach((item) => {
            v.props.options.push({
              value: item.app_id,
              label: item.app_name
            })
          })
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  getWechatConfAllList()

  // 集中处理 操作事件
  const handleActions = (type, data) => {
    // if (type == 'batch') {
    //   if (state.selectionRowsPlus.length == 0) return message.warning('请先选择一项')
    // }
    state.modalConfig.isVisible = true
    state.modalConfig.title = modalConfigTitle(type).title
    state.modalConfig.width = modalConfigTitle(type).width
    state.modalConfig.warpKey = type
    state.modalConfig.data = data
  }

  // 删除
  const onDel = async (row) => {
    try {
      const { data } = await check_temp(row.id)
      const temps = data.join('、')
      const tips = data.length ? `当前模板已关联【${temps}】，是否删除？` : `您确定要将【${row.sms_temp_name}】删除`
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, tips),
        async onOk() {
          await delete_temp(row.id)
          message.success('删除成功')
          initPageData(state.initParams)
        }
      })
    } catch (error) {
      console.error(error)
    }
  }

  const onEvent = (values) => {
    switch (values.cmd) {
      case 'close':
        modalCancel()
        break
      case 'edit':
        modalCancel()
        initPageData(state.initParams, true)
        break
    }
  }
  const modalCancel = () => {
    state.modalConfig.isVisible = false
    setTimeout(() => {
      state.modalConfig.title = ''
      state.modalConfig.warpKey = ''
      state.modalConfig.data = ''
      state.modalConfig.form.resp = ''
    }, 200)
  }
  const modalOk = async () => {
    let result = null
    if (result?.code === 0) {
      message.success('操作成功')
      state.selectTable = []
      state.indeterminate = false
      initPageData(state.initParams)
    }
    modalCancel()
  }
</script>
<style scoped lang="scss">
  @import '@/assets/css/mixin_scss_fn';
  img {
    display: block;
    width: 100%;
    height: 100%;
  }
  .card-warp-dex {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .action-warp {
    // margin-top: 5px;
    padding: 16px;
  }
  .desc-table-layout-list-item-desc {
    background-color: rgba(36, 47, 87, 0.05);
    padding: 8px 16px;
  }
  .text_overflow3 {
    @include text_overflow(3);
  }
  .cellos-item_title {
    @include set_font_config(--font-size-large, --text-color-base);
    @include text_overflow(2);
  }
  .cellos-item-prod-warp {
    box-sizing: border-box;
    padding: 0 8px;
    overflow: hidden;
  }
  .cellos-item_img {
    @include set_node_whb(70px, 70px);
  }
  .cellos-item_style {
    @include set_font_config(--font-size-tiny, --text-color-gray);
    @include text_overflow(1);
    margin: 4px 0px;
    box-sizing: border-box;
  }
  .cellos-item_id {
    @include set_font_config(--font-size-tiny, --text-color-gray);
  }
  .cellos-item_mask {
    @include set_font_config(--font-size-tiny, --primary-color);
  }
  .status_dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    color: #999999;
  }
  .status_label {
    font-size: 14px;
  }
  .table_star {
    display: flex;
    align-items: center;
  }
  .star_label {
    font-size: 14px;
  }
  .star-icon {
    transform: scale(0.8);
  }
  .appraise {
    word-break: break-word;
    @include text_overflow(2);
  }
  .mask-item {
    border-top: 1px solid #eaedf7;
    padding: 10px;
  }
  .table_appraise_box {
    width: 50px;
    height: 50px;
    overflow: hidden;
    position: relative;
    background: rgba(0, 0, 0, 0.8);
    .table_appraise_imgMark {
      position: absolute;
      width: 50px;
      height: 50px;
      top: 0px;
      left: 0px;
      text-align: center;
      line-height: 50px;
      font-size: 14px;
      color: #ffffff;
      pointer-events: none;
    }
  }
  :deep(.ant-btn-link) {
    padding-left: 0;
    padding-right: 5px;
  }
  .model_block {
    background-color: #fff;
    padding: 24px;
    margin-bottom: 10px;
    border-radius: var(--border-radius-huge);
  }
</style>
