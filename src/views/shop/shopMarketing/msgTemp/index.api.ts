import http from '@/utils/request'
// 获取动态标签
export const get_dynamic_tag = (data) => {
  return http('get', `/merchant/shop-sms-temp/dynamic-tag`, data)
}

// 新增模板
export const creat_temp = (data) => {
  return http('post', `/merchant/shop-sms-temp/create`, data)
}

// 更新模板
export const update_temp = (data) => {
  return http('post', `/merchant/shop-sms-temp/update`, data)
}
// 新增模板
export const get_temp_list = (data) => {
  return http('post', `/merchant/shop-sms-temp/list`, data)
}
// 新增模板
export const get_temp_list_count = (data) => {
  return http('post', `/merchant/shop-sms-temp/count`, data)
}
// 删除模板
export const delete_temp = (data) => {
  return http('post', `/merchant/shop-sms-temp/delete/${data}`, {})
}
// 模板
export const get_temp_info = (data) => {
  return http('get', `/merchant/shop-sms-temp/info/${data}`, {})
}

// 模板
export const examine_temp = (data) => {
  return http('post', `/merchant/shop-sms-temp/examine`, data)
}
// 模板检查
export const check_temp = (data) => {
  return http('get', `/merchant/shop-sms-temp/check/${data}`, {})
}
