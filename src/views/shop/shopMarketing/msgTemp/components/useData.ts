import { ref, reactive, watchEffect, watch } from 'vue'
import { debounce } from 'lodash-es'
import { get_dynamic_tag, creat_temp, get_temp_info, update_temp } from '../index.api'
import { message } from 'ant-design-vue'

export default function useData(emit, props) {
  const ruleForm = ref(null)
  const textareaCache = { start: '', end: '' }
  const text_content = ref(null)
  const validMsg = (rule, value) => {
    if (!value) return Promise.reject('请输入通知内容')
    if (!value.endsWith('拒收请回复R')) {
      return Promise.reject('短信后缀必须添加拒收请回复R')
    } else {
      console.log(value, '3333')
      return Promise.resolve()
    }
  }

  const validName = (rule, value) => {
    let reg = /[^\a-\z\A-\Z0-9\u4E00-\u9FA5-\(-\)-\（-\）]/g
    if (reg.test(value)) {
      return Promise.reject('请输入除符号以外的字符')
    } else {
      return Promise.resolve()
    }
  }

  const validateSmsContent = (rule, value, callback) => {
    if (!value) {
      return Promise.reject('请输入短信内容') // required 校验
    }
    // 检查是否包含 [ ] 或 【】
    const hasInvalidSymbols = /[\[\]【】]/.test(value)
    if (hasInvalidSymbols) {
      return Promise.reject('【】为短信签名专用符号，短信内容暂不支持使用')
    } else {
      return Promise.resolve()
    }
  }
  const rules = reactive({
    sms_temp_name: [{ required: true, message: '请输入模板名称', trigger: ['change', 'blur'] }],
    sms_content: [{ required: true, validator: validateSmsContent, trigger: ['change', 'blur'] }]
  })

  const state = reactive<any>({
    form: {
      sms_temp_name: undefined,
      sms_description: undefined,
      sms_content: undefined,
      sms_config: {
        OtherProductLink: {}
      }
    },
    modalConfig: {
      isVisible: false,
      title: '插入其他商品链接',
      width: 500,
      warpKey: '',
      data: {}
    },
    loading: false,
    btnList: [],
    eventInfo: undefined
  })

  const onDeleteEventObject = (item: number | string, index: number) => {
    if (state.form.notifyObjectList.indexOf(item) > -1) {
      state.form.notifyObjectList.splice(state.form.notifyObjectList.indexOf(item), 1)
    }
  }

  const getTagList = async () => {
    try {
      const res = await get_dynamic_tag({})
      state.btnList = (res.data || []).map((item) => {
        return {
          ...item,
          label: item.Name,
          value: '{' + item.Name + '}'
        }
      })
    } catch (error) {}
  }
  let otherIndex = 0

  const editecho = async () => {
    await getTagList()
    if (props.item?.id) {
      console.log('props.item', props.item)
      const { data } = await get_temp_info(props.item.id)

      state.form = {
        sms_content: data.shopSmsTemp.sms_content,
        sms_temp_name: data.shopSmsTemp.sms_temp_name,
        sms_config: data.shopSmsTemp.sms_config,
        sms_description: data.shopSmsTemp.sms_description
      }
      textareaCache.start = data.shopSmsTemp.sms_content
      const linkInfo = data.shopSmsTemp.sms_config.OtherProductLink
      const maxKeyNumber =
        Object.keys(linkInfo).length === 0
          ? 1
          : Math.max(
              ...Object.keys(linkInfo).map((key) => {
                const match = key.match(/\d+/) // 使用正则表达式提取数字
                return match ? parseInt(match[0], 10) : 1 // 如果找到数字，转换为整数；否则返回 1
              })
            )
      otherIndex = maxKeyNumber + 1
      console.log(maxKeyNumber, 'maxKeyNumbermaxKeyNumber')
    } else {
      otherIndex = 1
    }
  }
  editecho()
  watchEffect(() => {
    if (props.dialogType === 'add') {
      state.form.remark = ''
      state.form.scope = ''
      state.form.content = ''
      state.btnList = []
    } else {
      // editecho()
    }
  })

  // 点击按钮添加标签
  const onAddeventSign = debounce((e, event) => {
    // 判断是否需要加空格
    const shouldAddSpace = e.Value.includes('Link')
    const space = shouldAddSpace ? ' ' : '' // 如果需要加空格，则 space 为 ' '，否则为空字符串

    // 根据 e.Value 的值设置 currentTarget
    const currentTarget =
      e.Value == 'OrderProductLink' ? space + '{' + e.Name + '}' + space : space + '{' + e.Name + '}' + space

    if (e.Value == 'OtherProductLink') {
      state.goodsLink = space + '{' + e.Name + otherIndex + '}' + space
      state.goodsLinkVal = e.Value + otherIndex
      state.modalConfig.isVisible = true
      return
    }

    if (!textareaCache.start && !textareaCache.end) {
      state.form.sms_content = currentTarget
      textareaCache.start = currentTarget
    } else if (textareaCache.start) {
      state.form.sms_content = textareaCache.start + currentTarget + textareaCache.end
      textareaCache.start = textareaCache.start + currentTarget
    } else if (textareaCache.end) {
      state.form.sms_content = textareaCache.start + currentTarget + textareaCache.end
      textareaCache.end = currentTarget + textareaCache.end
    }
  }, 500)
  //点击内容文本框获取焦点
  const handleTextareaClick = () => {
    const textarea: any = document.getElementById('textarea')
    // const cursorPos = textarea.selectionStart

    let _content = state.form?.sms_content || ''
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd

    // 插入标签名称到光标位置或文本末尾
    const textBeforeCursor = _content.substring(0, startPos)
    const textAfterCursor = _content.substring(endPos)

    console.log(textBeforeCursor, 'textBeforeCursor')
    console.log(textAfterCursor, ' textAfterCursor')
    textareaCache.start = textBeforeCursor
    textareaCache.end = textAfterCursor
  }

  const onEvent = (values) => {
    switch (values.cmd) {
      case 'close':
        state.modalConfig.isVisible = false
        break
      case 'edit':
        state.modalConfig.isVisible = false
        otherIndex++
        const goodsObj = {
          [state.goodsLinkVal]: values.data
        }
        Object.assign(state.form.sms_config.OtherProductLink, goodsObj)
        console.log(state.form.sms_config, 'state.form.sms_configstate.form.sms_config')
        state.form.sms_content = textareaCache.start + state.goodsLink + textareaCache.end
        textareaCache.start = textareaCache.start + state.goodsLink
        break
    }
  }

  //保存
  const submitForm = async () => {
    try {
      await ruleForm.value.validate()
      if (!state.form.sms_content.endsWith('拒收请回复R')) {
        return message.warning('短信后缀必须添加“拒收请回复R”')
      }
      let params = {
        ...state.form
      }
      console.log(params, 'params.')

      let res
      if (props.item) {
        params.id = props.item.id
        res = await update_temp(params)
      } else {
        res = await creat_temp(params)
      }
      if (res.code == 0) {
        message.success('保存成功')
        emit('event', { cmd: 'edit' })
      } else {
        message.warning(res.msg)
        return
      }
    } catch (err) {
      console.log('err', err)
    }
  }
  //取消
  const close = () => {
    ruleForm.value?.clearValidate()
    if (props.dialogType == 'add') {
      state.form.remark = ''
      state.form.scope = ''
      state.form.event_sign = ''
      state.form.notify_object = ''
      state.form.notify_addr = ''
      state.form.notify_secret = ''
      state.form.notify_type = ''
      state.form.aite = ''
      state.form.content = ''
      state.btnList = []
    }
    emit('event', { cmd: 'close', status: true })
  }

  const handleSearchOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }

  return {
    state,
    rules,
    onAddeventSign,
    submitForm,
    close,
    onEvent,
    text_content,
    onDeleteEventObject,
    handleTextareaClick,
    ruleForm,
    handleSearchOption
  }
}
