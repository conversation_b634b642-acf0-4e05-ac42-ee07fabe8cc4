<template>
  <div class="notify-modal-wrapper">
    <a-form
      class="notify-form-wrapper"
      :model="state.form"
      ref="ruleForm"
      :rules="rules"
      :labelCol="{ style: 'width: 80px' }"
      autocomplete="off"
    >
      <a-form-item label="模板名称" name="sms_temp_name">
        <a-input
          placeholder="请输入模板名称"
          v-model:value="state.form.sms_temp_name"
          :disabled="item?.id"
          show-count
          :maxlength="30"
        />
      </a-form-item>
      <a-form-item label="描述" name="sms_description">
        <a-textarea
          class="flex-1"
          placeholder="请输入模板描述，最多200字"
          v-model:value="state.form.sms_description"
          show-count
          :rows="3"
          :maxlength="200"
        />
      </a-form-item>

      <a-form-item label="短信内容" name="sms_content">
        <a-textarea
          id="textarea"
          ref="text_content"
          class="flex-1"
          placeholder="请输入短信内容"
          v-model:value="state.form.sms_content"
          show-count
          :rows="3"
          @click="handleTextareaClick"
          @blur="handleTextareaClick"
        />
        <!-- <a-mentions
          id="textarea"
          v-model:value="state.form.sms_content"
          placeholder="# to mention tag"
          :prefix="['#']"
          :options="state.btnList"
          @click="handleTextareaClick"
          @blur="handleTextareaClick"
          rows="4"
        ></a-mentions> -->
      </a-form-item>
      <a-form-item label=" " name="" :colon="false">
        <div class="btn_list">
          <div v-for="(item, index) in state.btnList" :key="index">
            <a-button type="primary" ghost :class="['content_btn']" @click.stop="onAddeventSign(item, $event)">
              + {{ item.label }}
            </a-button>
          </div>
        </div>
        <div class="c-#FF5153 font-size-12px">
          <div>注：为避免审核不通过，短信后缀必须添加“拒收请回复R“。</div>
          <div>
            短信收费标准：短信内容若超出限定字数(70字符)，将按超出部分累计计费，若超出部分不足限定字数，也将按照限定字数收费，请您留意。
          </div>
        </div>
      </a-form-item>
    </a-form>
    <div class="item-demo">
      <div class="tips-title mb-5px">短信模板</div>
      <span class="c-#323232 font-500"> 模板示例：</span
      >您有一笔待付款订单，现在支付再减{立减金额}元；戳：{订单详情链接} 支付；有效期5分钟，拒收请回复R
    </div>
    <div class="fooler_btn">
      <div>
        <AButton :mr="30" @click="close">取消</AButton>
        <AButton
          type="primary"
          @click="submitForm"
          :loading="state.loading"
          :disabled="props.item && state.form.scope === 3"
          >保存</AButton
        >
      </div>
    </div>
    <a-modal
      v-model:open="state.modalConfig.isVisible"
      :title="state.modalConfig.title"
      :width="state.modalConfig.width"
      centered
      :footer="null"
      destroyOnClose
    >
      <SelectGoods @event="onEvent" />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  import useData from './useData'
  import SelectGoods from './SelectGoods.vue'
  const emit = defineEmits(['event'])
  const props = defineProps({
    item: {
      type: Object,
      deflaut: () => {}
    },
    dialogType: {
      type: String
    }
  })

  const { state, rules, onAddeventSign, onEvent, close, submitForm, text_content, handleTextareaClick, ruleForm } =
    useData(emit, props)
</script>
<style lang="scss" scoped>
  .notify-modal-wrapper {
    margin-right: -20px;
    .notify-form-wrapper {
      max-height: 500px;
      overflow: auto;
      padding-right: 20px;
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        /**/
      }
      &::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 2px;
      }
      &::-webkit-scrollbar-thumb {
        background: #c5c5c5;
        border-radius: 10px;
        display: block;
        padding-left: 30px;
      }
    }
    .form-item.no-mb {
      margin-bottom: 0;
    }

    .fooler_btn {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 12px;
      margin-right: 20px;
    }

    .btn_list {
      display: flex;
      flex-wrap: wrap;

      .ant-btn {
        padding: 4px 8px;
        font-size: 12px;
        line-height: 12px;
        height: auto;
        .anticon {
          padding: 0;
        }
        &.content_btn {
          border: 1px solid #edf2ff;
          color: #1677ff;
          margin-right: 8px;
          margin-bottom: 12px;
        }
      }
    }
  }
  .tips-title {
    width: 56px;
    height: 20px;
    background: linear-gradient(180deg, #d4e6ff 0%, #f2f6ff 100%);
    border-radius: 4px 0px 4px 0px;
    font-weight: 500;
    font-size: 12px;
    color: #1677ff;
    line-height: 20px;
    text-align: center;
    font-style: normal;
    margin-left: -8px;
  }
  .item-demo {
    background: #f2f6ff;
    margin-top: -6px;
    border-radius: 4px;
    color: #5e5e5e;
    font-size: 12px;
    margin-right: 20px;
    padding: 0px 8px 8px 8px;
  }
</style>
