<template>
  <div class="comp_dialog_sold_out">
    <a-form
      ref="ruleFormRef"
      :model="form"
      :rules="{
        remark: [{ required: true, trigger: ['change', 'blur'] }]
      }"
      :labelCol="{ style: { width: '123px' } }"
    >
      <a-form-item label="审核不通过原因" required name="refund_reason">
        <a-textarea
          :auto-size="{ minRows: 5 }"
          v-model:value="form.refund_reason"
          :maxlength="100"
          show-count
          placeholder="请输入至少五个字符"
        ></a-textarea>
      </a-form-item>
    </a-form>
    <div style="text-align: right" class="mt-16px">
      <a-button @click="emit('event', { cmd: 'close' })">取消</a-button>
      <a-button type="primary" @click="submitForm()" :loading="state.loading">保存</a-button>
    </div>
  </div>
</template>
<script lang="tsx" setup name="DialogSoldOut">
  import { reactive, ref } from 'vue'
  import { message } from 'ant-design-vue'
  const emit = defineEmits(['event'])
  const state = reactive({
    loading: false
  })
  const props = defineProps({
    item: {
      type: Object,
      default: () => {}
    },
    ids: String,
    // 审核不通过时的type
    type: [String, Number]
  })
  const form = reactive({
    ids: props.ids,
    refund_reason: ''
  })
  const ruleFormRef = ref()

  const validatorSize = (rule: any, value: string) => {
    if (value.trim().length < 5) {
      form.remark = value.trim()
      return Promise.reject('请输入最少五个字符')
    } else {
      return Promise.resolve()
    }
  }

  async function submitForm() {
    try {
      state.loading = true

      if (!(await ruleFormRef.value.validate())) return

      emit('event', { cmd: 'edit', data: form.refund_reason })
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
</script>

<style lang="scss" scoped>
  .comp_dialog_sold_out {
    color: #404040;
    .title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      margin-bottom: 20px;
    }
    .tip {
      font-size: 12px;
      color: #85878a;
    }
  }
</style>
