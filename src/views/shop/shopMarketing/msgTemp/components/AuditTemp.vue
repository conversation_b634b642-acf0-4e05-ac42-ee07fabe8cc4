<template>
  <div class="notify-modal-wrapper mb-62px">
    <div class="flex flex-justify-between">
      <div class="left item-info">
        <div class="item-title bg-#e5e7eb">当前版本</div>
        <a-form class="notify-form-wrapper pr-16px pb-16px" :labelCol="{ style: 'width: 90px' }" autocomplete="off">
          <a-form-item label="模板名称" name="name"> {{ state.info.sms_temp_name }} </a-form-item>
          <a-form-item label="描述" name="remark"> {{ state.info.sms_description }} </a-form-item>
          <a-form-item label="短信内容" name="content"> {{ state.info.sms_content }} </a-form-item>
        </a-form>
      </div>
    </div>
    <TableZebraCrossing :data="state.tableConfigOptions" @change="pageChange">
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'created_at'">
          <div>{{ secToTime(scope.record.created_at) }}</div>
        </template>
      </template>
    </TableZebraCrossing>
    <div class="bottom-btn">
      <div>
        <a-button :mr="30" @click="emit('event', { cmd: 'close' })">返回</a-button>
        <a-button type="primary" @click="submitForm('fail')" :loading="state.loading">审核不通过</a-button>
        <a-button type="primary" @click="submitForm('success')" :loading="state.loading">审核通过</a-button>
      </div>
    </div>
    <a-modal
      v-model:open="state.modalConfig.isVisible"
      :title="state.modalConfig.title"
      :width="state.modalConfig.width"
      centered
      :footer="null"
      destroyOnClose
    >
      <RejectModal v-if="['reject'].includes(state.modalConfig.warpKey)" @event="onEvent" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { secToTime } from '@/utils'
  import RejectModal from './RejectModal.vue'
  import { get_temp_info, examine_temp } from '../index.api'
  import { onMounted, reactive } from 'vue'
  const emit = defineEmits(['event'])
  const props = defineProps({
    item: {
      type: Object,
      deflaut: () => {}
    },
    dialogType: {
      type: String
    }
  })
  const state = reactive({
    loading: false,
    info: {},
    modalConfig: {
      isVisible: false,
      title: '模板审核',
      width: 700,
      warpKey: 'reject',
      data: {}
    },
    tableConfigOptions: {
      bordered: false,
      loading: false,
      dataSource: [],
      rowKey: 'created_at',
      columns: [
        {
          title: '操作时间',
          dataIndex: 'created_at',
          key: 'created_at',
          width: 200
        },
        {
          title: '操作人',
          dataIndex: 'author_name',
          key: 'author_name',
          width: 180
        },
        {
          title: '操作内容',
          dataIndex: 'content',
          key: 'content',
          width: 300
        }
      ],
      pagination: false
    }
  })
  onMounted(() => {
    console.log('-------id', props.id)
    initData()
  })
  const submitForm = async (type) => {
    if (type == 'fail') {
      state.modalConfig.isVisible = true
    } else {
      examineData(2, undefined)
    }
  }
  const examineData = async (status, msg) => {
    await examine_temp({ id: props.item.id, status: status, refund_reason: msg })
    emit('event', { cmd: 'edit' })
  }
  const initData = async () => {
    const { data } = await get_temp_info(props.item.id)
    state.info = data.shopSmsTemp
    state.tableConfigOptions.dataSource = data.shopSmsTemp?.logs || []
    // state.tableConfigOptions.loading = false
  }
  const onEvent = (values) => {
    switch (values.cmd) {
      case 'close':
        state.modalConfig.isVisible = false
        break
      case 'edit':
        state.modalConfig.isVisible = false
        examineData(3, values.data)
        break
    }
  }
</script>
<style lang="scss" scoped>
  .item-info {
    flex: 1;
    box-shadow: 0px 0px 6px 0px #dae9ff;
    border-radius: 8px;
  }
  .item-title {
    text-align: center;
    padding: 7px 0 6px 0;
    color: #333333;
    line-height: 1.4;
    border-radius: 8px 8px 0px 0px;
  }

  .bottom-btn {
    position: absolute;
    box-shadow: 0px 0px 7px 0px rgba(113, 113, 113, 0.24);
    border-radius: 8px 8px 0px 0px;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #fff;
    display: flex;
    padding: 16px;
    justify-content: center;
    align-items: center;
  }
</style>
