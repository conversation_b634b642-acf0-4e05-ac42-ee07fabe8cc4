<template>
  <div class="notify-modal-wrapper">
    <a-form
      class="notify-form-wrapper"
      :model="state.form"
      ref="ruleForm"
      :labelCol="{ style: 'width: 120px' }"
      autocomplete="off"
    >
      <!-- <a-form-item label="商品库" name="library_id">
        <a-select
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          v-model:value="state.form.library_id"
          :options="state.libraryList"
          @change="onChangeLibrary"
          placeholder="请选择模板名称"
        ></a-select>
      </a-form-item> -->
      <a-form-item label="商品信息" name="goods_id">
        <!-- <a-select
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          v-model:value="state.form.goods_id"
          :options="state.goodsList"
          placeholder="请选择商品落地页信息"
        ></a-select> -->
        <a-select
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          v-model:value="state.form.goods_id"
          :options="state.goodsList"
          @focus="getProductLibrary"
          @search="getProductLibrary"
          :filter-option="handleAdFilterOption"
          placeholder="请选择商品"
        ></a-select>
      </a-form-item>
    </a-form>

    <div class="fooler_btn">
      <div>
        <AButton :mr="30" @click="close">取消</AButton>
        <AButton type="primary" @click="submitForm" :loading="state.loading">保存</AButton>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, reactive } from 'vue'
  import { debounce, isString } from 'lodash-es'
  import { setProductList } from '@/views/shop/goods/goodList/index.api'
  const emit = defineEmits(['event'])
  const ruleForm = ref()
  const props = defineProps({
    item: {
      type: Object,
      deflaut: () => {}
    },
    dialogType: {
      type: String
    }
  })
  const state = reactive<any>({
    form: {
      name: undefined,
      type: 1,
      range: 1,
      remark: undefined,
      content: undefined
    },
    goodsList: [],
    libraryList: [],
    loading: false
  })

  const handleAdFilterOption = (input, option) => {
    const lowerInput = input.toLowerCase()
    return (
      option.title.toLowerCase().includes(lowerInput) ||
      option.code.toLowerCase().includes(lowerInput) ||
      option.title.includes(input) ||
      option.code.includes(input)
    )
  }
  // 商品列表
  const getProductLibrary = debounce(async (val) => {
    try {
      let keyword = val && isString(val) ? val : undefined
      const resp = await setProductList({ page: 1, page_size: 100, on_sale: 1, name_code: keyword })
      state.goodsList = (resp.data?.list || []).map((v) => {
        return {
          ...v,
          label: v.title,
          value: v.id
        }
      })
    } catch (error) {
      console.error(error)
    }
  }, 300)
  //保存
  const submitForm = async () => {
    try {
      const product_code = state.goodsList.find((item) => item.value === state.form.goods_id)?.code
      emit('event', {
        cmd: 'edit',
        data: {
          product_code: product_code,
          product_id: state.form.goods_id
        }
      })
    } catch (err) {
      console.log('err', err)
    }
  }
  //取消
  const close = () => {
    emit('event', { cmd: 'close' })
  }
</script>
<style lang="scss" scoped>
  .notify-modal-wrapper {
    margin-right: -20px;
    .notify-form-wrapper {
      padding-right: 20px;
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        /**/
      }
      &::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 2px;
      }
      &::-webkit-scrollbar-thumb {
        background: #c5c5c5;
        border-radius: 10px;
        display: block;
        padding-left: 30px;
      }
    }
    .form-item.no-mb {
      margin-bottom: 0;
    }

    .fooler_btn {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 12px;
      margin-right: 20px;
    }
  }
</style>
