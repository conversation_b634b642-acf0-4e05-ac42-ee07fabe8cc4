<template>
  <div class="qd_Edit">
    <a-form :model="data.addForm" :rules="rulesEdit" ref="ruleForm">
      <div class="form_div">
        <a-form-item label="问答内容:" name="">
          <div class="bg-#F4F6F9 p-8px">
            <div>
              {{ item.question }}
            </div>
            <!-- <fileUpload :size="0" :isEdit="false" v-model="data.images" class="main-img mt-8px"></fileUpload> -->
          </div>
        </a-form-item>
        <TableZebraCrossing :data="data.tableConfig" @change="pageChange">
          <template #bodyCell="{ scope, data }">
            <template v-if="scope.column.key === 'question'">
              <a-tooltip>
                <template #title>
                  <div>{{ scope.record.content }}</div>
                </template>
                <div class="question">{{ scope.record.content }}</div>
              </a-tooltip>
            </template>
            <template v-if="scope.column.key === 'createdAt'">
              <div>{{ secToTime(scope.record.created_at) }}</div>
            </template>
            <template v-if="scope.column.key === 'user'">
              <div v-if="scope.record.user_name">
                <div class="flex-y-center">
                  <img :src="scope.record.avatar" style="width: 20px; height: 20px; border-radius: 4px" alt="" />
                  <span class="ml-6px">{{ scope.record.user_name }}</span>
                </div>
              </div>
              <div v-else>--</div>
            </template>
            <template v-if="scope.column.key === 'user_type'">
              <div>{{ scope.record.user_name ? '用户回复' : '商家回复' }}</div>
            </template>
            <template v-if="scope.column.key === 'check'">
              <a-space size="small">
                <div class="status_label" :style="{ color: checkType(scope.record.check_status)?.color }">
                  {{ checkType(scope.record.check_status)?.text }}
                  <a-tooltip :title="scope.record.fail_msg">
                    <QuestionCircleOutlined v-if="scope.record.fail_msg" />
                  </a-tooltip>
                </div>
              </a-space>
            </template>

            <template v-if="scope.column.key === 'statuss'">
              <a-space size="small">
                <div
                  class="status_label"
                  v-if="![1, 0].includes(scope.record.check_status)"
                  :style="{ color: statusType(scope.record.is_vaild)?.color }"
                >
                  {{ statusType(scope.record.is_vaild)?.text }}
                </div>
                <div v-else>-</div>
              </a-space>
            </template>

            <template v-if="scope.column.key === 'action'">
              <a-space direction="vertical" align="start" class="action-group">
                <!-- v-show="[3].includes(scope.record.check_status)" -->
                <div v-if="[3].includes(scope.record.check_status)">
                  <a-button
                    type="link"
                    v-auth="['shopWenDaEdit']"
                    size="small"
                    @click="handleActions('edit', scope.record)"
                    >编辑</a-button
                  >
                </div>
                <div v-if="[-1, 2].includes(scope.record.is_vaild) && [2].includes(scope.record.check_status)">
                  <a-button
                    type="link"
                    v-auth="['shopWenDaYes']"
                    size="small"
                    @click="handleActions('pass', scope.record)"
                    >审核通过</a-button
                  >
                </div>
                <div v-if="[-1, 1].includes(scope.record.is_vaild) && [2].includes(scope.record.check_status)">
                  <a-button
                    type="link"
                    v-auth="['shopWenDaNo']"
                    size="small"
                    @click="handleActions('noPass', scope.record)"
                    >审核不通过</a-button
                  >
                </div>
              </a-space>
            </template>
          </template>
        </TableZebraCrossing>
      </div>
    </a-form>
    <div class="footer_button">
      <a-button @click="close">取消</a-button>
      <a-button type="primary" :loading="data.loading" @click="close">确认</a-button>
    </div>
    <a-modal
      v-model:open="state.modalConfig.isVisible"
      :title="state.modalConfig.title"
      :width="state.modalConfig.width"
      destroyOnClose
      centered
      :footer="null"
      :closable="false"
    >
      <div v-if="['pass'].includes(state.modalConfig.warpKey)">确定要将该问答设为审核通过吗？</div>
      <div v-if="['noPass'].includes(state.modalConfig.warpKey)">确定要将该问答设为审核不通过吗？</div>
      <div class="footer_button">
        <a-button v-if="['pass', 'noPass'].includes(state.modalConfig.warpKey)" @click="modalCancel">取消</a-button>
        <a-button v-if="['pass', 'noPass'].includes(state.modalConfig.warpKey)" type="primary" @click="modalOk"
          >确定</a-button
        >
      </div>
      <Reply
        v-if="['reply'].includes(state.modalConfig.warpKey)"
        :item="state.modalConfig.data"
        :type="state.modalConfig.warpKey"
        handle="edit"
        @event="onEvent"
      />
      <EditReply
        v-if="['edit'].includes(state.modalConfig.warpKey)"
        :item="state.modalConfig.data"
        :type="state.modalConfig.warpKey"
        handle="edit"
        @event="onEvent"
      />
    </a-modal>
  </div>
</template>

<script setup name="QaEdit">
  import { reactive, ref } from 'vue'
  import { get_question_info, update_question_status } from '../index.api.ts'
  import { message, Modal } from 'ant-design-vue'
  import { useRoute } from 'vue-router'
  import Reply from './Reply.vue'
  import { QuestionCircleOutlined } from '@ant-design/icons-vue'
  import EditReply from './EditReply.vue'
  // import { formatDate } from '@/utils'
  import { secToTime } from '@/utils'
  import moment from 'moment'
  import datas from '../data'

  const { statusType, checkType } = datas()
  const route = useRoute()
  const emit = defineEmits(['event'])
  const props = defineProps({
    item: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'append'
    }
  })
  const ruleForm = ref(null)
  const rulesEdit = {
    question: [{ required: true, message: '请输入问题', trigger: ['change'] }],
    resp: [{ required: true, message: '请输入答案', trigger: ['change'] }]
  }
  const data = reactive({
    addForm: {
      created_time: moment().format('YYYY-MM-DD HH:mm:ss'),
      content: '', //
      images: [] //
    },
    images: props.item.images.join(','),
    loading: false,
    tableConfig: {
      loading: false,
      size: 'small',
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 700
      },
      columns: [
        {
          title: '回复问答内容',
          key: 'question',
          dataIndex: 'appraise',
          width: 210
        },
        {
          title: '用户名称',
          key: 'user',
          dataIndex: 'user',
          width: 180
        },
        {
          title: '回复时间',
          key: 'createdAt',
          dataIndex: 'createdAt',
          width: 180
        },
        {
          title: '回复类型',
          key: 'user_type',
          dataIndex: 'user_type',
          width: 180
        },
        {
          title: '校验状态',
          key: 'check',
          dataIndex: 'check',
          width: 120
        },
        {
          title: '审核状态',
          key: 'statuss',
          dataIndex: 'statuss',
          width: 120
        },

        {
          title: '操作',
          dataIndex: 'action',
          key: 'action',
          fixed: 'right',
          width: 120
        }
      ],
      dataSource: [],
      pagination: false
    },
    dialog: {
      id: '',
      title: '',
      visible: false,
      width: '',
      type: ''
    },
    product: {} // 商品信息
  })
  const state = reactive({
    modalConfig: {
      isVisible: false,
      title: '',
      width: 600,
      warpKey: 'add',
      data: {}
    },
    initParams: {
      page: 1,
      page_size: 10
    }
  })

  const modalConfigTitle = (type) => {
    let modalConfig = {
      pass: {
        title: '提示',
        width: 500
      },
      noPass: {
        title: '提示',
        width: 500
      },
      edit: {
        title: '编辑问答',
        width: 600
      },

      reply: {
        title: '商家回复',
        width: 600
      }
    }
    return modalConfig[type]
  }
  const initData = async () => {
    if (props.item?.id) {
      const res = await get_question_info({ ids: props.item.id, ...state.initParams })

      data.mainData = res.data.list
      const newData = res.data.list[0].detail_resp
      console.log(data.mainData, 'data.mainData')
      console.log(newData, 'newDatanewData')
      data.tableConfig.dataSource = (newData || []).map((item) => {
        return {
          ...item
        }
      })
    }
  }
  initData()

  const handleActions = (type, row) => {
    state.modalConfig.isVisible = true
    state.modalConfig.title = modalConfigTitle(type).title
    state.modalConfig.width = modalConfigTitle(type).width
    state.modalConfig.warpKey = type
    state.status_item = row
    const currentItem = {
      ...data.mainData[0],
      detail_resp: row
    }
    console.log(currentItem, 'currentItem')

    state.modalConfig.data = currentItem
  }
  const modalCancel = () => {
    state.modalConfig.isVisible = false
    setTimeout(() => {
      state.modalConfig.title = ''
      state.modalConfig.warpKey = ''
      state.modalConfig.data = ''
    }, 200)
  }
  const onEvent = (values) => {
    switch (values.cmd) {
      case 'close':
        modalCancel()
        break
      case 'edit':
        modalCancel()
        initData()
        break
    }
  }

  const modalOk = async () => {
    let result = null
    if (['pass', 'noPass'].includes(state.modalConfig.warpKey)) {
      result = await update_question_status({
        status: state.status_item.is_vaild == 1 ? 2 : 1,
        type: state.modalConfig.data.is_vaild == 1 ? 2 : 1,
        id: state.status_item.id
      })
    }
    // if (['noPass'].includes(state.modalConfig.warpKey)) {
    //   result = await update_question_status({ status: 0, id: state.modalConfig.data.id })
    // }

    if (result?.code === 0) {
      message.success('操作成功')
      state.selectTable = []
      state.indeterminate = false
      initData()
    }
    modalCancel()
  }
  const close = () => {
    emit('event', { cmd: 'close' })
  }
</script>

<style lang="scss" scoped>
  :deep(.page_main_table) {
    margin-top: 0;
  }

  :deep(.header_row .col) {
    align-items: flex-start !important;
    padding-bottom: 0 !important;
  }
  .fooler_btn {
    text-align: end;
    margin-right: 20px;
  }
  .mt25 {
    margin-top: 25px;
  }

  .image_list {
    flex-wrap: wrap;

    .item,
    .upload {
      position: relative;
      width: 60px;
      height: 60px;
      background: #f1f5fc;
      border-radius: 4px;
      cursor: pointer;
      // overflow: hidden;
      margin-bottom: 10px;
    }

    .item {
      margin-right: 21px;

      :deep(.el-image) {
        width: 100%;
        height: 100%;
      }

      .name {
        height: 30px;
        background: linear-gradient(0deg, #000000, rgba(0, 0, 0, 0.01));
        border-radius: 0px 0px 4px 4px;
        color: #fff;
        position: absolute;
        bottom: 0;
        width: 100%;
        text-align: center;
        box-sizing: border-box;
        padding: 0 5px;
      }

      .del_img {
        position: absolute;
        top: -10px;
        right: -7px;
        display: none;
      }

      &:hover {
        .del_img {
          display: inline-block;
        }
      }
    }

    .upload {
      border: 1px dashed #d8dde8;

      .add {
        width: 26px;
      }

      .desc {
        color: #999;
        margin-top: 9px;
        line-height: normal;
      }
    }
  }

  .desc_bottom {
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #85878a;
    margin-top: 10px;
    line-height: 1;
  }

  .desc_bottom1 {
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #404040;
    margin-top: 0;
    line-height: 1;
  }

  .sales_Edit {
    .order_num_title {
      font-size: 18px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #080f1e;
      padding: 20px 30px;
      border-radius: 10px 10px 0px 0px;
      margin-bottom: 1px;
      background-color: #ffffff;
    }

    .order_num {
      padding: 20px 30px;
      background-color: #ffffff;
      border-radius: 0px 0px 10px 10px;
    }

    .order_none {
      margin: 68px 30px;
      display: flex;
      align-items: center;

      .none_image {
        width: 120px;
        height: 101px;
      }

      .none_text {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        margin-left: 15px;

        .text_color080 {
          color: #080f1e;
        }

        .text_color858 {
          color: #85878a;
          margin-top: 20px;
        }
      }
    }

    .order_table_title {
      border-radius: 10px 10px 0px 0px;
      margin-bottom: 1px;
      background-color: #ffffff;
      margin-top: 20px;
      height: 16px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #080f1e;
      padding: 20px 30px;
    }

    .order_table {
      background-color: #ffffff;
      border-radius: 0px 0px 10px 10px;
      padding: 20px 30px;
      margin-bottom: 20px;

      .table_goods {
        display: flex;

        .img {
          width: 70px;
          height: 70px;
          flex-shrink: 0;
          margin-right: 10px;
          border-radius: 6px;
        }

        .goods_info {
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .name {
            font-size: 14px;
            color: #080f1e;
            overflow: hidden; //多出的隐藏
            text-overflow: ellipsis; //多出部分用...代替
            display: -webkit-box; //定义为盒子模型显示
            -webkit-line-clamp: 2; //用来限制在一个块元素显示的文本的行数
            -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
          }

          .sku {
            font-size: 14px;
            color: #404040;
          }
        }
      }
    }

    .sales_apply {
      padding: 20px 30px;
      background-color: #ffffff;
      border-radius: 10px;
      margin-bottom: 20px;

      .apply_title {
        height: 18px;
        font-size: 18px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #080f1e;
        margin-bottom: 20px;
      }

      .addForm_div {
        .from_after_type {
          display: flex;
          margin-bottom: 10px;
        }

        .type_tip {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #85878a;
          line-height: 12px;
        }

        .addForm_price {
          width: 16px;
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #e63030;
          line-height: 16px;
        }

        .addForm-width {
          width: 700px;
        }
      }
    }

    .eidt_sub_bottom {
      padding: 20px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      border-radius: 6px;
      box-shadow: 0px -2px 5px 0px rgba(229, 229, 229, 0.27);
    }
  }

  .append-content {
    // background: #ffffff;
    // border-radius: 4px;
    // border: 1px dashed #d8d8d8;
    // padding: 8px;
    :deep(.ant-input-textarea-show-count > .ant-input) {
      background: #f4f6f9;
      border: none;
      border-radius: 4px;
    }
  }
  .footer_button {
    margin-top: 10px;
    text-align: right;
  }

  :deep(.ant-btn-link) {
    padding: 0 !important;
  }
</style>
