<template>
  <div class="">
    <div class="tag-goods">当前商品评分过低</div>
    <img :src="requireImg('goods/bg-suggest.png')" alt="" class="pos-absolute right-34px top-18px" />
    <div class="bg-suggest">
      <div
        v-if="props.type == 'ad'"
        class="font-size-12px bg-#FEF5E8 border border-solid mb-10px border-#FFE7C8 border-rd-4px c-#8D5B19 h-30px flex-y-center"
      >
        <SvgIcon class="font-size-12px ml-8px mr-8px" icon="icon-tips" />
        请检查优惠券在广告中配置内容
      </div>
      <div
        v-scroll-bar="{
          right: '-17px',
          width: '4px',
          background: 'rgba(0,0,0,0.3)'
        }"
        class="max-h-396px"
      >
        <div>
          <div class="list-suggest" v-if="suggestList.length">
            <div v-for="(item, index) in suggestList" :key="item" class="flex-y-center item-suggest">
              <div class="c-#647DFF font-600">建议{{ numToChinese(index) }}：</div>

              {{ item }}
            </div>
          </div>
          <div class="list-suggest" v-else>
            <div class="mt-40px mb-16px"><a-spin /></div>
            <span class="c-#d9d9d9 font-size-12px mb-40px">数据正在加载中；请稍后再试....</span>
          </div>
        </div>
      </div>
    </div>
    <div class="fooler_btn">
      <AButton :mr="30" @click="close">取消</AButton>
      <AButton type="primary" :disabled="!suggestList.length" @click="goOptimize">去优化</AButton>
    </div>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      centered
      destroyOnClose
    >
      <EditGoodsDialog v-if="state.dialog.type === 'editGoods'" :goodsDetail="goodsDetail" @event="onEvent" />
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, h, computed } from 'vue'
  import EditGoodsDialog from './EditGoodsDialog.vue'
  import { Modal } from 'ant-design-vue'
  import { checkGoodEdit } from '../index.api'
  import { useRouter } from '@/hooks'
  import { requireImg } from '@/utils'
  const { routerResolve } = useRouter()
  const props = defineProps({
    goodsDetail: {
      type: Object,
      default: {}
    },
    type: {
      type: String,
      default: ''
    }
  })
  const state = reactive({
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: ''
    }
  })
  const emit = defineEmits(['event'])
  const close = () => {
    emit('event', { cmd: 'close' })
  }
  const suggestList = computed(() => {
    const tips = props.goodsDetail.score_tips || props.goodsDetail.product_score_tips
    return Object.values(tips).filter((item) => item)
  })
  // 把数字1 转成 一  总共12个
  const numToChinese = (num) => {
    const hanArr = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二', '十三', '十四', '十五']
    return hanArr[num]
  }
  const onEvent = (v) => {
    if (v.cmd === 'close') {
      state.dialog.visible = false
      emit('event', { cmd: 'close' })
    }
  }
  const goOptimize = async () => {
    try {
      if (props.type == 'report') {
        const query = {
          code: props.goodsDetail.product_code
        }
        routerResolve({
          path: '/mall/goods/goods_list',
          query: query
        })
        return
      }
      let res = await checkGoodEdit({ id: props.goodsDetail.id })
      console.log('ressssss', res)
      if (res.code === 0 && res.data.length > 0) {
        console.log('res', res.data)
        let modal = await Modal.confirm({
          title: '提示',
          width: '482px',
          centered: true,
          content: h('div', {}, [
            h('div', { style: { 'line-height': '24px', 'max-height': '200px', overflow: 'auto' } }, [
              h('span', { style: { color: '#313233' } }, '该商品正在参与'),
              res.data.map((item) => {
                return h(
                  'span',
                  {
                    style: {
                      color: '#1677ff',
                      cursor: 'pointer',
                      padding: '0 2px'
                    },
                    onClick: () => {
                      modal.destroy()
                      routerResolve({
                        name: 'justBuyOne',
                        query: {
                          id: item.id,
                          type: item.type
                        }
                      })
                    }
                  },
                  `【${item.name}${item.active_type === 1 ? '（顺手买一件）' : '（周边推荐）'}】`
                )
              }),
              h('span', { style: { color: '#313233' } }, '活动，请先去活动中删除该商品，再编辑该商品落地页信息')
            ])
          ]),

          async onOk() {
            console.log('ok')
          }
        })
      } else {
        const record = props.goodsDetail
        const result = Object.keys(record.audit_status || {})
          .map((v: any) => record.audit_status[v])
          .filter((v: any) => v)
        if (result.length > 1) {
          state.dialog = { visible: true, title: '编辑商品', width: 418, type: 'editGoods' }
        } else {
          const query = {
            id: props.goodsDetail.id,
            is_temp: props.goodsDetail.audit_status.temp_status ? 1 : 0,
            is_online: props.goodsDetail.audit_status.on_sale_status ? 1 : 0,
            type: 'edit',
            isWeixinshop: props.goodsDetail.type === 4 ? 1 : 0
          }
          // const href = router.resolve({
          //   path: '/shop/shopGoods/goods-publish',
          //   query: query
          // })
          // window.open(href.href, '_blank')
          routerResolve({
            path: '/mall/goods/goods_edit',
            query: query
          })
        }
      }
    } catch (error) {
      console.log('---')
    }
  }
</script>
<style lang="scss" scoped>
  :deep(.ant-modal .ant-modal-content) {
    padding: 16px;
    background: linear-gradient(221deg, #dee3ff 0%, #ffffff 55%);
  }
  .main-pg {
    max-height: 400px;
    overflow: auto;
    padding-right: 10px;
  }
  .bg-suggest {
    border-radius: 12px;
    // max-height: 400px;
    padding-bottom: 1px;
    position: relative;
  }
  .list-suggest {
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 12px;
    .item-suggest {
      background: #f6f8fe;
      border-radius: 6px;
      border: 1px solid #e0e8fd;
      // box-shadow: 1px 1px 5px 0px rgba(218, 218, 218, 0.39);
      color: #384f66;
      padding: 12px 14px;
      margin-bottom: 10px;
      width: 100%;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .out-circle {
      border: 1px solid #c4deff;
    }
  }
  .tag-goods {
    width: 107px;
    height: 20px;
    background: #fffafa;
    border-radius: 2px;
    font-size: 12px;
    color: #fc3232;
    padding-left: 4px;
    border: 1px solid #ff9b9b;
    margin-left: 80px;
    margin-top: -31px;
    margin-bottom: 21px;
  }
  .fooler_btn {
    text-align: end;
    margin-top: 16px;
  }
</style>
