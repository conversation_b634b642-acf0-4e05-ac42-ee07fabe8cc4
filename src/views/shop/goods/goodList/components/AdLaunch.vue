<template>
  <div>
    <div>
      <!-- <div
        class="c-#8D5B19 mb-8px msg-item flex-y-center"
        v-if="!(goodsDetail.type === 4 || (goodsDetail.type !== 4 && goodsDetail.is_pardon === 1))"
      >
        该类目仅支持投放小店类型商品
      </div>  -->
      <div class="flex_ju_sp">
        <!-- v-auth:GoodsList="['adAdd']"  -->
        <!--  :disabled="!canCreateAdLink" -->
        <AButton v-auth="['shopShopCreatdAQI']" type="primary" @click="onShowDialog('add')">创建广告链接</AButton>
        <div class="flex_center">
          <div class="ad_name flex_center">
            <div class="icon" :style="{ backgroundColor: riskColors(data.adAppIdList[0]?.health) }"></div>
            <a-tooltip popper-class="toolt" placement="bottom" effect="light">
              <span class="name">{{ data.adAppIdList[0]?.app_name }}</span>
              <template #title>
                <div class="flex_ju_sp">
                  <span>原始ID：{{ data.adAppIdList[0]?.original_id }}</span>
                  <CopyOutlined class="copy_icon" @click="copy(data.adAppIdList[0]?.original_id)" />
                </div>
                <div class="flex_ju_sp">
                  <span>APPID：{{ data.adAppIdList[0]?.app_id }}</span>
                  <CopyOutlined class="copy_icon" @click="copy(data.adAppIdList[0]?.app_id)" />
                </div>
              </template>
            </a-tooltip>
          </div>
          <a-popover placement="bottom" trigger="hover" :overlayInnerStyle="{ width: '400px' }">
            <span style="cursor: pointer">更多</span>
            <template #title>
              <div class="pop_content">
                <div>已绑定小程序</div>
                <div class="tips">请使用健康状态小程序投放</div>

                <div class="flex_ju_sp">
                  <div v-for="v in colors" :key="v.value" class="flex_c">
                    <div class="icon" :style="{ backgroundColor: riskColors(v.value), margin: 'auto' }"></div>
                    <div>{{ v.name }}</div>
                  </div>
                </div>

                <a-divider />
                <div style="max-height: 400px; overflow-y: auto; padding-right: 10px">
                  <div v-for="(v, i) in data.adAppIdList" :key="i">
                    <div class="flex_align_center">
                      <div class="icon" :style="{ backgroundColor: riskColors(v.health) }"></div>
                      <div class="name">{{ v.app_name }}</div>
                    </div>
                    <div class="flex_ju_sp mar">
                      <div>原始ID：{{ v.original_id }}</div>
                      <CopyOutlined class="copy_icon" @click="copy(v.original_id)" />
                    </div>
                    <div class="flex_ju_sp mar">
                      <div>APPID：{{ v.app_id }}</div>
                      <CopyOutlined class="copy_icon" @click="copy(v.app_id)" />
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </a-popover>
        </div>
      </div>
      <div class="custom-searchForm">
        <SearchBaseLayout :data="schemas" :actions="formConfig" @changeValue="submitForm" />
      </div>
      <a-table
        :data-source="tableData.list"
        :columns="sdColumns"
        style="width: 100%"
        border
        :header-cell-style="{ background: '#F3F6FC', color: '#080F1E', fontWeight: 500 }"
        height="364"
        :scroll="{ x: 852, y: 300 }"
        @change="onTableChange"
        :pagination="{
          hideOnSinglePage: false,
          showQuickJumper: true,
          total: tableData.total,
          showSizeChanger: true,
          pageSize: tableData.pageSize,
          current: tableData.pageNo,
          size: 'small',
          showTotal: (total) => `共${tableData.total}条数据`
        }"
      >
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'ad_url'">
            <div>
              <a-tooltip>
                <template #title>{{ record.name }}</template>
                <span class="text_overflow block">广告名称：{{ record.name }}</span>
              </a-tooltip>
              <div
                class="flex font-size-12px mt-3px mb-3px"
                v-if="goodsDetail.score < 80 && goodsDetail.score > 0 && goodsDetail?.audit_status?.on_sale_status"
              >
                <div
                  class="bg-#FFF6F6 pl-4px pr-5px border h-20px border-solid border-#FDBEA1 border-rd-2px font-size-12px inline-flex flex-y-center"
                >
                  <SvgIcon icon="goods-score" class="font-size-12px mr-4px" />
                  <span class="c-#FF5400 ml-2px mt-1px">评分过低</span>
                </div>
                <span class="c-#647DFF underline cursor-pointer ml-8px" @click="onShowDialog('suggestion', record)"
                  >查看建议>></span
                >
              </div>
            </div>
            <div v-if="record.ad_account_id">
              <a-space :size="[6, 0]">
                <span>账户ID:</span>
                <span class="text_overflow">{{ record.ad_account_id || '--' }}</span>
              </a-space>
            </div>
            <div class="flex">
              <div>
                {{ record.wechat_app_name || '--' }}
              </div>
              <div class="flex_column">
                <div>
                  <span style="margin-left: 8px">APPID: {{ record.wechat_app_id }}</span>
                  <CopyOutlined class="copy_icon" @click="copy(record.wechat_app_id)" />
                </div>
                <div>
                  <span style="margin-left: 8px">原始ID: {{ record.original_id }}</span>
                  <CopyOutlined class="copy_icon" @click="copy(record.original_id)" />
                </div>
              </div>
            </div>
            <div class="flex_center" v-if="record.mini_page">
              <a-tooltip>
                <template #title>{{ record.mini_page }}</template>
                <span class="text_overflow block"
                  >{{ record.platform_id === 5 ? '小程序路径 : ' : '' }} {{ record.mini_page }}</span
                >
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.mini_page)" />
            </div>
            <!-- <div class="flex_center" v-if="record.ad_url">
              <a-tooltip>
                <template #title>{{ record.ad_url }}</template>
                <span class="text_overflow block">
                  {{ record.platform_id === 5 ? '落地页(一跳) : ' : '' }}{{ record.ad_url }}</span
                >
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.ad_url)" />
            </div> -->
            <div class="flex" v-if="record.landing_page_url">
              <a-tooltip>
                <template #title>{{ record.landing_page_url }}</template>
                <span class="text_overflow block max-w-360px">
                  落地页地址： <span>{{ record.landing_page_url }}</span>
                </span>
              </a-tooltip>
              <CopyOutlined
                class="copy_icon"
                v-if="goodsDetail.type == 4"
                @click="checkStatus(record.landing_page_url, record.id)"
              />
              <CopyOutlined class="copy_icon" v-else @click="copy(record.landing_page_url)" />
            </div>

            <div class="flex" v-if="record.direct_url && record.platform_id != 6">
              <a-tooltip>
                <template #title>{{ record.direct_url }}</template>
                <span class="text_overflow block max-w-360px">
                  直达链接： <span>{{ record.direct_url }}</span>
                </span>
              </a-tooltip>
              <CopyOutlined
                class="copy_icon"
                v-if="goodsDetail.type == 4"
                @click="checkStatus(record.direct_url, record.id)"
              />
              <CopyOutlined class="copy_icon" v-else @click="copy(record.direct_url)" />
            </div>
            <div class="flex" v-if="record.ad_url">
              <a-tooltip>
                <template #title>{{ record.ad_url }}</template>
                <span class="text_overflow block w-360px">
                  {{ [6, 8].includes(record.platform_id) ? 'H5二跳链接: ' : '小程序链接：' }}
                  <span>{{ record.ad_url }}</span>
                </span>
              </a-tooltip>
              <CopyOutlined
                class="copy_icon"
                v-if="goodsDetail.type == 4"
                @click="checkStatus(record.ad_url, record.id)"
              />
              <CopyOutlined class="copy_icon" v-else @click="copy(record.ad_url)" />
            </div>
            <div class="flex" v-if="record.platform_id == 6 && record.direct_url">
              <a-tooltip>
                <template #title>{{ record.direct_url }}</template>
                <span class="text_overflow block max-w-360px">
                  小程序链接：<span>{{ record.direct_url || '--' }}</span>
                </span>
              </a-tooltip>
              <CopyOutlined
                class="copy_icon"
                v-if="goodsDetail.type == 4"
                @click="checkStatus(record.direct_url, record.id)"
              />
              <CopyOutlined class="copy_icon" v-else @click="copy(record.direct_url)" />
            </div>
            <div class="flex" v-if="record.report_url">
              <a-tooltip>
                <template #title> {{ record.report_url }}</template>
                <span class="text_overflow block"> 报备链接：{{ record.report_url }}</span>
              </a-tooltip>
              <CopyOutlined
                class="copy_icon"
                v-if="goodsDetail.type == 4"
                @click="checkStatus(record.report_url, record.id)"
              />
              <CopyOutlined class="copy_icon" v-else @click="copy(record.report_url)" />
            </div>
            <div class="flex" v-if="record.detection_url">
              <a-tooltip>
                <template #title> {{ record.detection_url }}</template>
                <span class="text_overflow block">
                  {{ record.platform_id == 8 ? '接口地址：' : '监测链接：' }} {{ record.detection_url }}</span
                >
              </a-tooltip>
              <CopyOutlined
                class="copy_icon"
                v-if="goodsDetail.type == 4"
                @click="checkStatus(record.detection_url, record.id)"
              />
              <CopyOutlined class="copy_icon" v-else @click="copy(record.detection_url)" />
            </div>

            <div class="flex_align_center mt-15px">
              <img
                :src="
                  [0, 3, 4, 5].includes(record.jump_type)
                    ? requireImg('goods/icon_app.png')
                    : requireImg('goods/icon_h5.png')
                "
                alt=""
                style="margin-right: 10px"
              />
              <span class="item-tag item-blue">{{
                record.wechat_landing_type == 1 ? '图文落地页' : '视频落地页'
              }}</span>
              <template v-if="record.platform_id == 1">
                <span class="item-tag item-green"> {{ platform_text[record.platform_id] }}</span>
                <span class="item-tag item-green">微信支付</span>
              </template>
              <template v-else-if="record.platform_id === 6">
                <span class="item-tag item-ocean">巨量引擎</span>
                <span class="item-tag item-green">微信支付</span>
              </template>
              <template v-else-if="record.platform_id === 8">
                <span class="item-tag item-hc"> 超级汇川</span>
                <span class="item-tag item-hc">{{ record.jump_type == 3 ? '二跳小程序' : '锦帆建站' }}</span>
              </template>
              <template v-else>
                <span class="item-tag item-cili"> 快手广告</span>
                <span class="item-tag item-cili">{{ record.jump_type == 3 ? '二跳小程序' : '快手磁力建站' }}</span>
              </template>

              <span v-if="record.h5_landing_page_audit" class="item-tag item-green"
                >{{ record.h5_landing_page_audit }} {{ record.h5_landing_page_version }}</span
              >
            </div>
          </template>
          <template v-if="column.dataIndex === 'callback_ratio'">
            <div v-if="[1].includes(record.platform_id) && (shopInfo?.limit_callback === 0 || goodsDetail.type === 4)">
              <div>回传{{ !shopInfo?.limit_callback && goodsDetail.type !== 4 ? '100' : record.callback_ratio }}%</div>
            </div>
            <template v-else>
              <div v-if="!record.callback_type && shopInfo?.limit_callback">
                <div>
                  回传{{ record.callback_ratio }}%
                  <FormOutlined class="ml-4px c-primary" @click="onShowDialog('edit_feedback', record)" />
                </div>
                <div>整单金额</div>
              </div>
              <div v-else>
                <div v-if="record.callback_type === 1">
                  回传{{ record.callback_ratio }}%
                  <FormOutlined class="ml-4px c-primary" @click="onShowDialog('edit_feedback', record)" />
                </div>
                <div v-else>
                  按金额区间回传
                  <FormOutlined class="ml-4px c-primary" @click="onShowDialog('edit_feedback', record)" />
                </div>
                <div>
                  {{
                    {
                      1: '整单金额',
                      2: '单件金额',
                      3: '固定金额',
                      4: '固定比例',
                      5: '金额区间'
                    }[record.callback_amount_type]
                  }}回传
                </div>
              </div>
            </template>
          </template>
          <template v-if="column.dataIndex === 'created_at'">
            {{ record.created_at ? formatDate(record.created_at * 1000) : '' }}
          </template>
          <template v-if="column.dataIndex === 'style_type'">
            {{ styleType2Name(record.style_type) || '--' }}
          </template>
          <template v-if="column.dataIndex === 'handle'">
            <div class="handle_btns">
              <div class="flex_align_center space_between flex_column">
                <!-- v-auth:GoodsList="['adEdit']" -->
                <a-button type="link" v-auth="['shopShopEditAQI']" @click="onShowDialog('edit', record)">编辑</a-button>
                <div v-click-outside="() => (record.popViseble = false)">
                  <a-popover
                    v-model:open="record.popViseble"
                    trigger="click"
                    :overlayInnerStyle="{ width: '200px', padding: '15px', fontSize: '16px' }"
                  >
                    <template #title>
                      <div @click.stop="record.popViseble = true">
                        <div class="flex_ju_sp pop_title">
                          <span>落地页预览</span>
                          <img
                            class="close"
                            src="@/assets/icon/close.png"
                            alt=""
                            @click.stop="record.popViseble = false"
                          />
                        </div>
                        <div class="text_align_center">
                          <img
                            :src="data.preview_url"
                            style="min-width: 150px; min-height: 150px"
                            alt=""
                            v-if="data.preview_url"
                          />
                          <img
                            src="@/assets/images/empty/no_content.png"
                            style="width: 150px; height: 150px"
                            alt=""
                            v-else
                          />
                          <p>{{ data.preview_url ? '手机微信扫码预览' : '二维码加载中' }}</p>
                        </div>
                      </div>
                    </template>
                    <a-button type="link" @click="onPreview(record)">预览</a-button>
                  </a-popover>
                </div>
                <!-- v-auth:GoodsList="['adDelete']" -->
                <a-button type="link" v-auth="['shopShopDelAQI']" @click="onDel(record)">删除</a-button>
              </div>
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <a-modal
      :title="data.dialog.title"
      v-model:open="data.dialog.visible"
      :width="data.dialog.width"
      :wrapClassName="[data.dialog.type === 'suggestion' ? 'goods-suggestion' : '']"
      :close-modal="data.dialog.type != 'edit'"
      :footer="null"
      centered
      destroyOnClose
    >
      <AddAdLink
        v-if="data.dialog.type == 'edit' && data.dialog.visible"
        :goodsDetail="goodsDetail"
        :item="data.item"
        @event="onEvent"
      />
      <AddAdLinkFeedback
        v-if="data.dialog.type == 'edit_feedback' && data.dialog.visible"
        :goodsDetail="goodsDetail"
        :item="data.item"
        @event="onEvent"
      />
      <GoodsSuggestion v-if="data.dialog.type === 'suggestion'" type="ad" :goodsDetail="goodsDetail" @event="onEvent" />

      <!-- <AdLink v-if="data.dialog.type == 'link' && data.dialog.visible" :ad_url="data.item.ad_url" @event="onEvent" /> -->
    </a-modal>
  </div>
</template>
<script setup>
  import { watch, reactive, createVNode, computed } from 'vue'
  import SeachCom from '@/components/ui/common/SearchComponents/SearchBaseLayout.vue'
  import AddAdLinkFeedback from './AddAdLinkFeedback.vue'
  import { requireImg, formatDate, copy } from '@/utils'
  import { setAdList, deleteAd, miniCode, getAdAppidListApi, adInfoApi } from '../index.api'
  import AddAdLink from './AddAdLink.vue'
  import GoodsSuggestion from './GoodsSuggestion.vue'

  // import AdLink from './AdLink.vue'
  import datas from '../src/datas'
  import addatas from './src/datas'
  import { message, Modal } from 'ant-design-vue'
  import { CopyOutlined, ExclamationCircleOutlined, FormOutlined, WarningFilled } from '@ant-design/icons-vue'
  import { useApp } from '@/hooks'
  const { shopInfo } = useApp()
  // 导入广告平台、回传行为
  const { platformType } = datas()
  const { formConfig, schemas, sdColumns } = addatas()
  const emit = defineEmits(['close'])
  const props = defineProps({
    goodsDetail: {
      type: Object,
      default: () => {}
    }
  })
  const styleType2Name = (type) => {
    let obj = {
      1: '大众版',
      2: '老年版',
      3: '淘宝版',
      4: '拼多多版'
    }
    return obj[type]
  }
  const colors = [
    {
      value: 'normal',
      name: '健康'
    },
    {
      value: 'low_risk',
      name: '低风险'
    },
    {
      value: 'medium_risk',
      name: '中风险'
    },
    {
      value: 'high_risk',
      name: '高风险'
    },
    {
      value: 'lock_down',
      name: '风控中'
    }
  ]

  const platform_text = {
    1: '腾讯广告',
    4: '磁力引擎',
    5: 'Bilibili'
  }

  const tableData = reactive({
    list: [],
    // columns,
    pageNo: 1,
    pageSize: 10,
    total: 1,
    loading: false
  })

  const data = reactive({
    adAppIdList: [], //小程序原始ID
    popViseble: false,
    dialog: {
      id: '',
      type: '',
      width: '',
      visible: false,
      titie: ''
    },
    open_video: 1,
    visible: false,
    item: {},
    goods_details: {},
    preview_url: null,
    query: {
      page: tableData.pageNo || 1,
      page_size: tableData.pageSize || 10,
      product_id: props.goodsDetail.id,
      useShop: 'shop_id', //店铺id
      jump_type: -1
    }
  })
  // 计算是否可以创建广告链接
  const canCreateAdLink = computed(() => {
    const { type, is_pardon } = props.goodsDetail
    // const { hasLibrary } = data
    // 条件1: goodsDetail.type == 4
    // 条件2: goodsDetail.type !== 4 && goodsDetail.is_pardon == 1
    // 同时需要满足 hasLibrary 为 true
    // return hasLibrary && (type === 4 || (type !== 4 && is_pardon === 1))
    return type === 4 || (type !== 4 && is_pardon === 1)
  })
  // 广告投放列表
  const getAdList = async () => {
    try {
      tableData.loading = true
      const params = new URLSearchParams()
      Object.keys(data.query).forEach((key) => {
        if (data.query[key]) {
          if (key === 'exist_coupon_type' && data.query.exist_coupon_type?.length) {
            data.query.exist_coupon_type.forEach((val) => {
              params.append('exist_coupon_type', val)
            })
          } else {
            params.append(key, data.query[key])
          }
        }
      })
      const resp = await setAdList(params.toString())
      tableData.list = (resp.data?.list || []).map((v) => {
        return {
          ...v,
          popViseble: false
        }
      })
      tableData.total = resp.data?.total_num || 0
      tableData.loading = false
    } catch (error) {
      console.error(error)
      emit('close', false)
      tableData.loading = false
    }
  }

  // 风险颜色
  const riskColors = (type) => {
    let status = {
      normal: '#60A13B',
      low_risk: '#B9C2D1',
      medium_risk: '#D4A821',
      high_risk: '#E77316',
      lock_down: '#E63030'
    }
    return status[type]
  }
  // 搜素和重置
  const submitForm = (v) => {
    if (v.status) {
      data.query = {
        ...data.query,
        ...v.formData,
        jump_type: [0, 1].includes(v.formData.jump_type) ? v.formData.jump_type : -1
      }
      getAdList()
    } else {
      tableData.pageNo = 1
      data.query = {
        page: 1,
        page_size: tableData.pageSize || 10,
        product_id: props.goodsDetail.id,
        useShop: 'shop_id', //店铺id
        jump_type: -1
      }
      getAdList()
    }
  }

  const onTableChange = (page) => {
    data.query.page = page.current
    data.query.page_size = page.pageSize
    tableData.pageNo = page.current
    tableData.pageSize = page.pageSize
    getAdList()
  }

  // 获取小程序id
  const getAdAppidList = async () => {
    try {
      let res = await getAdAppidListApi({ product_id: props.goodsDetail.id })
      data.adAppIdList = res.data || []
    } catch (error) {
      console.error(error)
    }
  }
  getAdAppidList()

  // 预览
  const onPreview = async (item) => {
    ;(tableData.list || []).forEach((v) => {
      if (v.id == item.id) {
        v.popViseble = item.popViseble == true ? false : true
      } else {
        v.popViseble = false
      }
    })
    try {
      data.preview_url = null
      const resp = await miniCode({ id: item.id, useShop: 'shop_id' })
      data.preview_url = resp.data.image
    } catch (error) {
      console.error(error)
    }
  }
  // 删除
  const onDel = async (item) => {
    Modal.confirm({
      title: '提示',
      centered: true,
      icon: createVNode(ExclamationCircleOutlined),
      content: '请确认是否删除当前广告？',
      onOk() {
        return new Promise(async (resolve, reject) => {
          try {
            let res = await deleteAd({ id: item.id, useShop: 'shop_id' })
            getAdList()
            message.success(res.msg)
            resolve
          } catch (error) {
            reject(error)
          } finally {
            Modal.destroyAll()
          }
        }).catch(() => {
          message.warning('复制当前商品失败')
        })
      },
      onCancel() {
        Modal.destroyAll()
      }
    })
  }

  function platformFunc(type) {
    const obj = platformType.find((v) => v.value == type) || {}
    return obj.label || null
  }

  watch(
    () => props.goodsDetail,
    (val, old) => {
      data.goods_details = { ...val }
      data.query.product_id = data.goods_details.id
      getAdList()
      if (props.goodsDetail.type === 4) {
        schemas.value = schemas.value?.filter((it) => ['name', 'ad_account_id'].includes(it.field))
      }
    },
    {
      deep: true,
      immediate: true
    }
  )

  const onShowDialog = (type, item) => {
    data.item = { ...item }
    switch (type) {
      case 'add':
        data.dialog = { id: null, title: '创建广告链接', width: 700, visible: true, type: 'edit' }
        break
      case 'edit':
        data.dialog = { id: item.id, title: '编辑广告链接', width: 700, visible: true, type: 'edit' }
        break
      case 'link':
        data.dialog = { id: null, title: '广告链接', width: 742, visible: true, type: 'link' }
        break
      case 'edit_feedback':
        data.dialog = { id: item.id, title: '回传比例', width: 700, visible: true, type: 'edit_feedback' }
        break
      case 'suggestion':
        data.dialog = { visible: true, title: '优化建议', width: 680, type: 'suggestion' }
        break
    }
  }

  const onEvent = (values) => {
    switch (values.cmd) {
      case 'close':
        data.dialog.visible = false
        break
      case 'edit':
        data.dialog.visible = false
        getAdList()
        break
    }
    data.dialog.type = ''
  }
  //小店校验状态
  const checkStatus = async (url, id) => {
    try {
      await adInfoApi({ id: id })
    } catch (error) {
      console.log(error)
    } finally {
      copy(url)
    }
  }
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';
  :deep(.el-divider--horizontal) {
    margin: 10px 0;
  }

  // .text_overflow {
  //   @include text_overflow(2);
  // }

  .icon {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-right: 10px;
  }

  .pop_content {
    .tips {
      color: #999;
      margin: 5px 0;
    }

    .mar {
      margin: 5px 0;
    }
  }

  .ad_name {
    .name {
      margin-right: 30px;
    }
  }

  .copy_icon {
    margin-left: 10px;
    cursor: pointer;
    color: var(--el-color-primary);
  }

  :deep(.el-table) {
    border-radius: 10px;
    margin-top: 20px;
  }

  :deep(.el-dialog__body) {
    padding: 0 30px 30px 30px !important;
  }

  :deep(.el-table .cell) {
    padding: 0 12px;
    color: #080f1e;
  }

  :deep(.el-popper.is-dark) {
    max-width: 450px;
    line-height: 1.4;
    color: #fff;
    font-weight: 400;
    word-break: break-all;
  }
  :deep(.custom-searchForm) {
    margin-top: 20px;
    margin-bottom: 20px;
    .comp_searchForm {
      .form_item {
        width: 180px;
      }
      .kk_btn_group {
        display: none;
      }
    }
  }

  .pop_title {
    margin-bottom: 10px;

    .close {
      cursor: pointer;
    }
  }
  .item-blue {
    border: 1px solid #3f9fff;
    color: #3f9fff;
  }
  .item-green {
    border: 1px solid #70b606;
    color: #70b606;
  }
  .item-cili {
    border: 1px solid #fe4a08;
    color: #fe4a08;
  }
  .item-hc {
    border: 1px solid #ff7800;
    color: #ff7800;
  }
  .item-ocean {
    border: 1px solid #2a55e5;
    color: #2a55e5;
  }
  .item-tag {
    border-radius: 5px;
    font-size: 12px;
    padding: 0 6px;
    margin-right: 16px;
  }
  .space_between {
    justify-content: space-between;
  }

  .text_align_center {
    text-align: center;
  }

  .handle_btns {
    user-select: none;

    span {
      color: var(--el-color-primary);
      cursor: pointer;
      margin-right: 10px;
    }
  }

  .copy_icon {
    margin-left: 10px;
    cursor: pointer;
    color: var(--el-color-primary);
  }

  .divider {
    margin-top: 5px;
    padding-bottom: 5px;
    border-bottom: var(--el-border);

    &:nth-last-of-type(1) {
      border-bottom: none;
    }
  }
  .ant-btn + .ant-btn {
    margin-left: 0;
  }
  .msg-item {
    background: #fef5e8;
    border-radius: 5px;
    border: 1px solid #ffe7c8;
    padding: 5px 0 5px 8px;
    line-height: 20px;
    height: 30px;
  }
</style>
