<template>
  <div class="ad_link">
    <a-form
      :model="data.form"
      ref="ruleForm"
      :rules="adRules"
      :labelCol="{ style: 'width: 130px' }"
      autocomplete="off"
      class="ad_form"
    >
      <!-- @finish="submitForm(ruleForm)" -->
      <a-form-item label="广告名称：" name="name">
        <a-input v-model:value="data.form.name" placeholder="仅用于内部查看" :maxlength="100" />
      </a-form-item>
      <a-form-item label="广告平台：" name="platform_id">
        <!-- <a-select
          :disabled="data.form.id ? true : false"
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          v-model:value="data.form.platform_id"
          placeholder="请选择广告平台"
          @change="onChangePlatform"
        >
          <a-select-option v-for="v in data.platformList" :key="v.value" :value="v.value">{{
            v.label
          }}</a-select-option>
        </a-select> -->
        <a-radio-group
          v-model:value="data.form.platform_id"
          size="small"
          :disabled="!!props.item?.id"
          @change="changePlatform"
        >
          <a-radio :value="1">广点通</a-radio>
          <a-radio :value="4">磁力引擎 </a-radio>
          <a-radio :value="8" v-if="!isWeixinshop">超级汇川 </a-radio>
          <!-- shopInfo?.douyin_entities -->
          <a-radio :value="6" v-if="isWeixinshop && shopInfo?.douyin_entities"
            ><span class="c-#333">巨量引擎</span></a-radio
          >
        </a-radio-group>
      </a-form-item>
      <a-form-item
        label="账户ID："
        :name="data.form.platform_id == 1 ? 'ad_account_id' : null"
        v-if="data.form.platform_id != 8"
      >
        <div class="flex_btn flex">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="data.form.ad_account_id"
            filterable
            allowClear
            placeholder="请选择账户ID"
            @focus="getAdDmpList"
            @search="getAdDmpList"
            popper-class="select_show_id"
            :disabled="!!!data.form.platform_id"
            show-search
            :filter-option="handleAdFilterOption"
          >
            <a-select-option
              v-for="v in data.adList"
              :key="v.id"
              :value="v.token_account_id"
              :disabled="v.account_role_type === 'ACCOUNT_ROLE_TYPE_AGENCY' && data.form.platform_id == 1"
              :account_name="v.account_name"
            >
              <div>
                <div>账户名称：{{ v.account_name }}</div>
                <div>账户ID：{{ v.token_account_id }}</div>
              </div>
            </a-select-option>
          </a-select>
          <!-- v-auth:AdDmp="['adAccountAuth']" -->
          <div class="flex_center" style="margin-left: 15px">
            <AButton type="primary" @click="onAuth">新授权</AButton>
          </div>
        </div>
        <span class="callback-tips">注：授权广告账户后您将获得更完整的数据报表功能</span>
      </a-form-item>
      <a-form-item key="jump—type" required v-if="data.form.platform_id == 1">
        <template #label>
          <div class="flex-align">
            <span>跳转类型</span>
            <a-tooltip class="ml-4px">
              <template #title>
                {{ skip_text }}
              </template>
              <QuestionCircleFilled />
            </a-tooltip>
          </div>
        </template>
        <a-radio-group
          v-model:value="data.form.jump_type"
          size="small"
          :disabled="!!props.item?.id"
          class="jump_type_cls"
          @change="changeLandingType"
        >
          <a-radio :value="0">小程序落地页</a-radio>
          <a-radio
            v-if="
              data.landing_page_switch == 1 &&
              [1, undefined].includes(data.form.platform_id) &&
              !useInfo.is_private_platform &&
              !isWeixinshop
            "
            :value="1"
            >H5落地页</a-radio
          >
        </a-radio-group>
      </a-form-item>
      <a-form-item key="jump—type" required v-if="[4, 8].includes(data.form.platform_id)">
        <template #label>
          <div class="flex-align">
            <span>跳转方式</span>
          </div>
        </template>
        <a-radio-group
          v-model:value="data.form.jump_type"
          size="small"
          class="jump_type_cls"
          :disabled="!!props.item?.id"
          @change="changeLandingType"
        >
          <a-radio :value="3"
            >二跳小程序
            <!-- <a-tooltip placement="top" trigger="hover">
              <template #title> 广告触发默认进入中间页，通过点击中间页按钮，跳转进入小程序，需填充中间页信息 </template>
              <QuestionCircleFilled />
            </a-tooltip> -->
          </a-radio>
          <a-radio :value="4" v-if="[4].includes(data.form.platform_id)"
            >快手磁力建站<a-tooltip placement="top" trigger="hover">
              <template #title> 广告触发直接跳转进入小程序（中间页自动跳过） </template>
              <QuestionCircleFilled /> </a-tooltip
          ></a-radio>
          <!-- <a-radio :value="5" v-if="[8].includes(data.form.platform_id)"
            >锦帆建站<a-tooltip placement="top" trigger="hover">
              <template #title> 广告触发直接跳转进入小程序（中间页自动跳过） </template>
              <QuestionCircleFilled /> </a-tooltip
          ></a-radio> -->
        </a-radio-group>
      </a-form-item>
      <!-- 磁力-二跳-new -->
      <template
        v-if="
          (data.form.jump_type === 3 && data.form.platform_id === 4) ||
          (data.form.jump_type === 3 && data.form.platform_id === 8) ||
          data.form.platform_id === 6
        "
      >
        <a-form-item
          v-if="
            (data.form.jump_type === 3 && data.form.platform_id === 4) ||
            (data.form.jump_type === 3 && data.form.platform_id === 8)
          "
          label="跳转中间页"
          name="image"
          class="jumpTypePage"
          :rules="{ 
            required: true,
            validator:(_:any,__:string,callback:Function)=>{
              if(!data.form.web_center.image.length){
                callback('请上传图片')
              }
              callback()
            }, trigger: ['change', 'blur'] }"
        >
          <Upload accept=".jpg,.png,.jpeg,.gif" max="1" size="3" v-model="data.form.web_center.image" />
        </a-form-item>
        <a-form-item key="jump—type" name="jump_type" v-if="data.form.platform_id === 6">
          <template #label>
            <div class="flex-align">
              <span>跳转类型</span>
              <a-tooltip>
                <template #title> 投放二跳需配置中转页，点击中转页底部按钮，跳转进入小程序 </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-tooltip>
            </div>
          </template>
          <a-radio-group v-model:value="data.form.jump_type" size="small" class="jump_type_cls">
            <a-radio :value="3">
              <span class="c-#333">二跳小程序</span>
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="data.form.platform_id === 6"
          label="H5中转页"
          name="type"
          :rules="{ required: true,validator:(_:any,__:string,callback:Function)=>{
                    if(!data.form.web_center.type){
                      callback('请选择H5中转页')
                    }
                    callback()
                  }, trigger: ['change', 'blur'] }"
          :class="[data.form.web_center.type === 1 ? 'mb-0!' : '']"
        >
          <a-radio-group
            v-model:value="data.form.web_center.type"
            size="small"
            :disabled="!!props.item?.id"
            class="jump_type_cls"
          >
            <a-radio :value="1">
              <span class="c-#333">自定义</span>
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          class="ml-130px"
          v-if="data.form.web_center.type === 1 && data.form.platform_id === 6"
          name="image"
          :rules="{ required: true,validator:(_:any,__:string,callback:Function)=>{
                    if(!data.form.web_center.image.length){
                      callback('请上传图片')
                    }
                    callback()
                  }, trigger: ['change', 'blur'] }"
        >
          <Upload accept=".jpg,.png,.jpeg,.gif" max="1" size="3" v-model="data.form.web_center.image" />
          <template #extra> 查看默认样式，支持大小3M内，图片或Gif，建议宽度：750*1600px </template>
        </a-form-item>
        <a-form-item
          name="title"
          :rules="{ 
            required: true,
            validator:(_:any,__:string,callback:Function)=>{
              if(!data.form.web_center.title){
                callback('请输入H5页面标题')
              }
              callback()
            }, 
            trigger: ['change', 'blur'] }"
        >
          <template #label>
            <div class="flex-align">
              <span>页面标题</span>
              <a-popover placement="right" trigger="hover" :overlayInnerStyle="{ padding: 0 }">
                <template #content>
                  <div>
                    <a-image style="width: 296px; height: 212px" :src="landPageList[4]" />
                  </div>
                </template>
                <QuestionCircleFilled class="ml-4px" />
              </a-popover>
            </div>
          </template>
          <a-input
            v-model:value="data.form.web_center.title"
            placeholder="请输入H5页面标题"
            :maxlength="8"
            show-count
          />
        </a-form-item>
        <a-form-item required>
          <template #label>
            <div class="flex-align">
              <span>底部按钮</span>
              <a-tooltip>
                <template #title> 点击底部按钮用于拉起二跳小程序页面 </template>
                <QuestionCircleFilled class="ml-4px" />
              </a-tooltip>
            </div>
          </template>
          <a-form-item
            name="button_style"
            :rules="{ 
              validator:(_:any,__:string,callback:Function)=>{
                if(!data.form.web_center.button_style){
                  callback('请选择底部按钮')
                }
                callback()
              }, trigger: ['change', 'blur'] }"
            :class="[data.form.web_center.button_style === 4 ? 'mb-0!' : 'mb-16px!']"
          >
            <a-radio-group
              v-model:value="data.form.web_center.button_style"
              size="small"
              class="jump_type_cls a-radio-group-con"
              @change="buttonBtnChange"
            >
              <a-radio :value="1"
                >置底悬浮
                <a-popover placement="right" trigger="hover" :overlayInnerStyle="{ padding: 0 }" class="ml4px">
                  <template #content>
                    <div>
                      <a-image style="width: 296px; height: 212px" :src="landPageList[5]" />
                    </div>
                  </template>
                  <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
                </a-popover>
              </a-radio>
              <a-radio :value="2"
                >偏向底部
                <a-popover placement="right" trigger="hover" :overlayInnerStyle="{ padding: 0 }" class="ml4px">
                  <template #content>
                    <div>
                      <a-image style="width: 296px; height: 212px" :src="landPageList[6]" />
                    </div>
                  </template>
                  <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
                </a-popover>
              </a-radio>
              <a-radio :value="3"
                >上滑按钮
                <a-popover placement="right" trigger="hover" :overlayInnerStyle="{ padding: 0 }" class="ml4px">
                  <template #content>
                    <div>
                      <a-image style="width: 296px; height: 212px" :src="landPageList[7]" />
                    </div>
                  </template>
                  <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
                </a-popover>
              </a-radio>
              <a-radio :value="4"
                >隐藏按钮
                <a-popover placement="right" trigger="hover" :overlayInnerStyle="{ padding: 0 }" class="ml4px">
                  <template #content>
                    <div>
                      <a-image style="width: 296px; height: 212px" :src="landPageList[8]" />
                    </div>
                  </template>
                  <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
                </a-popover>
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item
            name="button"
            class="w-100% mb-16px"
            :rules="{ 
              validator:(_:any,__:string,callback:Function)=>{
                if(!data.form.web_center.button){
                  callback('请输入底部按钮文案')
                }
                callback()
              }, trigger: ['change', 'blur'] }"
            v-if="data.form.web_center.button_style !== 4"
          >
            <a-input
              class="w-100%"
              v-model:value="data.form.web_center.button"
              placeholder="请输入底部按钮文案"
              :maxlength="15"
              show-count
            />
          </a-form-item>
          <a-form-item name="is_click" class="mb-0!">
            <a-checkbox
              v-model:checked="data.form.web_center.is_click"
              :disabled="data.form.web_center.button_style === 4"
            >
              点击图片打开小程序
              <a-tooltip>
                <template #title> 手动点击H5页面，跳转打开小程序 </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-tooltip>
            </a-checkbox>
          </a-form-item>
          <a-form-item name="is_direct" class="mb-0!" v-if="data.form.platform_id != 6">
            <a-checkbox v-model:checked="data.form.web_center.is_direct">
              优先极速拉起小程序
              <a-tooltip>
                <template #title> 自动跳转打开小程序 </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-tooltip>
            </a-checkbox>
          </a-form-item>
        </a-form-item>
        <a-form-item
          v-if="data.form.platform_id === 4"
          label="主体信息"
          name="shop_entity_id"
          :rules="{
            required: true,
            message: '请选择主体信息',

            trigger: ['change', 'blur']
          }"
        >
          <div class="flex-y-center">
            <a-select
              class="w-430px! flex-none"
              :getPopupContainer="(triggerNode:any) => triggerNode.parentNode"
              v-model:value="data.form.shop_entity_id"
              filterable
              show-search
              clearable
              :options="data.subjectList"
              placeholder="请选择主体信息"
              :filter-option="handleSubjectFilterOption"
            >
            </a-select>
            <a-button type="link" class="ml-12px p-0! h-auto!" @click="onShowDialog('subject')">新建</a-button>
            <a-divider type="vertical" />
            <a-button type="text" class="p-0! h-auto!" @click="getSubjectList">刷新</a-button>
          </div>
        </a-form-item>
        <a-form-item
          v-if="false"
          name="ad_entity_id"
          :rules="{
            required: true,
            message: '请选择主体信息',

            trigger: ['change', 'blur']
          }"
        >
          <template #label>
            <div class="flex-align">
              <span>主体信息</span>
              <a-tooltip>
                <template #title> 该主体信息将在中转页底部使用 </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-tooltip>
            </div>
          </template>
          <div class="flex-y-center">
            <a-select
              class="w-430px! flex-none"
              :getPopupContainer="(triggerNode:any) => triggerNode.parentNode"
              v-model:value="data.form.ad_entity_id"
              filterable
              show-search
              clearable
              :options="data.subjectList"
              placeholder="请选择主体信息"
              :filter-option="handleSubjectFilterOption"
            >
            </a-select>
            <a-button type="link" class="ml-12px p-0! h-auto!" @click="onShowDialog('subject')">新建</a-button>
            <a-divider type="vertical" />
            <a-button type="text" class="p-0! h-auto!" @click="getSubjectList">刷新</a-button>
          </div>
        </a-form-item>
      </template>
      <a-form-item label="小程序：" name="wechat_app_name" v-if="[1, 4, 8].includes(data.form.platform_id)">
        <a-input v-model:value="data.form.wechat_app_name" placeholder="" :maxlength="100" disabled />
        <!-- <a-select
          v-else
          :getPopupContainer="(triggerNode:any) => triggerNode.parentNode"
          v-model:value="data.form.wechat_app_name"
          :options="data.appletList"
          :disabled="!!props.item?.id"
          :field-names="{ label: 'app_name', value: 'app_id' }"
          filterable
          show-search
          clearable
          placeholder="请选择小程序"
        >
        </a-select> -->
      </a-form-item>
      <a-form-item
        label="小程序名称"
        name="wechat_app_id"
        :rules="{
          required: true,
          message: '请选择小程序',
          trigger: ['change', 'blur']
        }"
        v-else
      >
        <a-select
          :getPopupContainer="(triggerNode:any) => triggerNode.parentNode"
          v-model:value="data.form.wechat_app_id"
          :options="data.appletList"
          :field-names="{ label: 'app_name', value: 'app_id' }"
          filterable
          show-search
          clearable
          :disabled="data.form.platform_id == 6 && !!props.item?.id"
          placeholder="请选择小程序"
          @change="appNameChange"
        >
        </a-select>
      </a-form-item>
      <a-form-item key="wx-landing—type" v-if="![4, 5].includes(data.form.platform_id)" class="frequired">
        <template #label>
          <div class="flex_align_center">
            <span>落地页类型</span>
            <a-popover placement="right" trigger="hover" width="380" class="14px">
              <template #title>
                <div class="flex justify-center">
                  <div class="flex_column flex">
                    <span>图文落地页</span>
                    <a-image style="width: 160px" :src="requireImg('goods/new_pic_text_landing.png')" />
                  </div>
                </div>
              </template>
              <QuestionCircleFilled />
            </a-popover>
          </div>
        </template>
        <a-radio-group v-model:value="data.form.wechat_landing_type" size="small" class="jump_type_cls">
          <a-radio :value="1">
            <div class="flex-y-center">
              图文落地页
              <!-- <a-popover placement="right" trigger="hover" width="380">
                <template #title>
                  <div class="flex justify-center">
                    <div class="flex_column flex">
                      <span>图文落地页</span>
                      <a-image style="width: 160px" :src="requireImg('goods/new_pic_text_landing.png')" />
                    </div>
                  </div>
                </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-popover> -->
            </div>
          </a-radio>
          <a-radio :value="2" v-if="[0, 3, 5].includes(data.form.jump_type)">
            <div class="flex-y-center">
              视频落地页
              <a-popover placement="right" trigger="hover" width="380">
                <template #title>
                  <div class="flex justify-center">
                    <div class="flex_column flex">
                      <span>视频落地页</span>
                      <a-image style="width: 160px" :src="requireImg('goods/new_video_landing.png')" />
                    </div>
                  </div>
                </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-popover>
            </div>
          </a-radio>
        </a-radio-group>
        <div v-if="data.form.wechat_landing_type == 2 && [0, 3, 5].includes(data.form.jump_type)" class="upload">
          <div class="upload">
            <div class="video_list flex">
              <div class="item flex" v-if="data.form.wechat_landing_resource">
                <FilePreview
                  :src="data.form.wechat_landing_resource"
                  style="width: 102px; height: 160px"
                  :showPlay="[0, 4, 5].includes(data.form.wechat_landing_video_status) ? false : true"
                  class="overflow-hidden"
                  :class="{ error: data.land_error }"
                ></FilePreview>
                <CloseCircleFilled size="16" color="#404040" class="del_img" @click="onDelVideo('land')" />
                <div class="item-mark flex-center" v-if="[0, 4, 5].includes(data.form.wechat_landing_video_status)">
                  <SvgIcon class="w-24px h-24px" icon="video-tips" />

                  <span class="font-size-12px c-#fff mt-16px line-height-17px">{{
                    data.form.wechat_landing_video_status == 4
                      ? '视频已下线'
                      : data.form.wechat_landing_video_status == 5
                        ? '视频已禁用'
                        : '视频已失效'
                  }}</span>
                </div>
              </div>
              <div class="upload flex_center flex_column" @click="onShowDialog('wechat_landing')" v-else>
                <SvgIcon class="w-19px h-18px" icon="video" />
                <span class="desc c-#7B808D">选择视频</span>
              </div>
              <div class="callback-tips">建议您上传大小不超过100M,建议使用90Ss以内，宽高比为9:16、清晰流畅的视频；</div>
            </div>
          </div>
        </div>
      </a-form-item>
      <a-form-item key="wx-landing—version" name="style_type">
        <template #label>
          <div class="flex_align_center">
            <span>落地页版本</span>
          </div>
        </template>
        <a-radio-group v-model:value="data.form.style_type" size="small" class="jump_type_cls" :disabled="!!item?.id">
          <a-radio :value="2" v-if="!isWeixinshop"
            >老年版<a-popover placement="right" trigger="hover" :overlayInnerStyle="{ padding: 0 }" class="ml4px">
              <template #content>
                <div>
                  <a-image :src="landPageList[0]" />
                </div>
              </template>
              <QuestionCircleFilled /> </a-popover
          ></a-radio>

          <a-radio :value="1"
            >大众版
            <a-popover placement="right" trigger="hover" width="180" :overlayInnerStyle="{ padding: 0 }">
              <template #content>
                <div>
                  <a-image :src="landPageList[1]" />
                </div>
              </template>
              <QuestionCircleFilled /> </a-popover
          ></a-radio>

          <a-radio :value="3" v-if="!isWeixinshop"
            >淘宝版
            <a-popover placement="right" trigger="hover" width="180" :overlayInnerStyle="{ padding: 0 }">
              <template #content>
                <div>
                  <a-image :src="landPageList[9]" />
                </div>
              </template>
              <QuestionCircleFilled /> </a-popover
          ></a-radio>

          <a-radio :value="4" v-if="!isWeixinshop"
            >拼多多版
            <a-popover placement="right" trigger="hover" width="180" :overlayInnerStyle="{ padding: 0 }">
              <template #content>
                <div>
                  <a-image :src="landPageList[10]" />
                </div>
              </template>
              <QuestionCircleFilled /> </a-popover
          ></a-radio>
        </a-radio-group>
      </a-form-item>
      <!--   哔哩哔哩 磁力   start      -->
      <a-form-item label="落地页：" v-if="[5].includes(data.form.platform_id)" required>
        <div>
          <a-tag v-if="landing_page_item_data" closable color="#108ee9" @close="closeItem">{{
            landing_page_item_data.name
          }}</a-tag>
          <a-button v-else type="link" @click="onShowDialog('LandingpageCom')">选择落地页</a-button>
          <a-button v-if="landing_page_item_data" type="link" @click="onShowDialog('LandingpageCom')">编辑</a-button>
        </div>
      </a-form-item>
      <!--   哔哩哔哩  磁力 end     -->

      <a-form-item label="优惠券：">
        <div class="coupon">
          <span class="title">
            <span class="switch_name">新客优惠券</span>
            <a-switch v-model:checked="data.form.full_status" :checkedValue="1" :unCheckedValue="0" />
          </span>
          <div v-if="data.form.full_status">
            <a-form-item label="" label-width="0" name="full_coupon_ids">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                v-model:value="data.form.full_coupon_ids"
                filterable
                show-search
                clearable
                :filter-option="handleCouponFilterOption"
                placeholder="请选择新客优惠券"
                option-label-prop="label"
              >
                <a-select-option
                  v-for="v in data.fullList"
                  :key="v.id"
                  :value="v.id"
                  :disabled="v.disabled"
                  :label="v.name"
                >
                  <div>
                    <div class="flex justify-between">
                      <div class="c-#212121 font-size-14px font-600">{{ v.name }}</div>
                      <a-button @click.stop="v.show = !v.show" type="link" class="font-size-12px h-auto!"
                        >{{ v.show ? '收起规则' : '展开规则' }}
                        <SvgIcon
                          class="ml-3px mb--2px font-size-12px transition-transform duration-200"
                          :class="{ 'rotate-180': !v.show }"
                          icon="arrow"
                        />
                      </a-button>
                    </div>
                    <div class="youxiaoqi" v-if="v.show">
                      <div v-for="item in v.rules.split('#')">{{ item }}</div>
                      <div class="youxiaoqi">领券时间：{{ v.receive_start_time }}至{{ v.receive_end_time }}</div>
                    </div>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
            <span class="link_btn">
              <div class="youxiaoqi" v-if="full_rule">
                <div v-for="item in full_rule?.rules?.split('#') || []">{{ item }}</div>
                <div>领券时间：{{ full_rule.receive_start_time }}-{{ full_rule.receive_end_time }}</div>
              </div>
              不可选择减免金额大于商品单价的优惠券，可在店铺营销-优惠券中 创建新优惠券，
              <span class="btn" @click="onJump">去创建</span>
            </span>
          </div>
        </div>
        <div class="coupon">
          <span class="title">
            <span class="switch_name">回流优惠券</span>
            <a-switch v-model:checked="data.form.backflow_status" :checkedValue="1" :unCheckedValue="0" />
          </span>
          <div v-if="data.form.backflow_status">
            <a-form-item label="" label-width="0" name="back_coupon_ids">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                v-model:value="data.form.back_coupon_ids"
                filterable
                show-search
                clearable
                placeholder="请选择回流优惠券"
                option-label-prop="label"
                :filter-option="handleCouponFilterOption"
              >
                <a-select-option
                  v-for="v in data.backList"
                  :key="v.id"
                  :value="v.id"
                  :name="v.name"
                  :label="v.name"
                  :disabled="v.disabled"
                >
                  <div>
                    <div class="flex justify-between">
                      <div class="c-#212121 font-size-14px font-600">{{ v.name }}</div>
                      <a-button @click.stop="v.show = !v.show" type="link" class="font-size-12px h-auto!"
                        >{{ v.show ? '收起规则' : '展开规则' }}
                        <SvgIcon
                          class="ml-3px mb--2px font-size-12px transition-transform duration-200"
                          :class="{ 'rotate-180': !v.show }"
                          icon="arrow"
                        />
                      </a-button>
                    </div>
                    <div class="youxiaoqi" v-if="v.show">
                      <div v-for="item in v.rules.split('#')">{{ item }}</div>
                      <div class="youxiaoqi">领券时间：{{ v.receive_start_time }}至{{ v.receive_end_time }}</div>
                    </div>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
            <span class="link_btn"> *注：腾讯广告视频号版位不支持设置回流券，建议非视频号版位设置。</span>
            <span class="link_btn">
              <div class="youxiaoqi" v-if="back_rule">
                <div v-for="item in back_rule?.rules?.split('#') || []">{{ item }}</div>
                <div>领券时间：{{ back_rule.receive_start_time }}-{{ back_rule.receive_end_time }}</div>
              </div>
              不可选择减免金额大于商品单价的优惠券，可在店铺营销-优惠券中 创建新优惠券，
              <span class="btn" @click="onJump">去创建</span>
            </span>
          </div>
        </div>
        <div class="coupon" v-if="!isWeixinshop">
          <span class="title">
            <span class="switch_name m-r-8px">支付失败优惠券</span>
            <a-switch v-model:checked="data.form.stay_status" :checkedValue="1" :unCheckedValue="0" />
          </span>
          <div v-if="data.form.stay_status">
            <a-form-item label="" label-width="0" name="stay_coupon_ids">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                v-model:value="data.form.stay_coupon_ids"
                filterable
                show-search
                clearable
                placeholder="请选择支付失败优惠券"
                option-label-prop="label"
                :filter-option="handleCouponFilterOption"
              >
                <a-select-option
                  v-for="v in data.stayList"
                  :key="v.id"
                  :value="v.id"
                  :name="v.name"
                  :disabled="v.disabled"
                  :label="v.name"
                >
                  <div>
                    <div class="flex justify-between">
                      <div class="c-#212121 font-size-14px font-600">{{ v.name }}</div>
                      <a-button @click.stop="v.show = !v.show" type="link" class="font-size-12px h-auto!"
                        >{{ v.show ? '收起规则' : '展开规则' }}
                        <SvgIcon
                          class="ml-3px mb--2px font-size-12px transition-transform duration-200"
                          :class="{ 'rotate-180': !v.show }"
                          icon="arrow"
                        />
                      </a-button>
                    </div>
                    <div class="youxiaoqi" v-if="v.show">
                      <div v-for="item in v.rules.split('#')">{{ item }}</div>
                    </div>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
            <span class="link_btn">
              <div class="youxiaoqi" v-if="stay_rule">
                <div v-for="item in stay_rule?.rules?.split('#') || []">{{ item }}</div>
              </div>
              不可选择减免金额大于商品单价的优惠券，可在店铺营销-优惠券中 创建新优惠券，
              <span class="btn" @click="onJump">去创建</span>
            </span>
          </div>
        </div>
        <div class="coupon" v-if="!isWeixinshop">
          <span class="title">
            <span class="switch_name m-r-8px">商品浏览优惠券</span>
            <a-switch v-model:checked="data.form.view_coupon_status" :checkedValue="1" :unCheckedValue="0" />
          </span>
          <div v-if="data.form.view_coupon_status">
            <a-form-item label="" label-width="0" name="view_coupon_ids">
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                v-model:value="data.form.view_coupon_ids"
                filterable
                show-search
                clearable
                :filter-option="handleCouponFilterOption"
                option-label-prop="label"
                placeholder="请选择商品浏览优惠券"
              >
                <a-select-option
                  v-for="v in data.browseList"
                  :key="v.id"
                  :value="v.id"
                  :name="v.name"
                  :disabled="v.disabled"
                  :label="v.name"
                >
                  <div>
                    <div class="flex justify-between">
                      <div class="c-#212121 font-size-14px font-600">{{ v.name }}</div>
                      <a-button @click.stop="v.show = !v.show" type="link" class="font-size-12px h-auto!"
                        >{{ v.show ? '收起规则' : '展开规则' }}
                        <SvgIcon
                          class="ml-3px mb--2px font-size-12px transition-transform duration-200"
                          :class="{ 'rotate-180': !v.show }"
                          icon="arrow"
                        />
                      </a-button>
                    </div>
                    <div class="youxiaoqi" v-if="v.show">
                      <div v-for="item in v.rules.split('#')">{{ item }}</div>
                      <div class="youxiaoqi">领券时间：{{ v.receive_start_time }}至{{ v.receive_end_time }}</div>
                    </div>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
            <span class="link_btn">
              <div class="youxiaoqi" v-if="browse_rule">
                <div v-for="item in browse_rule?.rules?.split('#') || []">{{ item }}</div>
                <div>领券时间：{{ browse_rule.receive_start_time }}-{{ browse_rule.receive_end_time }}</div>
              </div>
              不可选择减免金额大于商品单价的优惠券，可在店铺营销-优惠券中 创建新优惠券，
              <span class="btn" @click="onJump">去创建</span>
            </span>
          </div>
        </div>
      </a-form-item>
      <!-- 跨境商品不支持营销活动 -->
      <a-form-item label="营销活动" v-if="props.goodsDetail.type != 2 && !isWeixinshop">
        <a-form-item-rest>
          <a-switch v-model:checked="data.form.active_status" :checkedValue="1" :unCheckedValue="2" />
        </a-form-item-rest>
        <template v-if="data.form.active_status == 1">
          <div class="mt8px">
            <a-form-item-rest>
              <a-radio-group v-model:value="data.form.active_type" class="jump_type_cls" @change="chanegActivityType">
                <a-radio :value="1">
                  <a-space>
                    <span>顺手买一件</span>
                    <a-popover placement="right" trigger="hover" :overlayInnerStyle="{ padding: 0 }">
                      <template #content>
                        <div>
                          <a-image :src="landPageList[2]" />
                        </div>
                      </template>
                      <QuestionCircleFilled />
                    </a-popover>
                  </a-space>
                </a-radio>
                <a-radio :value="2">
                  <a-space>
                    <span>周边推荐</span>
                    <a-popover placement="right" trigger="hover" :overlayInnerStyle="{ padding: 0 }">
                      <template #content>
                        <div>
                          <a-image :src="landPageList[3]" />
                        </div>
                      </template>
                      <QuestionCircleFilled />
                    </a-popover>
                  </a-space>
                </a-radio>
              </a-radio-group>
            </a-form-item-rest>
            <a-form-item label="选择活动" :name="data.form.active_status == 1 ? 'active_id' : null">
              <a-select
                v-model:value="data.form.active_id"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                allowClear
                :field-names="{ label: 'name', value: 'id' }"
                :options="data.activityList"
                placeholder="请选择活动"
              >
              </a-select>
            </a-form-item>
          </div>
        </template>
      </a-form-item>
      <!-- <a-form-item label="商品讲解视频：" key="goods-video" v-if="data.form.open_video == 1" class="c-red">
        <div class="upload">
          <div class="video_list flex">
            <div class="item flex" v-if="data.form.video_url">
              <FilePreview
                :src="data.form.video_url"
                style="width: 102px; height: 160px"
                :showPlay="[0, 4, 5].includes(data.form.video_status) ? false : true"
                class="overflow-hidden"
                :class="{ error: data.error }"
              ></FilePreview>
              <CloseCircleFilled size="16" color="#404040" class="del_img" @click="onDelVideo('goods')" />
              <div class="item-mark flex-center" v-if="[0, 4, 5].includes(data.form.video_status)">
                <SvgIcon class="w-24px h-24px" icon="video-tips" />

                <span class="font-size-12px c-#fff mt-16px line-height-17px">{{
                  data.form.video_status == 4 ? '视频已下线' : data.form.video_status == 5 ? '视频已禁用' : '视频已失效'
                }}</span>
              </div>
            </div>
            <div class="upload flex_center flex_column" @click="onShowDialog('video')" v-else>
              <SvgIcon class="w-19px h-18px" icon="video" />
              <span class="desc c-#7B808D">选择视频</span>
            </div>
          </div>
        </div>
      </a-form-item> -->

      <a-form-item label="客服组：" name="callback_time">
        <a-radio-group v-model:value="data.form.openKefu" :disabled="isWeixinshop" @change="switchKefuGroup">
          <a-radio :value="1">关闭</a-radio>
          <a-radio :value="2">开启</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        v-if="data.form.openKefu == 2"
        name="kefu_group_id"
        :rules="[{ required: data.form.openKefu == 2, message: '请选择客服组', trigger: ['blur', 'change'] }]"
      >
        <template #label>
          <div class="flex-align">
            <span>选择客服组</span>
            <a-tooltip>
              <template #title><div class="f-flex">可在用户支付成功时，展示客服二维码进行加粉操作</div></template>
              <QuestionCircleFilled class="icon_filled" />
            </a-tooltip>
          </div>
        </template>
        <div class="flex-align" style="height: 24px; justify-content: flex-end; align-items: flex-start">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="data.form.kefu_group_id"
            placeholder="请选择客服组"
            clearable
            filterable
            @change="changeKefu('goods')"
          >
            <a-select-option v-for="v in data.kefuGroupList" :key="v.id" :value="v.id">{{ v.name }}</a-select-option>
          </a-select>
        </div>
      </a-form-item>
      <a-form-item
        v-if="data.form.kefu_group_id && data.form.openKefu == 2"
        name="kefu_template_name"
        :rules="[{ required: data.form.openKefu == 2, message: '请选择客服海报', trigger: ['blur', 'change'] }]"
      >
        <template #label>
          <div class="flex-align">
            <span>客服海报</span>
            <a-popover width="180">
              <template #title>
                <div class="f-flex">
                  <div class="flex-column f-flex">
                    <a-image
                      style="width: 150px"
                      :src="requireImg('goods/poster_tip.png')"
                      :preview-src-list="[requireImg('goods/poster_tip.png')]"
                    />
                  </div>
                </div>
              </template>
              <QuestionCircleFilled class="icon_filled" />
            </a-popover>
          </div>
        </template>
        <div class="flex-align">
          <a-button
            v-if="!data.form.kefu_template_id"
            type="link"
            class="text1 ellipsis"
            @click="onShowDialog('poster')"
            >链接到页面地址</a-button
          >
          <div v-else class="flex-align">
            <div class="lpname ellipsis">
              <div class="temp-name ellipsis">
                {{ data.form.kefu_template_name }}
              </div>
              <CloseOutlined @click="onDel('poster')" />
            </div>
            <a-button type="link" class="plmofidy" @click="onShowDialog('poster')">
              修改
              <template #icon>
                <DownOutlined />
              </template>
            </a-button>
          </div>
        </div>
      </a-form-item>
      <a-form-item label="优化目标：" name="callback_status">
        <a-select
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          v-model:value="data.form.callback_status"
          placeholder="请选择优化目标"
        >
          <a-select-option v-for="v in data.callbackStatusType" :key="v.key" :value="v.key">{{
            v.value
          }}</a-select-option>
        </a-select>
        <span class="callback-tips" v-if="[1, undefined].includes(data.form.platform_id)">
          需与ADQ广告后台配置的转化行为保持一致
        </span>
      </a-form-item>
      <a-form-item name="product_type" v-if="isWeixinshop && data.form.platform_id === 1">
        <template #label>
          <div class="flex-align">
            <span>订单归因</span>
            <a-tooltip>
              <template #title>
                若推广产品选择「商品」，请选择自主归因。若推广产品选择「微信小店商品」，请选择小店归因
              </template>
              <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
            </a-tooltip>
          </div>
        </template>
        <a-radio-group v-model:value="data.form.product_type" size="small" @change="onChangeType">
          <a-radio :value="1" v-if="props.item.product_type == 1 && !!props.item?.id"
            ><span class="c-#333">自主归因</span></a-radio
          >
          <a-radio :value="2"><span class="c-#333">小店归因</span> </a-radio>
        </a-radio-group>
      </a-form-item>
      <template v-if="!isWeixinshop && ![4, 8].includes(data.form.platform_id) && !isGDTFEEDBACKModify">
        <a-form-item
          v-if="!useInfo.is_private_platform"
          label="回传比例："
          name="callback_ratio"
          :rules="setFormCallback_ratio()"
        >
          <div class="coupon">
            <a-input-number
              class="w100%"
              v-model:value="data.form.callback_ratio"
              disabled
              :precision="0"
              placeholder="100"
              addonAfter="%"
            >
            </a-input-number>
            <span class="link_btn"> 广告订单将回传至广告侧，帮助广告侧优化广告投放群体 </span>
          </div>
        </a-form-item>
        <a-form-item
          v-else
          label="回传比例："
          name="callback_ratio"
          :rules="[
            { required: true, message: '请输入回传比例', trigger: ['change', 'blur'] },
            { pattern: /^([1]\d{2}|100|200)$/, message: '请输入转化比例100-200' }
          ]"
        >
          <div class="coupon">
            <a-input-number
              class="w100%"
              v-model:value="data.form.callback_ratio"
              placeholder="100-200"
              :precision="0"
              addonAfter="%"
            >
            </a-input-number>
            <span class="link_btn">广告订单将回传至广告侧，帮助广告侧优化广告投放群体</span>
          </div>
        </a-form-item>
      </template>
      <template v-if="[4, 6, 8].includes(data.form.platform_id) || isGDTFEEDBACKModify">
        <a-form-item
          id="callback_type"
          v-if="
            (isWeixinshop && data.form.product_type === 1) ||
            [4, 6, 8].includes(data.form.platform_id) ||
            isGDTFEEDBACKModify
          "
          label="回传单量类型"
          name="callback_type"
          required
        >
          <div class="flex">
            <div class="mr-4px mt-6px">按</div>
            <a-form-item>
              <a-select
                :getPopupContainer="(triggerNode:any) => triggerNode.parentNode"
                v-model:value="data.form.callback_type"
                :style="{ width: '160px' }"
                :options="[
                  {
                    label: '固定比例',
                    value: 1
                  },
                  {
                    label: '金额区间',
                    value: 2
                  }
                ]"
                placeholder="请选择回传单量类型"
                @change="(val:number)=>callback_handler(val, 'type')"
              ></a-select>
            </a-form-item>
            <a-form-item
              class="ml-4px"
              v-if="data.form.callback_type === 1"
              name="callback_role"
              :rules="{
                    required: true,
                    validator:(_:any,__:string,callback:Function)=>{
                      if(!data.form.callback_role && data.form.callback_role !== 0){
                        callback('请输入固定比例')
                      }
                      callback()
                    },
                    trigger:['change','blur']
                  }"
            >
              <a-input-number
                class="w-120px"
                v-model:value="data.form.callback_role"
                :precision="0"
                :min="0"
                :max="100"
                addonAfter="%"
              >
              </a-input-number>
            </a-form-item>
            <div class="ml-4px mt-6px">
              回传广告单量
              <a-tooltip>
                <template #title> 支持按比例设置广告订单的回传数量，填0%即不回传，填100%即全量回传 </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-tooltip>
            </div>
          </div>
          <div class="solid-wrapper" v-if="data.form.callback_type === 2">
            <div class="c-#85878a flex-y-center">
              <span class="c-#ff4d4f">*</span>
              区间中的广告流量订单按比例回传，不在区间中的广告流量订单不回传
            </div>
            <div class="mt-12px mb-12px c-#85878a">
              <span>金额区间</span>
              <span class="ml-234px">广告流量订单回传比例</span>
            </div>
            <div class="flex-y-center item" v-for="(item, index) in data.form.callback_role_list" :key="index">
              <a-form-item
                class="w-120px flex-none"
                :name="'callback_type' + index + '_min_price'"
                :rules="{
                      validator:(_:any,__:string,callback:Function)=>{
                        if(!item.min_price){
                          callback('请输入最小金额')
                        }
                        if(item.max_price && item.min_price && item.min_price>item.max_price){
                          callback('金额不能交叉')
                        }
                        if(item.min_price && index > 0 && item.min_price < Number(data.form?.callback_role_list?.[index - 1]?.max_price)){
                          callback('金额不能交叉')
                        }
                        callback()
                      },
                      trigger:['change','blur']
                    }"
              >
                <a-input-number
                  v-model:value="item.min_price"
                  :min="0.01"
                  placeholder="最小金额"
                  :precision="2"
                  :max="99999999"
                  addonAfter="元"
                  @blur="resetFieldCallbackList(data.form.callback_role_list.length, 'callback_type')"
                >
                </a-input-number>
              </a-form-item>
              <div class="flex-none ml-4px mr-4px mt--16px c-#85878a">-</div>
              <a-form-item
                class="w-120px flex-none"
                :name="'callback_type' + index + '_max_price'"
                :rules="{
                      validator:(_:any,__:string,callback:Function)=>{
                        if(!item.max_price){
                          callback('请输入最大金额')
                        }
                        if(item.max_price && item.min_price && item.max_price <= item.min_price){
                          callback('金额不能重复或交叉2')
                        }
                        let nextMin = index + 1 <= (data.form.callback_role_list || [])?.length ? data.form.callback_role_list?.[index + 1]?.min_price : undefined
                        if(nextMin && item.max_price && nextMin < Number(item.max_price)){
                          callback('金额不能重复或交叉')
                        }
                        callback()
                      },
                      trigger:['blur','change']
                    }"
              >
                <a-input-number
                  :min="0.01"
                  v-model:value="item.max_price"
                  placeholder="最大金额"
                  :precision="2"
                  :max="99999999"
                  @blur="resetFieldCallbackList(data.form.callback_role_list.length, 'callback_type')"
                  addonAfter="元"
                >
                </a-input-number>
              </a-form-item>
              <div class="flex-none ml-4px mr-4px mt--16px c-#85878a">回传</div>
              <a-form-item
                class="w-160px flex-none"
                :name="'callback_type' + index + '_ration'"
                :rules="{
                      validator:(_:any,__:string,callback:Function)=>{
                        if(!item.ratio && item.ratio !== 0){
                          callback('请输入订单比例')
                        }
                        callback()
                      },
                      trigger:['change','blur']
                    }"
              >
                <a-input-number
                  v-model:value="item.ratio"
                  placeholder="填写订单比例"
                  :precision="0"
                  :min="0"
                  :max="100"
                  addonAfter="%"
                >
                </a-input-number>
              </a-form-item>
              <MinusCircleOutlined
                class="font-size-16px c-#85878a cursor-pointer ml-4px mt--16px"
                @click="deleteItem('type', index)"
              />
            </div>
            <a-button
              type="link"
              class="p-0! h-auto!"
              :disabled="data.form.callback_role_list && data.form.callback_role_list.length >= 10"
              @click="addItem('type')"
              >添加阶梯</a-button
            >
          </div>
        </a-form-item>
        <a-form-item
          v-if="
            (isWeixinshop && data.form.product_type === 1) ||
            [4, 8].includes(data.form.platform_id) ||
            isGDTFEEDBACKModify
          "
          id="callback_amount"
        >
          <template #label>
            <div class="flex-align">
              <span>回传金额</span>
              <a-tooltip>
                <template #title>
                  整单金额，即按用户的实付金额回传； <br />
                  单件金额，即按实付金额/件数的金额回传；<br />
                  按固定金额，即回传金额为指定金额； <br />
                  按固定比例，即回传金额为{{ data.form.platform_id === 4 ? '原价' : '实付金额' }}*比例；<br />
                  按金额区间，即实付金额落在金额区间中的订单，按所设比例回传；<br />
                  如50-100元回传100%，101-150元回传50%；
                </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-tooltip>
            </div>
          </template>
          <div class="flex">
            <span class="mr-4px mt-6px">按</span>
            <a-form-item>
              <a-select
                :getPopupContainer="(triggerNode:any) => triggerNode.parentNode"
                v-model:value="data.form.callback_amount_type"
                :style="{ width: '160px' }"
                @change="(val:number)=>callback_handler(val,'amount')"
                :options="[
                  {
                    label: '整单金额',
                    value: 1
                  },
                  {
                    label: '单件金额',
                    value: 2
                  },
                  {
                    label: '固定金额',
                    value: 3
                  },
                  {
                    label: '固定比例',
                    value: 4
                  },
                  {
                    label: '金额区间',
                    value: 5
                  }
                ]"
                placeholder="请选择回传金额类型"
              ></a-select>
            </a-form-item>
            <a-form-item
              class="ml-4px"
              v-if="data.form.callback_amount_type === 3"
              name="callback_amount_role"
              :rules="{
                    validator:(_:any,__:string,callback:Function)=>{
                      if(!data.form.callback_amount_role){
                        callback('请输入固定金额')
                      }
                      callback()
                    },
                    trigger:['change','blur']
                  }"
            >
              <a-input-number
                class="w-120px"
                v-model:value="data.form.callback_amount_role"
                :precision="2"
                :min="0.01"
                :max="99999999"
                addonAfter="元"
              >
              </a-input-number>
            </a-form-item>
            <a-form-item
              class="ml-4px"
              v-if="data.form.callback_amount_type === 4"
              name="callback_amount_role"
              :rules="{
                    validator:(_:any,__:string,callback:Function)=>{
                      if(!data.form.callback_amount_role && data.form.callback_amount_role !== 0){
                        callback('请输入固定比例')
                      }
                      callback()
                    },
                    trigger:['change','blur']
                  }"
            >
              <a-input-number
                class="w-120px"
                v-model:value="data.form.callback_amount_role"
                :precision="0"
                :min="0"
                :max="100"
                addonAfter="%"
              >
              </a-input-number>
            </a-form-item>
            <span class="ml-4px mt-6px">回传</span>
          </div>
          <div class="solid-wrapper" v-if="data.form.callback_amount_type === 5">
            <div class="mb-12px c-#85878a">
              <span>金额区间</span>
              <!-- <span class="ml-234px">回流订单回传比例</span> -->
            </div>
            <div class="flex-y-center item" v-for="(item, index) in data.form.callback_amount_role_list" :key="index">
              <a-form-item
                class="w-120px flex-none"
                :name="'callback_amount' + index + '_min_price'"
                :rules="{
                      validator:(_:any,__:string,callback:Function)=>{
                        if(!item.min_price){
                          callback('请输入最小金额')
                        }
                        if(item.max_price && item.min_price && item.min_price>item.max_price){
                          callback('金额不能交叉')
                        }
                        if(item.min_price && index > 0 && item.min_price < Number(data.form?.callback_amount_role_list?.[index - 1]?.max_price)){
                          callback('金额不能交叉')
                        }
                        callback()
                      },
                      trigger:['blur','change']
                    }"
              >
                <a-input-number
                  v-model:value="item.min_price"
                  :min="0.01"
                  placeholder="最小金额"
                  :precision="2"
                  :max="99999999"
                  addonAfter="元"
                  @blur="resetFieldCallbackList(data.form.callback_amount_role_list.length, 'callback_amount')"
                >
                </a-input-number>
              </a-form-item>
              <span class="ml-4px mr-4px mt--16px c-#85878a">-</span>
              <a-form-item
                class="w-120px flex-none"
                :name="'callback_amount' + index + '_max_price'"
                :rules="{
                      validator:(_:any,__:string,callback:Function)=>{
                        if(!item.max_price){
                          callback('请输入最大金额')
                        }
                        if(item.max_price && item.min_price && item.max_price <= item.min_price){
                          callback('金额不能交叉')
                        }
                        let nextMin = index + 1 <= (data.form.callback_amount_role_list || [])?.length ? data.form.callback_amount_role_list?.[index + 1]?.min_price : undefined
                        if(nextMin && item.max_price && nextMin < Number(item.max_price)){
                          callback('金额不能交叉')
                        }
                        callback()
                      },
                      trigger:['blur','change']
                    }"
              >
                <a-input-number
                  :min="0.01"
                  v-model:value="item.max_price"
                  placeholder="最大金额"
                  :precision="2"
                  :max="99999999"
                  addonAfter="元"
                  @blur="resetFieldCallbackList(data.form.callback_amount_role_list.length, 'callback_amount')"
                >
                </a-input-number>
              </a-form-item>
              <span class="ml-4px mr-4px mt--16px c-#85878a">回传</span>
              <a-form-item
                class="w-160px flex-none"
                :name="'callback_amount' + index + '_ration'"
                :rules="{
                      validator:(_:any,__:string,callback:Function)=>{
                        if(!item.ratio && item.ratio !== 0){
                          callback('请输入订单比例')
                        }
                        callback()
                      },
                      trigger:['blur','change']
                    }"
              >
                <a-input-number
                  v-model:value="item.ratio"
                  placeholder="填写订单比例"
                  :precision="0"
                  :min="0"
                  :max="100"
                  addonAfter="%"
                >
                </a-input-number>
              </a-form-item>
              <MinusCircleOutlined
                class="font-size-16px c-#85878a cursor-pointer ml-4px mt--16px"
                @click="deleteItem('amount', index)"
              />
            </div>
            <a-button
              type="link"
              class="p-0! h-auto!"
              :disabled="data.form.callback_amount_role_list.length >= 10"
              @click="addItem('amount')"
              >添加阶梯</a-button
            >
          </div>
        </a-form-item>
      </template>

      <a-form-item>
        <div class="fooler_btn" v-if="type != 'detail'">
          <AButton :mr="30" @click="close">取消</AButton>
          <AButton type="primary" @click="submitForm" :loading="data.loading">保存</AButton>
        </div>
      </a-form-item>
    </a-form>
    <a-modal
      :title="data.dialog.title"
      v-model:open="data.dialog.visible"
      :width="data.dialog.width"
      :footer="null"
      centered
      destroyOnClose
    >
      <PosterList v-if="data.dialog.type === 'list'" @event="onEvent" />
      <MaterialLibrary type="video" :size="1" v-if="data.dialog.type === 'library'" @event="onEvent" />
      <LandingpageCom
        v-if="data.dialog.type === 'LandingpageCom'"
        :item="landing_page_item_data"
        @itemStatus="itemStatus"
      />
      <VideoList v-if="['video', 'wechat_landing'].includes(data.dialog.type)" @event="onEvent" />
      <addSubjectModal v-if="data.dialog.type === 'subject'" @onEvent="onEvent"></addSubjectModal>
    </a-modal>
  </div>
</template>
<script setup lang="tsx">
  import {
    cratedAd,
    setAdDmpList,
    editAd,
    callbackList,
    product_magnetism_list,
    getAdAppidListApi,
    getShopInfo,
    fetchBestApp,
    get_ActiveList,
    get_Active_info,
    subjectList as subjectListApi,
    getExamineDetailApi,
    get_product_temp_info,
    get_douyin_entity_list,
    setProductInfo
  } from '../index.api'
  import { setList, setCouponInfo, setAuthUrl, generate_auth_url, adDmpCallback, getCustomerGroup } from '../index.api'
  import { wechat_coupon_info } from '@/views/shop/shopMarketing/coupon-add-weixin/index.api'
  import { getOrglist, setAuthUrl as setDyAuthUrl } from '@/views/system/media/original-ocean/index.api'

  import { watch, reactive, ref, nextTick, computed, onMounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { getConfig, requireImg, cosSameWords } from '@/utils'
  import rules from '../src/rules'
  import datas from '../src/datas'
  import PosterList from './PosterList.vue'
  import VideoList from './VideoList.vue'
  import { message } from 'ant-design-vue'
  import {
    CloseOutlined,
    CloseCircleFilled,
    QuestionCircleFilled,
    DownOutlined,
    MinusCircleOutlined
  } from '@ant-design/icons-vue'
  import MaterialLibrary from '@/components/ui/common/MaterialLibrary/Index.vue'
  import LandingpageCom from './LandingpageCom.vue'
  import addSubjectModal from './addSubjectModal.vue'
  // import { get_html_detail } from '@/views/canvas/index.api'
  import { isString, throttle, cloneDeep } from 'lodash-es'
  const router = useRouter()
  const route = useRoute()
  import { useApp } from '@/hooks'
  const { useInfo, shopInfo } = useApp()

  const ruleForm = ref()
  // 导入form验证规则
  const { adRules } = rules()
  // 导入广告平台、回传行为
  const { platformType } = datas()

  const props = defineProps({
    goodsDetail: {
      type: Object,
      default: () => {}
    },
    item: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    }
  })
  const landPageList = [
    requireImg('goods/new_land-old.png'),
    requireImg('goods/new_land-normal.png'),
    requireImg('market/just-buy-one.png'),
    requireImg('market/rim.png'),

    requireImg('order/ad_bd_title.png'),
    requireImg('order/ad_bd_hover.png'),
    requireImg('order/ad_bd_bottom.png'),
    requireImg('order/ad_bd_top.png'),
    requireImg('order/ad_bd_hide.png'),
    requireImg('goods/tb-style.png'),
    requireImg('goods/pdd-style.png')
  ]
  const emit = defineEmits(['event'])
  const skip_text = !useInfo.is_private_platform
    ? '小程序：点击广告直接唤起小程序'
    : '小程序：点击广告直接唤起小程序;H5跳小程序：点击广告唤起H5页面，下单跳转到小程序'

  interface CallbackListItem {
    max_price: number | undefined
    min_price: number | undefined
    ratio: number | undefined
  }
  const az = (item, options) => {
    console.log('item', item, options)
  }
  const data = reactive({
    isBackFlowWeixinShopValid: false,
    weixinShopProductTitle: '', // 记录微信小店商品标题
    loading: false,
    form: {
      ad_account_id: null,
      active_status: 2,
      active_type: 1,
      active_id: undefined,
      coupent: ''
    } as any,
    platformList: platformType,
    landing_page_switch: 0, // 0 关闭 1开启
    backList: [],
    appletList: [],
    stayList: [],
    fullList: [],
    browseList: [],
    adList: [], // 账户ID
    activityList: [], //营销活动
    dialog: {
      id: '',
      title: '',
      visible: false,
      width: '',
      type: ''
    },
    error: false,
    land_error: false,
    kefuGroupList: [],
    callbackStatusType: [] as any, //回传行为
    subjectList: [] //主体信息列表
  })
  const full_rule = computed(() => {
    if (!data.fullList.length) return ''
    return data.fullList.find((item) => data.form.full_coupon_ids == item.id)
  })
  const back_rule = computed(() => {
    if (!data.backList.length) return ''
    return data.backList.find((item) => data.form.back_coupon_ids == item.id)
  })
  const stay_rule = computed(() => {
    if (!data.stayList.length) return ''
    return data.stayList.find((item) => data.form.stay_coupon_ids == item.id)
  })
  const browse_rule = computed(() => {
    if (!data.browseList.length) return ''
    return data.browseList.find((item) => data.form.view_coupon_ids == item.id)
  })
  const close = () => {
    emit('event', { cmd: 'close' })
  }
  // 主体信息列表
  const getSubjectList = async () => {
    try {
      let res: any = await subjectListApi({ page: 1, page_size: 9999999 })
      data.subjectList =
        res.data.list.map((it) => {
          return {
            ...it,
            label: it.name,
            value: it.id
          }
        }) || []
      console.log('data.subjectList', data.subjectList)
    } catch (e) {
      console.log(e)
    }
  }

  const handleSubjectFilterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }
  // 优惠券跳转
  const onJump = () => {
    const url = router.resolve({
      path: '/mall/shop_marketing/coupon'
    })
    window.open(url.href)
  }
  const changeLandingType = (event) => {
    if (event.target.value == 1) {
      data.form.wechat_landing_type = 1
      data.form.platform_id = 1
      data.platformList = [{ value: 1, label: '广点通' }]
      getCallbackList(event.target.value)
    } else {
      data.platformList = platformType
    }
  }
  const changePlatform = (event: any) => {
    data.form.wechat_landing_type = 1
    if (event.target.value == 1) {
      data.form.callback_ratio = 100
      if (data.form.ad_account_id) data.form.ad_account_id = null
      data.form.jump_type = 0
    } else if (event.target.value == 6) {
      data.form.jump_type = 3
      data.form.product_type = 1
      getDouyinList()
      getAdAppidList()
    } else {
      getSubjectList()
      data.form.jump_type = 3
    }
    getCallbackList(event.target.value)
    getBestApp(event.target.value)

    // getAdAppidList()
  }
  // 获取小程序id
  const getAdAppidList = async () => {
    try {
      let res = await getAdAppidListApi({
        product_id: props.goodsDetail.id,
        platform_id: [4, 6].includes(data.form.platform_id) ? data.form.platform_id : undefined
      })
      let arr: Array<any> = res.data || []

      // 磁力
      if (data.form.platform_id == 4) {
        let ciArr: any[] = []
        const bindApp = [
          'wx515dd0bd8e6b724a',
          'wx91001073963f1abd',
          'wxde37c4a6d2171abf',
          'wx0cc93f0d9160f0a1',
          'wxa894e7078fecd792'
        ]
        arr.forEach((v) => {
          if (['dev', 'test'].includes(getConfig('NODE_ENV')) || bindApp.includes(v.app_id)) {
            ciArr.push(v)
          }
        })
        if (ciArr.length) {
          data.appletList = ciArr
          data.form.wechat_app_name = ciArr[0].app_id
        } else {
          data.appletList = []
          message.warning('商户号请绑定小程序后再进行创建')
          data.form.wechat_app_name = ''
        }
      } else {
        data.appletList = arr
        data.form.wechat_app_name = data.cache_wechat_app_name
      }
    } catch (error) {
      console.error(error)
    }
  }

  const appNameChange = (val: string, options: any) => {
    data.form.wechat_app_id = val
    data.form.wechat_app_name = options.app_name
  }

  const getDouyinList = async () => {
    const res = await get_douyin_entity_list({})
    if (res.code === 0) {
      data.subjectList =
        res.data.tkMiniprogramEntitys.map((it) => {
          return {
            ...it,
            label: it.name,
            value: it.id
          }
        }) || []
      if (data.subjectList.length == 1) {
        data.form.ad_entity_id = data.subjectList[0].id
      }
      return
    } else {
      console.error('获取音列表失败', res.msg)
    }
  }

  // 广告授权
  const onAuth = async () => {
    try {
      let resp = null
      if (data.form.platform_id === 4) {
        resp = await generate_auth_url()
      } else if (data.form.platform_id === 6) {
        resp = await setDyAuthUrl()
      } else {
        resp = await setAuthUrl()
      }
      window.open(resp.data.url, '_blank')
    } catch (error) {
      console.error(error)
    }
  }

  //获取回传行为
  const getCallbackList = async (platform_id: Number) => {
    try {
      if (data.form.platform_id == 1) {
        let res = await callbackList({ platform_id })
        data.callbackStatusType = data.form.platform_id == 1 ? res.data : [{ key: '14', value: '订单提交' }]
      } else if (data.form.platform_id == 6) {
        data.callbackStatusType = [
          { key: 'in_app_order', value: '下单' },
          { key: 'active_pay', value: '购买' }
        ]
      } else if (data.form.platform_id == 8) {
        data.callbackStatusType = [
          { key: '3', value: '页面浏览' },
          { key: '77', value: '付费金额页面' },
          { key: '1000', value: '付费' }
        ]
      } else {
        data.callbackStatusType = [{ key: '14', value: '订单提交' }]
      }
      if (!props.item?.id) {
        data.form.callback_status =
          data.form.platform_id == 1
            ? 'COMPLETE_ORDER'
            : data.form.platform_id == 6
              ? 'in_app_order'
              : data.form.platform_id == 8
                ? '3'
                : '14'
      }
    } catch (error) {
      console.error(error)
    }
  }
  // 获取最优小程序
  const getBestApp = async (type) => {
    try {
      let res = await fetchBestApp({ product_id: props.goodsDetail?.id, platform_id: type == 4 ? 4 : undefined })
      if (!res.data) {
        data.form.wechat_app_name = ''
        data.wechat_app_id = ''
        data.cache_wechat_app_name = ''
        return message.warning('暂无可用小程序')
      }
      data.form.wechat_app_name = res.data.app_name || ''
      data.wechat_app_id = res.data.app_id || ''
      data.cache_wechat_app_name = res.data.app_name || ''
    } catch (error) {
      console.error(error)
    }
  }
  // 设置回传比例
  const setFormCallback_ratio = () => {
    if (data.form.platform_id === 1) {
      return [
        { required: true, message: '请输入回传比例', trigger: ['change', 'blur'] },
        { pattern: /^([1]\d{2}|100|200)$/, message: '请输入转化比例100-200' }
      ]
    } else if (data.form.platform_id === 5) {
      return [
        { required: true, message: '请输入回传比例', trigger: ['change', 'blur'] },
        { pattern: /^(?:200|[0-1]?[0-9]{1,2})$/, message: '请输入转化比例0-200' }
      ]
    } else {
      ;[
        { required: true, message: '请输入回传比例', trigger: ['change', 'blur'] },
        { pattern: /^(0|[1-9][0-9]?|100)$/, message: '请输入转化比例0-100' }
      ]
    }
  }
  // 提交
  const submitForm = async () => {
    console.log(data.form, paramsFunc())
    // formName
    try {
      if (!(await ruleForm.value.validate())) return
      if (data.form.wechat_landing_type == 1) data.form.wechat_landing_resource = null
      if (data.form.wechat_landing_type == 2 && !data.form.wechat_landing_resource) return message.warning('请上传视频')
      if (data.form.openKefu == 2 && !data.form.kefu_group_id) return message.warning('请选择客服组')
      if (data.landing_page_switch == 0 && data.form.jump_type == 1) {
        return message.warning('暂无权限')
      }
      if (
        data.form.wechat_landing_resource &&
        [0, 4, 5].includes(data.form.wechat_landing_video_status) &&
        data.form.wechat_landing_type == 2
      ) {
        const video_type =
          data.form.wechat_landing_video_status == 4
            ? '被下线'
            : data.form.wechat_landing_video_status == 5
              ? '被禁用'
              : '失效'
        data.land_error = true
        return message.warning(`所选视频已被${video_type}，请重新选择`)
      }
      if (data.form.video_url && [0, 4, 5].includes(data.form.video_status)) {
        const video_type = data.form.video_status == 4 ? '被下线' : data.form.video_status == 5 ? '被禁用' : '失效'
        data.error = true
        return message.warning(`该视频已${video_type}，请更换视频`)
      }
      data.form.id ? update() : save()
    } catch (error) {
      console.log(error)
    }
  }
  // 隐藏按钮下，点击图片打开小程序默认勾选且禁用
  const buttonBtnChange = (val: any) => {
    if (val.target.value === 4) {
      data.form.web_center.is_click = true
    }
  }
  const paramsFunc = () => {
    let params = {
      name: data.form.name || '',
      product_id: data.form.product_id || '',
      platform_id: data.form.platform_id || '',
      ad_account_id: data.form.ad_account_id ? +data.form.ad_account_id : undefined,
      backflow_status: data.form.backflow_status || 0,
      back_coupon_ids: data.form.backflow_status == 1 ? data.form.back_coupon_ids + '' : '',
      view_coupon_status: data.form.view_coupon_status || 0,
      view_coupon_ids: data.form.view_coupon_status == 1 ? data.form.view_coupon_ids + '' : '',
      full_status: data.form.full_status || 0,
      full_coupon_ids: data.form.full_status == 1 ? data.form.full_coupon_ids + '' : '',
      stay_status: data.form.stay_status || 0,
      stay_coupon_ids: data.form.stay_status == 1 ? data.form.stay_coupon_ids + '' : '',
      callback_status: data.form.callback_status || '',
      callback_time: data.form.callback_time || 1,
      // callback_ratio: data.form.callback_ratio ? Number(data.form.callback_ratio) : '',
      wechat_landing_type: data.form.wechat_landing_type || 1,
      wechat_landing_resource: data.form.wechat_landing_resource || '',
      callback_ratio: data.form.callback_ratio ? Number(data.form.callback_ratio) : '',
      // callback_ratio: 100,
      useShop: 'shop_id',
      product_type: data.form.platform_id == 4 ? 1 : data.form.product_type,
      kefu_group_id: data.form.openKefu == 2 ? data.form.kefu_group_id : 0,
      kefu_template_id: data.form.openKefu == 2 ? data.form.kefu_template_id : 0,
      kefu_template_name: data.form.openKefu == 2 ? data.form.kefu_template_name : '',
      jump_type: data.form.jump_type || 0,
      wechat_app_name: data.form.wechat_app_name || '',
      wechat_app_id: data.form.wechat_app_id || '',
      style_type: data.form.style_type,
      landing_page_id: landing_page_item_data.value?.id || 0,
      video_id: data.form.video_id,
      wechat_landing_video_id: data.form.wechat_landing_type == 2 ? data.form.wechat_landing_video_id : 0,
      active_status: data.form.active_status,
      active_type: data.form.active_type,
      ad_entity_id: data.form.ad_entity_id || undefined,
      active_id: data.form.active_id || undefined
    }
    if (([4, 8].includes(data.form.platform_id) && data.form.jump_type === 3) || data.form.platform_id === 6) {
      params.web_center = {
        ...data.form.web_center
      }
      params.shop_entity_id = data.form.shop_entity_id
      params.ad_entity_id = undefined
      params.web_center.content = data.form.web_center?.image?.[0]?.url
      delete params.web_center.image
      if (data.form.web_center.button_style === 4) {
        params.web_center.button = undefined
      }
    }
    let newParams = {} as any
    if (isWeixinshop.value || [4, 8].includes(data.form.platform_id) || isGDTFEEDBACKModify.value) {
      newParams = {
        callback_amount_type: data.form.callback_amount_type,
        callback_type: data.form.callback_type
      }
      if (data.form.callback_amount_type === 1 || data.form.callback_amount_type === 2) {
        newParams.callback_amount_role = undefined
      } else if (data.form.callback_amount_type === 3) {
        newParams.callback_amount_role = data.form?.callback_amount_role
          ? data.form?.callback_amount_role + ''
          : undefined
      } else if (data.form.callback_amount_type === 4) {
        newParams.callback_amount_role = data.form.callback_amount_role + ''
      } else {
        newParams.callback_amount_role = JSON.stringify(data.form.callback_amount_role_list)
      }

      if (isWeixinshop.value) {
        if (params.product_type === 1) {
          if (data.form.callback_type === 1) {
            newParams.callback_role = undefined
            newParams.callback_ratio =
              data.form.platform_id == 1 && data.form?.product_type == 2 ? 100 : data.form.callback_role
          } else {
            newParams.callback_role = data.form.callback_role_list
          }
        } else {
          newParams.callback_role = undefined
          newParams.callback_ratio = data.form.callback_role
          newParams.callback_type = 1
          newParams.callback_amount_role = undefined
          newParams.callback_amount_type = 1
        }
      } else {
        if (data.form.callback_type === 1) {
          newParams.callback_role = undefined
          newParams.callback_ratio = data.form.callback_role
        } else {
          newParams.callback_role = data.form.callback_role_list
        }
      }
    }

    return { ...params, ...newParams }
  }
  const onChangeType = () => {
    if (data.form.callback_type === 1) {
      data.form.callback_role = data.form.callback_role || 100
      data.form.callback_role_list = []
    } else {
      data.form.callback_role = undefined
      if (!data.form.callback_role_list?.length) {
        let item: CallbackListItem = { min_price: undefined, max_price: undefined, ratio: undefined }
        data.form.callback_role_list.push(item)
      }
    }
  }
  // 金额区间change
  const callback_handler = (val: number, type: string) => {
    if (type === 'type') {
      if (val === 1) {
        data.form.callback_role = 100
        data.form.callback_role_list = []
      } else {
        data.form.callback_role = undefined
        if (!data.form.callback_role_list?.length) {
          let item: CallbackListItem = { min_price: undefined, max_price: undefined, ratio: undefined }
          data.form.callback_role_list?.push(item)
        }
      }
      return
    }
    if (type === 'amount') {
      if (val === 5) {
        data.form.callback_amount_role = undefined
        if (!data.form.callback_amount_role_list?.length) {
          let item: CallbackListItem = { min_price: undefined, max_price: undefined, ratio: undefined }
          data.form.callback_amount_role_list?.push(item)
        }
      } else {
        data.form.callback_amount_role = undefined
        data.form.callback_amount_role_list = []
      }

      return
    }
  }
  // 价格区间校验
  const resetFieldCallbackList = (len: number, type: string) => {
    let callback_type_arr = []
    let callback_amount_arr = []
    if (type === 'callback_type') {
      for (let i = 0; i < len; i++) {
        callback_type_arr.push('callback_type' + i + '_min_price')
        callback_type_arr.push('callback_type' + i + '_max_price')
      }
      ruleForm?.value?.validateFields(callback_type_arr)
    } else {
      for (let i = 0; i < len; i++) {
        callback_amount_arr.push('callback_amount' + i + '_min_price')
        callback_amount_arr.push('callback_amount' + i + '_max_price')
      }
      ruleForm.value.validateFields(callback_amount_arr)
    }
  }
  // 添加
  const addItem = (type: string) => {
    let list = type === 'type' ? data.form.callback_role_list : data.form.callback_amount_role_list
    if (list?.some((it) => !it.min_price || !it.max_price || (!it.ratio && it.ratio !== 0)))
      return message.warning('请先填写完金额区间及回传比例')
    let item: CallbackListItem = { min_price: undefined, max_price: undefined, ratio: undefined }
    list?.push(item)
  }
  // 删除
  const deleteItem = (type: string, index: number) => {
    let list = type === 'type' ? data.form.callback_role_list : data.form.callback_amount_role_list
    if ((list || [])?.length <= 1) return message.warning('至少有一条信息')
    list?.splice(index, 1)
  }
  // 添加
  const save = async () => {
    try {
      data.loading = true
      let params = paramsFunc()
      if ([5].includes(params.platform_id) && !params.landing_page_id) {
        data.loading = false
        message.warning('请选择落地页')
        return
      }
      params.wechat_app_id = data.wechat_app_id
      const resp = await cratedAd(params)
      ruleForm.value.resetFields()
      emit('event', { cmd: 'edit' })
      message.success('操作成功')
      data.loading = false
    } catch (error) {
      console.error(error)
      data.loading = false
    }
  }
  // 更新
  const update = async () => {
    try {
      data.loading = true
      let params = paramsFunc()
      const resp = await editAd({ ...params, id: data.form.id })
      ruleForm.value.resetFields()
      emit('event', { cmd: 'edit' })
      message.success('操作成功')
      data.loading = false
    } catch (error) {
      console.error(error)
      data.loading = false
    }
  }
  const onDel = (type) => {
    switch (type) {
      case 'poster':
        data.form.kefu_template_id = null
        data.form.kefu_template_name = null
        break
    }
  }
  // 回流优惠券change事件
  const backFlowChange = async (val: any, options: any) => {
    console.log('val', val, options)
    if (!isWeixinshop.value) return
    try {
      let currentItem: any = data.backList.find((item) => item.id == val)
      if (currentItem && currentItem?.store_coupon_ids?.length) {
        let promise: any = []
        currentItem?.store_coupon_ids?.forEach((item: any) => {
          promise.push(wechat_coupon_info({ id: item }))
        })
        let res = await Promise.all(promise)
        let flag = false
        for (let i = 0; i < res.length; i++) {
          let item = res[i].data?.product?.title
          if (cosSameWords(data.weixinShopProductTitle, item) < 90) {
            flag = true
            break
          }
        }
        if (flag) {
          message.warning(`请保持商品一致`)
          data.isBackFlowWeixinShopValid = true
        } else {
          data.isBackFlowWeixinShopValid = false
        }
        ruleForm.value.validateFields(['back_coupon_ids'])
      }
    } catch (error) {
      console.error(error)
      data.isBackFlowWeixinShopValid = false
      ruleForm.value.validateFields(['back_coupon_ids'])
    }
  }
  // 获取优惠券列表数据
  const getList = async () => {
    try {
      let params = {
        page: 1,
        page_size: 100,
        useShop: 'shop_id',
        sence: 1,
        product_id: props.goodsDetail?.id
      }
      // 回流优惠券
      let backResp = await setList({ ...params, type: 2, business: props.goodsDetail.type == 4 ? 2 : 1 })
      // 支付失败优惠券
      let stayResp = await setList({ ...params, type: 3 })
      // 满减券
      let fullResp = await setList({ ...params, type: 1, business: props.goodsDetail.type == 4 ? 2 : 1 })
      //浏览券
      let browseResp = await setList({ ...params, type: 5 })
      //营销活动
      let justResp = await get_ActiveList({ page: 1, page_size: 999, status: 32, type: data.form.active_type })
      data.backList = backResp.data?.list || []
      data.stayList = stayResp.data?.list || []
      data.fullList = fullResp.data?.list || []
      data.browseList = browseResp.data?.list || []
      data.activityList = justResp.data?.list || []
      if (data.form.id) {
        if (!data.backList.some((v) => v.id == data.form.back_coupon_ids) && data.form.back_coupon_ids) {
          let res = await setCouponInfo({ id: data.form.back_coupon_ids })
          data.backList.push({ id: res.data.id, name: `${res.data.name}(${res.data.status})`, disabled: true })
        }
        if (!data.fullList.some((v) => v.id == data.form.full_coupon_ids) && data.form.full_coupon_ids) {
          let res = await setCouponInfo({ id: data.form.full_coupon_ids })
          data.fullList.push({ id: res.data.id, name: `${res.data.name}(${res.data.status})`, disabled: true })
        }
        if (!data.browseList.some((v) => v.id == data.form.view_coupon_ids) && data.form.view_coupon_ids) {
          let res = await setCouponInfo({ id: data.form.view_coupon_ids })
          data.browseList.push({ id: res.data.id, name: `${res.data.name}(${res.data.status})`, disabled: true })
        }
        if (!data.stayList.some((v) => v.id == data.form.stay_coupon_ids) && data.form.stay_coupon_ids) {
          let res = await setCouponInfo({ id: data.form.stay_coupon_ids })
          data.stayList.push({ id: res.data.id, name: `${res.data.name}(${res.data.status})`, disabled: true })
        }

        if (!data.activityList.some((v) => v.id == data.form.active_id) && data.form.active_id) {
          let res = await get_Active_info({ id: data.form.active_id })
          data.activityList.push({
            id: res.data.tkActive.id,
            name: `${res.data.tkActive.name}(${res.data.tkActive.status_txt})`,
            disabled: true
          })
        }
      }
    } catch (error) {
      console.error(error)
    }
  }
  // getList()

  // 获取广告列表
  const getAdDmpList = async (val) => {
    try {
      let keyword = val && isString(val) ? val : undefined
      let resp = null
      if (data.form.platform_id === 4) {
        resp = await product_magnetism_list({ page: 1, page_size: 2000, keyword: keyword })
        if (resp.data?.list) {
          resp.data?.list.forEach((item) => {
            item.account_name = item.advertiser_name
            item.token_account_id = item.advertiser_id + ''
          })
        }
      } else if (data.form.platform_id === 6) {
        resp = await getOrglist({
          page: 1,
          page_size: 20,
          type: 2,
          is_cid: 2,
          keyword: keyword
        })
      } else {
        resp = await setAdDmpList({
          page: 1,
          page_size: 3000,
          useShop: 'shop_id',
          keyword: keyword,
          account_role_type: 'ACCOUNT_ROLE_TYPE_ADVERTISER'
        })
      }
      data.adList = resp.data?.list || []
    } catch (error) {}
  }
  // 广告账号前端模糊搜索
  const handleAdFilterOption = (input: string, option: any) => {
    return option.account_name.toLowerCase().indexOf(input.toLowerCase()) >= 0 || option.value.indexOf(input) >= 0
  }
  // 新客搜搜索
  const handleCouponFilterOption = (input: string, option: any) => {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }

  // 获取店铺开关
  const getActiveList = async (type) => {
    try {
      const resp = await get_ActiveList({ page: 1, page_size: 999, status: 32, type: type })
      data.activityList = resp.data.list
    } catch (error) {}
  }

  const chanegActivityType = async (e) => {
    getActiveList(e.target.value)
    data.form.active_id = undefined
  }
  // 获取店铺开关
  const getShopAuth = async () => {
    try {
      const resp = await getShopInfo({})
      data.landing_page_switch = resp.data.landing_page_switch || 0
    } catch (error) {}
  }
  getShopAuth()
  const isGDTFEEDBACKModify = computed(() => {
    return data.form.platform_id === 1 && shopInfo.value?.limit_callback === 1 && !isWeixinshop.value
  })
  // 获取客服组
  const getKefuGroupList = async () => {
    try {
      const resp = await getCustomerGroup({})
      data.kefuGroupList = resp.data?.list || []
    } catch (error) {}
  }
  getKefuGroupList()
  const changeKefu = () => {
    if (!data.form.kefu_group_id) {
      data.form.kefu_template_id = null
      data.form.kefu_template_name = null
    }
  }
  const switchKefuGroup = (val) => {
    if (val == 1) {
      data.form.kefu_template_id = null
      data.form.kefu_template_name = null
      data.form.kefu_group_id = null
    }
  }
  // 海报列表
  const onShowDialog = (type) => {
    switch (type) {
      case 'library':
        data.dialog = {
          id: '',
          title: '素材库',
          visible: true,
          width: '960px',
          type: 'library'
        }
        break
      case 'poster':
        data.dialog = {
          id: '',
          title: '选择模版',
          visible: true,
          width: '932px',
          type: 'list'
        }
        break
      case 'LandingpageCom':
        data.dialog = {
          id: '',
          title: '选择兜底营销页',
          visible: true,
          width: '932px',
          type: 'LandingpageCom'
        }
        break
      case 'video':
        data.dialog = {
          id: '',
          title: '选择视频',
          visible: true,
          width: '640px',
          type: 'video'
        }
        break
      case 'wechat_landing':
        data.dialog = {
          id: '',
          title: '选择视频',
          visible: true,
          width: '640px',
          type: 'wechat_landing'
        }
        break
      case 'subject':
        data.dialog = {
          id: '',
          title: '新增主体',
          visible: true,
          width: '640px',
          type: 'subject'
        }
        break
    }
  }
  const onDelVideo = (type) => {
    if (type == 'goods') {
      data.form.video_url = null
      data.form.video_id = undefined
    } else {
      data.form.wechat_landing_resource = null
      data.form.wechat_landing_video_id = undefined
    }
  }

  const onEvent = (obj) => {
    switch (obj.cmd) {
      case 'close':
        data.dialog.visible = false
        break
      case 'material':
        data.dialog.visible = false
        if (obj.data.length) {
          data.form.wechat_landing_resource = obj.data[0].file_url
        }
        break
      case 'confirm':
        data.dialog.visible = false
        data.form.kefu_template_id = obj.posterItem.id
        data.form.kefu_template_name = obj.posterItem.name_alias
        ruleForm.value.validateFields(['kefu_template_name'])
        break
      case 'video':
        data.dialog.visible = false
        if (obj.data) {
          console.log(obj.data, 'obj.dataobj.data')
          if (data.dialog.type == 'video') {
            data.form.video_url = obj.data.video
            data.form.video_id = obj.data.id
            data.form.video_status = obj.data.status
            data.error = false
          } else {
            data.form.wechat_landing_resource = obj.data.video
            data.form.wechat_landing_video_id = obj.data.id
            data.form.wechat_landing_video_status = obj.data.status
            data.land_error = false
          }
        }
        break
      case 'subject':
        data.dialog.visible = false
        getSubjectList()
        break
    }
  }
  // 获取广告回调code
  const getadDmpCallback = async () => {
    try {
      let code = route.query.authorization_code
      if (!code) return getAdDmpList()
      await adDmpCallback({ authorization_code: code })
      router.replace({
        name: 'AdDmp'
      })
      getAdDmpList()
    } catch (error) {
      console.log(error)
      message.error('授权失败，请联系负责人')
      router.replace({
        name: 'AdDmp'
      })
    }
  }
  getadDmpCallback()
  // getAdDmpList()

  const initData = async () => {
    if (props.item?.id) {
      console.log(props.item, '编辑')
      let result = null

      data.form = {
        id: props.item.id || '',
        name: props.item.name || '',
        product_id: props.goodsDetail.id,
        platform_id: Number(props.item.platform_id) || undefined,
        ad_account_id:
          props.item.ad_account_id == 0
            ? undefined
            : props.item.platform_id == 6
              ? props.item.ad_account_id
              : props.item.ad_account_id + '',
        backflow_status: props.item.backflow_status || undefined,
        back_coupon_ids: Number(props.item.back_coupon_ids) || undefined,
        full_status: props.item.full_status || 0,
        full_coupon_ids: Number(props.item.full_coupon_ids) || undefined,
        view_coupon_status: props.item.view_coupon_status || undefined,
        view_coupon_ids: Number(props.item.view_coupon_ids) || undefined,
        stay_status: props.item.stay_status || 0,
        stay_coupon_ids: Number(props.item.stay_coupon_ids) || undefined,
        callback_status: props.item.callback_status || undefined,
        callback_time: props.item.callback_time || 1,
        callback_ratio: props.item.callback_ratio || '',
        openKefu: props.item.kefu_group_id ? 2 : 1,
        kefu_group_id: props.item.kefu_group_id || '',
        kefu_template_id: props.item.kefu_template_id || 0,
        kefu_template_name: props.item.kefu_template_name || '',
        wechat_landing_type: props.item.wechat_landing_type || 1,
        wechat_landing_resource: props.item.wechat_landing_resource || '',
        wechat_landing_video_status: props.item.wechat_landing_video_status || 0,
        wechat_landing_video_id: props.item.wechat_landing_video_id || 0,
        jump_type: props.item.jump_type || 0,
        wechat_app_name: props.item.wechat_app_name || '',
        style_type: props.item.style_type || 1,
        wechat_app_id: props.item.wechat_app_id || undefined,
        video_status: props.item.video_status || 0,
        open_video: shopInfo.value.open_video,
        video_url: props.item.video_url || '',
        video_id: props.item.video_id || 0,
        active_status: props.item.active_status,
        active_type: props.item.active_type || 1,
        ad_entity_id: props.item.ad_entity_id || undefined,
        active_id: props.item.active_id || undefined,

        product_type: props.item.product_type || 1
      }
      // 磁力二跳
      if (([4, 8].includes(data.form.platform_id) && props.item.jump_type === 3) || props.item.platform_id === 6) {
        data.form.web_center = {
          ...props.item.web_center
        }
        data.form.shop_entity_id = props.item.shop_entity_id
        data.form.web_center.image = props.item.web_center.content ? [{ url: props.item.web_center.content }] : []
      }
      if (data.form.platform_id === 4) {
        getSubjectList()
      }
      if (data.form.platform_id === 6) {
        getDouyinList()
      }
      // 处理底部的回传
      let res = props.item
      data.form.callback_type = res.callback_type
      if (res.callback_type === 1) {
        data.form.callback_role = res.callback_ratio
        data.form.callback_role_list = []
      } else {
        data.form.callback_role = undefined
        data.form.callback_role_list = res.callback_role || []
      }
      data.form.callback_amount_type = res.callback_amount_type
      if (res.callback_amount_type === 3) {
        data.form.callback_amount_role = +res.callback_amount_role
        data.form.callback_amount_role_list = []
      } else if (res.callback_amount_type === 4) {
        data.form.callback_amount_role = +res.callback_amount_role
        data.form.callback_amount_role_list = []
      } else if ([1, 2].includes(res.callback_amount_type)) {
        data.form.callback_amount_role = undefined
        data.form.callback_amount_role_list = []
      } else {
        data.form.callback_amount_role = undefined
        data.form.callback_amount_role_list = res.callback_amount_role
          ? JSON.parse(res.callback_amount_role)
          : [{ min_price: undefined, max_price: undefined, ratio: undefined }]
      }
      if (data.form.platform_id === 1 && !shopInfo.value?.limit_callback) {
        data.form.callback_ratio = 100
        data.form.callback_amount_type = 1
        data.form.callback_amount_role = undefined
      }
      if (isGDTFEEDBACKModify.value && !props.item.callback_type) {
        data.form.callback_type = 1
        data.form.callback_role = 100
        data.form.callback_amount_type = 1
      }

      data.cache_wechat_app_name = props.item.wechat_app_name || ''
      await getCallbackList(+props.item?.platform_id || undefined)
      // if (props.item.landing_page_id) {
      //   result = await get_html_detail({ id: props.item.landing_page_id })
      // }
      landing_page_item_data.value = result?.data?.tkHtml || null
      data.form.landing_page_id = landing_page_item_data.value?.id || 0
      getAdAppidList()
    } else {
      data.form = {
        name: '',
        product_id: props.goodsDetail.id,
        platform_id: 1,
        ad_account_id: undefined,
        backflow_status: undefined,
        back_coupon_ids: undefined,
        view_coupon_status: undefined,
        view_coupon_ids: undefined,
        full_status: 0,
        full_coupon_ids: undefined,
        stay_status: 0,
        stay_coupon_ids: undefined,
        callback_status: undefined,
        callback_time: 1,
        callback_ratio: 100,
        openKefu: 1,
        kefu_group_id: undefined,
        kefu_template_id: 0,
        kefu_template_name: '',
        wechat_landing_type: 1,
        active_status: 2,
        active_type: 1,
        style_type: 1,
        active_id: undefined,
        open_video: shopInfo.value.open_video,
        jump_type: 0,
        ad_entity_id: undefined,
        shop_entity_id: undefined,
        web_center: {
          image: [],
          content: undefined,
          title: undefined,
          button: undefined,
          button_style: 1,
          type: 1,
          is_click: true,
          is_direct: true
        },
        callback_type: 1,
        callback_role: 100,
        callback_role_list: [],
        product_type: 2,
        callback_amount_type: 1,
        callback_amount_role: undefined,
        callback_amount_role_list: []
      }
      // 微信小店隐藏优惠券相关
      if (props.goodsDetail.type !== 4) {
        data.form.backflow_status = props.goodsDetail.back_coupon_ids ? 1 : 0
        data.form.back_coupon_ids = +props.goodsDetail.back_coupon_ids || undefined
        data.form.view_coupon_status = props.goodsDetail.view_coupon_ids ? 1 : 0
        data.form.view_coupon_ids = +props.goodsDetail.view_coupon_ids || undefined
        data.form.full_status = props.goodsDetail.full_coupon_ids ? 1 : 0
        data.form.full_coupon_ids = +props.goodsDetail.full_coupon_ids || undefined
        data.form.stay_status = props.goodsDetail.stay_coupon_ids ? 1 : 0
        data.form.stay_coupon_ids = +props.goodsDetail.stay_coupon_ids || undefined
      }
      getBestApp()
      getCallbackList(data.form.platform_id)
    }
    nextTick(() => {
      ruleForm.value.resetFields()
    })
    getList()
    getAdDmpList()
  }
  const backFlowHandler = async () => {
    try {
      const { audit_status, id } = props.goodsDetail
      let infoFn: any = undefined
      if (audit_status.on_sale_status) {
        infoFn = setProductInfo
      } else if (audit_status.audit_ver_status) {
        infoFn = getExamineDetailApi
      } else {
        infoFn = get_product_temp_info
      }
      let { data: newData = {} } = await infoFn({ id })
      if (audit_status.temp_status) newData = JSON.parse(newData.data) // 草稿不支持广告投放，此处没用
      data.weixinShopProductTitle = newData?.wechat_store_product_name || ''
      adRules['back_coupon_ids'].push({
        trigger: ['change', 'blur'],
        validator: async () => {
          if (data.isBackFlowWeixinShopValid) {
            return Promise.reject('请保持商品一致')
          } else {
            return Promise.resolve()
          }
        }
      })
    } catch (e) {
      console.log('获取商品信息失败', e)
    }
  }
  let isWeixinshop = ref(false)
  watch(() => props.item, initData, { immediate: true })
  watch(
    () => props.goodsDetail,
    () => {
      if (props.goodsDetail?.type === 4) {
        isWeixinshop.value = true
        // backFlowHandler()
      }
    },
    {
      immediate: true
    }
  )
  onMounted(() => {
    visibilitychange()
  })
  const visibilitychange = () => {
    document.addEventListener('visibilitychange', function () {
      if (document.visibilityState === 'visible') {
        getAdDmpList()
      }
    })
  }
  const landing_page_item_data = ref(null)
  const itemStatus = (item) => {
    data.dialog = {
      id: '',
      title: '',
      visible: false,
      width: '',
      type: ''
    }
    landing_page_item_data.value = item[0]
    data.form.landing_page_id = item[0].id
  }
  const closeItem = () => {
    landing_page_item_data.value = null
    data.form.landing_page_id = 0
  }
</script>
<style lang="scss" scoped>
  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-form-item__content) {
    display: block;
  }

  :deep(.el-form-item__label) {
    white-space: nowrap;
  }

  .ad_form::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    /**/
  }

  .ad_form::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
  }

  .ad_form::-webkit-scrollbar-thumb {
    background: #c5c5c5;
    border-radius: 10px;
    display: block;
    padding-left: 30px;
  }
  .text1 {
    padding: 5px 0;
  }

  .ad_link {
    margin-right: -14px;

    .ad_form {
      max-height: 600px;
      overflow: auto;
      padding-right: 15px;
      .jump_type_cls {
        height: var(--height-large);
        line-height: var(--height-large);
      }

      .flex_btn {
      }

      .coupon {
        :deep(.ant-form-item) {
          margin-bottom: 0px;
        }
        .title {
          .switch_name {
            display: inline-block;
            min-width: 80px;
            height: var(--height-medium);
            line-height: var(--height-medium);
            color: #404040;
          }
        }

        .link_btn {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #85878a;
          line-height: 24px;
          display: inline-block;
          margin-top: 5px;

          .btn {
            color: var(--primary-color);
            cursor: pointer;
          }
        }

        :deep(.el-form-item__error) {
          position: static;
        }
      }
      .image_list {
        flex-wrap: wrap;
        flex-direction: column;

        .item,
        .upload {
          position: relative;
          width: 101px;
          height: 101px;
          background: #f1f5fc;
          border-radius: 4px;
          cursor: pointer;
          // overflow: hidden;
          margin-bottom: 10px;
        }

        .item {
          margin-right: 21px;
          .imgg {
            width: 100%;
            height: 100%;
            border-radius: 4px;
          }

          :deep(.el-image) {
            width: 100%;
            height: 100%;
          }

          .name {
            height: 30px;
            background: linear-gradient(0deg, #000000, rgba(0, 0, 0, 0.01));
            border-radius: 0px 0px 4px 4px;
            color: #fff;
            position: absolute;
            bottom: 0;
            width: 100%;
            text-align: center;
            box-sizing: border-box;
            padding: 0 5px;
          }

          .del_img {
            position: absolute;
            top: -10px;
            right: -7px;
            display: none;
          }

          &:hover {
            .del_img {
              display: inline-block;
            }
          }
        }

        .upload {
          border: 1px dashed #d8dde8;

          .add {
            width: 26px;
          }

          .desc {
            color: #999;
            margin-top: 9px;
            line-height: normal;
          }
        }
      }
      .video_list {
        flex-wrap: wrap;
        flex-direction: column;

        .item,
        .upload {
          position: relative;
          width: 102px;
          height: 160px;
          background: #f1f5fc;
          border-radius: 4px;
          cursor: pointer;
        }

        .item {
          margin-right: 21px;
          .imgg {
            width: 100%;
            height: 100%;
            border-radius: 4px;
          }

          :deep(.el-image) {
            width: 100%;
            height: 100%;
          }

          .name {
            height: 30px;
            background: linear-gradient(0deg, #000000, rgba(0, 0, 0, 0.01));
            border-radius: 0px 0px 4px 4px;
            color: #fff;
            position: absolute;
            bottom: 0;
            width: 100%;
            text-align: center;
            box-sizing: border-box;
            padding: 0 5px;
          }

          .del_img {
            position: absolute;
            top: -10px;
            right: -7px;
            display: none;
          }

          &:hover {
            .del_img {
              display: inline-block;
            }
          }
        }

        .upload {
          border: 1px dashed #d8dde8;

          .add {
            width: 26px;
          }

          .desc {
            font-size: 12px;
            margin-top: 16px;
            line-height: normal;
          }
        }
      }
    }
    .lpname {
      margin-right: 10px;
      max-width: 200px;
      display: flex;
      white-space: nowrap;
      cursor: pointer;
      background: #ecf5ff;
      border: 1px solid #b3d8ff;
      font-weight: 500;
      padding: 3px 10px;
      font-size: 14px;
      border-radius: 4px;
      line-height: normal;
      color: var(--el-color-primary);
    }
    .temp-name {
      max-width: 200px;
    }
    .ellipsis {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .flex-align {
      display: flex;
      align-items: center;
    }
    .fooler_btn {
      text-align: end;
      margin-right: 20px;
      margin-top: 20px;
    }
    .callback-tips {
      font-size: 12px;
      font-weight: 400;
      color: #85878a;
      line-height: 24px;
      display: inline-block;
      margin-top: 5px;
    }
  }
  :deep(.ant-image) {
    display: block;
  }
  .ad_link .ad_form .jump_type_cls {
    height: 30px;
  }
  :deep(.ant-row) {
    align-items: baseline !important;
  }
  .item-mark {
    position: absolute;
    width: 100px;
    height: 158px;
    background: #000;
    z-index: 100;
    flex-direction: column;
    opacity: 0.58;
    left: 1px;
    top: 1px;
  }
  .error {
    border: 1px solid red;
  }
  .youxiaoqi {
    font-weight: 400;
    font-size: 12px;
    color: #93969a;
  }
  .jumpTypePage {
    :deep(.ant-row) {
      align-items: flex-end !important;
    }
  }
</style>
