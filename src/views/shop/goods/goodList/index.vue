<template>
  <a-card class="page_main common_page_warp common_card_wrapper" title="商品落地页">
    <template #extra>
      <!-- v-if="state.open_wechat_shop === 1" -->
      <a-button type="primary" @click="routerPush({ name: 'GoodsPublish', query: { is_temp: 1, isWeixinshop: 1 } })"
        >发布微信小店商品</a-button
      >
      <a-button
        v-auth="['shopShopFabuShop']"
        type="primary"
        @click="routerPush({ name: 'GoodsPublish', query: { is_temp: 1 } })"
        >发布商品</a-button
      >
      <a-button @click="handleExportCreate">导出</a-button>
      <a-button type="primary" ghost @click="gotoSubjectManagement" v-if="isRoute('SubjectManagement')"
        >添加资质</a-button
      >
    </template>
    <SearchBaseLayout :data="schemas" :actions="formConfig" @changeValue="submitForm" />
    <ButtonRadioGroup class="btn_list" :data="btnOptions" @changeValue="changeBtnType" />
    <div class="flex flex-justify-between items-center">
      <a-space class="page_table_toolbar" :size="[12, 8]">
        <template v-for="(v, i) in btnsData" :key="i">
          <template v-if="v.confirm">
            <!-- <a-popconfirm
            :title="v.confirm.message"
            @confirm="onBtns(i, {}, v)"
            :disabled="!!(v.disabledStatus(state.selectionItem) && !state.selectionItem.length)"
          > -->
            <a-button
              v-auth="v.auth"
              @click="onBtns(i, {}, v)"
              :disabled="!!(v.disabledStatus(state.selectionItem) && !state.selectionItem.length)"
              >{{ v.name }}</a-button
            >
            <!-- </a-popconfirm> -->
          </template>
          <template v-else-if="v.name == '批量复制'">
            <a-button
              v-auth="['shopShopCopyShop']"
              v-if="!!(v.disabledStatus(state.selectionItem) && !state.selectionItem.length)"
              :disabled="!!(v.disabledStatus(state.selectionItem) && !state.selectionItem.length)"
              >{{ v.name }}</a-button
            >
            <a-dropdown v-else>
              <a-button
                v-auth="['shopShopCopyShop']"
                :disabled="!!(v.disabledStatus(state.selectionItem) && !state.selectionItem.length)"
                >{{ v.name }}</a-button
              >
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <div @click="onBtns(i, {}, v, 1)">本店复制</div>
                  </a-menu-item>
                  <a-menu-item>
                    <div @click="onBtns(i, {}, v, 2)">跨店复制</div>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
          <template v-else>
            <a-button
              :type="v.type"
              :disabled="!!(v.disabledStatus(state.selectionItem) && !state.selectionItem.length)"
              @click="onBtns(i, {}, v)"
              >{{ v.name }}</a-button
            >
          </template>
        </template>
      </a-space>
      <a-checkbox v-model:checked="state.score_sort" @change="changeSort">
        <span class="inline-block">优先展示低分商品</span>
      </a-checkbox>
    </div>
    <div class="page_table">
      <TableZebraCrossing
        :data="state.tableConfigOptions"
        @change="pageChange"
        :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
      >
        <template #headerCell="{ scope: { column } }">
          <template v-if="column.dataIndex === 'sales_actual'">
            <a-space class="sales_actual_class">
              <span
                @click="
                  salesActualSort({
                    prop: 'sales_actual',
                    order: null
                  })
                "
              >
                累计销量
              </span>
              <a-popover
                trigger="hover"
                placement="bottom"
                :overlayInnerStyle="{
                  boxShadow: 'rgba(0, 0, 0, 0.06) 0px 0px 10px 5px',
                  padding: '6px 8px',
                  width: '200px'
                }"
              >
                <span class="ant-table-column-sorter ant-table-column-sorter-full">
                  <span class="ant-table-column-sorter-inner">
                    <span
                      role="presentation"
                      aria-label="caret-up"
                      class="anticon anticon-caret-up ant-table-column-sorter-up"
                    >
                      <svg
                        focusable="false"
                        data-icon="caret-up"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                        aria-hidden="true"
                        viewBox="0 0 1024 1024"
                      >
                        <path
                          d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                        ></path>
                      </svg>
                    </span>
                    <span
                      role="presentation"
                      aria-label="caret-down"
                      class="anticon anticon-caret-down ant-table-column-sorter-down"
                    >
                      <svg
                        focusable="false"
                        data-icon="caret-down"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                        aria-hidden="true"
                        viewBox="0 0 1024 1024"
                      >
                        <path
                          d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                        ></path>
                      </svg>
                    </span>
                  </span>
                </span>
                <template #title>
                  <div class="sales_popover">
                    <div class="title">排序</div>
                    <div class="title_assist">销量仅统计支付订单</div>
                    <div class="border_line"></div>
                    <div class="title_assist">排序方式</div>
                    <a-radio-group
                      v-model:value="state.query.sales_actual_sort_type"
                      @change="
                        salesActualSort({
                          prop: 'sales_actual',
                          order: state.query.saleSort == '1' ? 'ascend' : state.query.saleSort == '2' ? 'descend' : null
                        })
                      "
                    >
                      <a-radio :value="1">按累计销量</a-radio>
                      <a-radio :value="2">按广告引流销量</a-radio>
                      <a-radio :value="3">按自然流量销量</a-radio>
                    </a-radio-group>
                    <div class="border_line"></div>
                    <div class="title_assist">排序规则</div>
                    <a-radio-group
                      v-model:value="state.query.saleSort"
                      @change="
                        salesActualSort({
                          prop: 'sales_actual',
                          order: state.query.saleSort == '1' ? 'ascend' : state.query.saleSort == '2' ? 'descend' : null
                        })
                      "
                    >
                      <a-radio value="2">从高到低</a-radio>
                      <a-radio value="1">从低到高</a-radio>
                    </a-radio-group>
                  </div>
                </template>
              </a-popover>
            </a-space>
          </template>
          <template v-if="column.dataIndex === 'is_brand_shop'">
            <div>
              <span>店铺主页展示</span>
              <a-tooltip>
                <template #title>仅支持售卖中商品同步，开启后小程序店铺内将展示该商品</template>
                <QuestionCircleFilled style="color: #939599" class="m-l-8px" />
              </a-tooltip>
            </div>
          </template>

          <template v-if="column?.dataIndex === 'handle'">
            <a-space>
              <span>{{ column?.title }} </span>
              <span>
                <SetTableColumns
                  class="cursor-pointer"
                  v-model:data="state.tableConfigOptions.columns"
                  :column="columns"
                />
              </span>
            </a-space>
          </template>
        </template>
        <template #bodyCell="{ scope: { record, column } }">
          <template v-if="column.dataIndex === 'title'">
            <div class="flex goods_info">
              <div class="img">
                <a-image
                  style="width: 60px; height: 60px; border-radius: 6px"
                  :src="record.image"
                  fit="fill"
                  :fallback="errorImg"
                />
              </div>
              <div class="goods_info_data">
                <a-tooltip
                  popper-class="toolt"
                  :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                  placement="topLeft"
                >
                  <template #title>{{ record.title }}</template>
                  <p class="goods_info_data_name" @click="handleGoodsDetail(record)">
                    {{ record.title }}
                  </p>
                </a-tooltip>
                <!-- goods_info_data_number -->
                <span class="number-id">商品ID：{{ record.code }}</span>
                <div
                  class="flex font-size-12px"
                  v-if="record.score < 80 && record.score > 0 && record?.audit_status?.on_sale_status"
                >
                  <div
                    class="bg-#FFF6F6 pl-4px pr-5px border h-20px border-solid border-#FDBEA1 border-rd-2px font-size-12px inline-flex flex-y-center"
                  >
                    <SvgIcon icon="goods-score" class="font-size-12px mr-4px" />
                    <span class="c-#FF5400 ml-2px mt-1px">评分过低</span>
                  </div>
                  <span class="c-#647DFF underline cursor-pointer ml-8px" @click="onShowDialog('suggestion', record)"
                    >查看建议>></span
                  >
                </div>
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'product_type'">
            <div class="flex items-center">
              <img class="w-14px h-14px" :src="imgObj[record.type].img" alt="" />
              <span class="ml4px"> {{ imgObj[record.type].name }}</span>
            </div>
            <div v-if="record.type == 4" class="number-id ml-18px">{{ record.wechat_shop_name }}</div>
            <div v-if="record?.wechat_product_status === 2">商品已在微信小店删除</div>
          </template>
          <template v-if="column.dataIndex === 'category_names'">
            <span> {{ record.category_names || '-' }}</span>
          </template>
          <template v-if="column.dataIndex === 'price'">
            <a-space :size="[8, 10]" wrap>
              <span>{{ record.price.toFixed(2) }}</span>
              <EditOutlined
                v-if="record.status !== 1"
                class="icons icons_item icons_none"
                @click="onBtns(-100, record)"
              />
            </a-space>
          </template>
          <template v-if="column.dataIndex === 'protections'">
            <div v-for="(item, index) in record.protections" :key="index">{{ item }}</div>
            <!-- 下架春节停发-暂时注释 -->
            <div v-if="record.year_stop_send === 1 && false" class="number-id">
              {{ moment.unix(record?.stop_start_time).format('YYYY-MM-DD') }}至{{
                moment.unix(record?.stop_end_time).format('YYYY-MM-DD')
              }}暂停发货
            </div>
          </template>
          <template v-if="column.dataIndex === 'desc'">
            <template v-if="record.desc">
              <a-tooltip :getPopupContainer="(triggerNode: any) => triggerNode.parentNode" placement="topLeft">
                <template #title>{{ record.desc || '--' }}</template>
                <div class="text_overflow">{{ record.desc || '--' }}</div>
              </a-tooltip>
            </template>
            <template v-else>--</template>
          </template>
          <template v-if="column.dataIndex === 'sales_actual'">
            <a-space :size="[8, 0]">
              <span>{{ record.sales_actual }}</span>
              <a-popover
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                placement="bottom"
                trigger="hover"
                :overlayInnerStyle="{ boxShadow: 'rgba(0, 0, 0, 0.06) 0px 0px 10px 5px', width: '200px' }"
              >
                <InfoCircleOutlined class="icons icons_item" @click="record.salesvisible = true" />
                <template #title>
                  <div style="line-height: 1.8; font-size: 12px">
                    <div>累计销量：{{ record.sales_actual || 0 }}</div>
                    <div>广告引流销量：{{ record.ad_sales || 0 }}</div>
                    <div>自然流量销量：{{ record.natural_sales || 0 }}</div>
                    <div>顺手买一件：{{ record.just_buy_sales || 0 }}</div>
                    <div>周边推荐：{{ record.noun_sales || 0 }}</div>
                  </div>
                </template>
              </a-popover>
            </a-space>
          </template>
          <template v-if="column.dataIndex === 'status'">
            <a-space :size="[6, 0]">
              <span class="round" :style="{ background: colorType(record.on_sales_txt) }"></span>
              <span>{{ record.on_sales_txt }}</span>
              <a-tooltip
                v-if="record.on_sale === 3 && record.violation_msg"
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                placement="top"
              >
                <template #title>{{ record.violation_msg }}</template>
                <InfoCircleOutlined class="warning" />
              </a-tooltip>
            </a-space>
          </template>
          <template v-if="column.dataIndex === 'status_txt'">
            <!-- <span class="flex_align_center" :style="{ color: statusTxt(record.status_txt) }">
              {{ statusWord(record) }}
              <a-tooltip
                v-if="record.status === 1"
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                placement="top"
              >
                <template #title>{{
                  record.audit_num ? '审核通过后小程序端展示修改后的商品信息' : '审核通过商品将自动上架'
                }}</template>
                <InfoCircleOutlined class="warning" />
              </a-tooltip>
              <a-tooltip
                v-if="record.status === 3 && record.fail_msg"
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                placement="top"
              >
                <template #title>{{ record.fail_msg }}</template>
                <InfoCircleOutlined class="warning" />
              </a-tooltip>
            </span> -->
            <div>
              <div class="flex">
                <span>线上版本：</span>
                <span class="flex_align_center c-#60A13B" v-if="record?.audit_status?.on_sale_status">审核通过</span>
                <span v-else>-</span>
              </div>
              <div class="flex">
                <span>审核版本：</span>

                <div
                  v-show="record?.audit_status?.audit_ver_status"
                  class="flex_align_center"
                  :class="{
                    'c-#118BCE': record?.audit_status?.audit_ver_status === 1,
                    'c-#E63030': record?.audit_status?.audit_ver_status === 2
                  }"
                >
                  <span>{{ record?.audit_status?.audit_ver_status === 1 ? '审核中' : '审核拒绝' }}</span>
                  <template v-if="record?.audit_status?.audit_ver_status === 1">
                    <a-tooltip placement="top">
                      <template #title>{{
                        record.audit_num ? '审核通过后小程序端展示修改后的商品信息' : '审核通过商品将自动上架'
                      }}</template>
                      <InfoCircleOutlined class="warning ml-4px" />
                    </a-tooltip>
                  </template>
                  <template v-if="record?.audit_status?.audit_ver_status === 2 && record.fail_msg">
                    <a-tooltip placement="top">
                      <template #title>{{ record.fail_msg }}</template>
                      <InfoCircleOutlined class="warning ml-4px" />
                    </a-tooltip>
                  </template>
                </div>
                <span v-show="!record?.audit_status?.audit_ver_status">-</span>
              </div>
              <div class="flex">
                <span>草稿版本：</span>
                <span class="flex_align_center" v-if="record?.audit_status?.temp_status">草稿</span>
                <span v-else>-</span>
              </div>
              <div class="flex">
                <span>历史版本：</span>
                <span
                  class="flex_align_center cursor-pointer"
                  style="color: var(--primary-color)"
                  v-if="record?.product_version_status == 1"
                  @click="viewPreviousVersions(record)"
                  >查看</span
                >
                <span v-else>--</span>
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'sort'">
            <a-space :size="[10, 0]" v-show="!record.isEditSort">
              <span>{{ record.sort }}</span>
              <EditOutlined
                v-if="record.status !== 1"
                class="icons icons_item icons_none"
                @click="record.isEditSort = true"
              />
            </a-space>
            <a-space flex middle v-show="record.isEditSort">
              <a-input-number
                v-model:value="record.sort"
                :precision="0"
                :min="0"
                :max="10000"
                :controls="false"
                @blur="editSort(record)"
                @keyup.enter="record.isEditSort = false"
              ></a-input-number>
            </a-space>
          </template>
          <template v-if="column.dataIndex === 'coupon_num'">
            <a-button
              class="pa-0 m-r-5px"
              v-if="record.type != 4"
              type="link"
              @click="onShowDialog('associateCoupons', record)"
            >
              {{ record.coupon_num }}
            </a-button>
            <span v-else>--</span>
          </template>
          <template v-if="column.dataIndex === 'is_brand_shop'">
            <a-switch
              v-model:checked="record.is_brand_shop"
              :checkedValue="1"
              :disabled="record.on_sale != 1"
              :unCheckedValue="0"
              @change="onShopSwitch($event, record)"
            />
          </template>
          <template v-if="column.dataIndex === 'updated_at'">
            <span>{{ record.updated_at ? formatDate(record.updated_at * 1000) || '--' : '--' }}</span>
          </template>
          <template v-if="column.dataIndex === 'shelf_time'">
            <span>{{ record.shelf_time ? formatDate(record.shelf_time * 1000) || '--' : '--' }}</span>
          </template>
          <template v-if="column.dataIndex === 'handle'">
            <!-- 审核状态为 ‘违规下架’ 时，不显示 ‘广告投放’ -->
            <!-- 待审核操作按钮 -->
            <template v-if="record.status == 1">
              <a-space :size="[0, 0]">
                <a-button class="pa-0 m-r-5px" type="link" @click="handleGoodsEdit(record)">编辑</a-button>
                <a-dropdown v-if="record.type == 1">
                  <a-button class="pa-0 m-r-5px" type="link" v-auth="['shopShopCopyShop']">复制</a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item>
                        <div @click="copy(record)">本店复制</div>
                      </a-menu-item>
                      <a-menu-item>
                        <div @click="onShowDialog('otherStoreCopy', record)">跨店复制</div>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
                <a-button class="pa-0 m-r-5px" type="link" v-auth="['shopShopCopyShop']" v-else @click="copy(record)"
                  >复制</a-button
                >
                <a-button
                  class="pa-0 m-r-5px"
                  type="link"
                  v-auth="['shopShopAQIShop']"
                  @click="onShowDialog('ad', record)"
                  v-if="record.on_sale != 3"
                  >广告投放</a-button
                >
              </a-space>
              <a-space>
                <a-popconfirm title="请确认是否删除当前商品？" placement="topRight" @confirm="onDel(record.id, '')">
                  <a-button class="pa-0 m-r-5px" type="link" v-auth="['shopShopDelShop']">删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>

            <a-space v-else-if="record.is_temp == 1" :size="[0, 0]">
              <a-button class="pa-0 m-r-5px" type="link" @click="handleGoodsEdit(record)">编辑</a-button>
              <a-dropdown v-if="record.type == 1">
                <a-button class="pa-0 m-r-5px" type="link" v-auth="['shopShopCopyShop']">复制</a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item>
                      <div @click="copy(record)">本店复制</div>
                    </a-menu-item>
                    <a-menu-item>
                      <div @click="onShowDialog('otherStoreCopy', record)">跨店复制</div>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
              <a-button v-else class="pa-0 m-r-5px" type="link" v-auth="['shopShopCopyShop']" @click="copy(record)"
                >复制</a-button
              >
              <!-- <a-popconfirm title="请确认是否删除当前商品？" placement="topRight" @confirm="onDel(record.id, '')">
                <a-button class="pa-0 m-r-5px" type="link" v-auth="['shopShopDelShop']">删除</a-button>
              </a-popconfirm> -->
            </a-space>
            <a-space v-else :size="[0, 0]" direction="vertical">
              <a-space :size="[0, 0]">
                <a-button
                  class="pa-0 m-r-5px"
                  type="link"
                  v-auth="['shopShopEditShop']"
                  @click="handleGoodsEdit(record)"
                  >编辑</a-button
                >
                <a-dropdown v-if="record.type == 1">
                  <a-button class="pa-0 m-r-5px" type="link" v-auth="['shopShopCopyShop']">复制</a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item>
                        <div @click="copy(record)">本店复制</div>
                      </a-menu-item>
                      <a-menu-item>
                        <div @click="onShowDialog('otherStoreCopy', record)">跨店复制</div>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
                <a-button v-else class="pa-0 m-r-5px" type="link" v-auth="['shopShopCopyShop']" @click="copy(record)"
                  >复制</a-button
                >
              </a-space>
              <a-space :size="[0, 0]" v-if="record.on_sale != 0">
                <a-button
                  type="link"
                  class="pa-0 m-r-5px"
                  v-auth="['shopShopUpDownShop']"
                  @click="onShelves(String(record.id), record.on_sale, record)"
                  v-if="record.on_sale != 3"
                  >{{ record.on_sale == 1 ? '下架' : '上架' }}</a-button
                >
                <a-button
                  class="pa-0 m-r-5px"
                  type="link"
                  v-auth="['shopShopAQIShop']"
                  @click="onShowDialog('ad', record)"
                  v-if="record.on_sale != 3"
                  >广告投放</a-button
                >
              </a-space>
              <a-space>
                <a-popconfirm title="请确认是否删除当前商品？" placement="topRight" @confirm="onDel(record.id, '')">
                  <a-button class="pa-0 m-r-5px" type="link" v-auth="['shopShopDelShop']">删除</a-button>
                </a-popconfirm>
              </a-space>
            </a-space>

            <!-- 待审核操作按钮 -->
            <!-- <template v-if="record.status == 1">
              <div class="handle_btns">
                <div v-auth="['shopShopCopyShop']" class="icons">
                  <span @click="copy(record)"> 复制 </span>
                </div>
                <div v-auth="['shopShopAQIShop']" class="flex_justify_end">
                  <span @click="onShowDialog('ad', record)"> 广告投放 </span>
                </div>
              </div>
            </template> -->
            <!-- 草稿状态 -->
            <!-- <template v-else-if="record.is_temp == 1">
              <div class="handle_btns">
                <a-space :size="[10, 0]" class="icons">
                  <span
                    @click="
                      routerPush({
                        name: 'GoodsPublish',
                        query: { id: record.id, is_temp: record.is_temp, type: 'edit' }
                      })
                    "
                  >
                    编辑
                  </span>
                  <span v-auth="['shopShopCopyShop']" @click="copy(record)"> 复制 </span>
                  <span v-auth="['shopShopDelShop']" class="icons_item" @click="onDel(record.id)"> 删除 </span>
                </a-space>
              </div>
            </template> -->
            <!-- 上下架操作按钮 -->
            <!-- <template v-else>
              <div class="handle_btns">
                <a-space :size="[10, 0]" class="icons">
                  <span
                    v-auth="['shopShopEditShop']"
                    @click="
                      routerPush({
                        name: 'GoodsPublish',
                        query: { id: record.id, is_temp: record.is_temp, type: 'edit' }
                      })
                    "
                  >
                    编辑
                  </span>
                  <span v-auth="['shopShopCopyShop']" @click="copy(record)"> 复制 </span>
                  <span v-auth="['shopShopDelShop']" class="icons_item" @click="onDel(record.id)"> 删除 </span>
                </a-space>
                <div v-if="record.on_sale != 0" class="flex_justify_end">
                  <span
                    v-auth="['shopShopUpDownShop']"
                    @click="onShelves(String(record.id), record.on_sale)"
                    style="margin-right: 10px"
                  >
                    {{ record.on_sale == 1 ? '下架' : '上架' }}
                  </span>
                  <span v-auth="['shopShopAQIShop']" @click="onShowDialog('ad', record)"> 广告投放 </span>
                </div>
              </div>
            </template> -->
          </template>
        </template>
      </TableZebraCrossing>
      <Pagination
        class="m-t-8px"
        v-model:page="state.query.page"
        v-model:pageSize="state.query.size"
        :total="tableData.total"
        @change="getProductList"
      ></Pagination>
    </div>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :wrapClassName="[state.dialog.type === 'suggestion' ? 'goods-suggestion' : '']"
      :footer="null"
      centered
      destroyOnClose
      @cancel="handleCancel"
    >
      <Freight v-if="state.dialog.type === 'Freight'" :ids="state.dialog.ids" @event="onEvent" />
      <AdLaunch v-if="state.dialog.type === 'ad'" :goodsDetail="state.item" @event="onEvent" @close="handleClose" />
      <!-- <EditPrice v-if="state.dialog.type === 'price'" @event="onEvent" /> -->
      <DialogEditSku v-if="state.dialog.type === 'editSku'" :id="state.dialog.ids" @event="onEvent"></DialogEditSku>

      <EditGoodsDialog v-if="state.dialog.type === 'editGoods'" :goodsDetail="state.item" @event="onEvent" />
      <AssociateCoupons v-if="state.dialog.type === 'associateCoupons'" :goodsDetail="state.item" />
      <StopNewYearSendModal v-if="state.dialog.type === 'batchSend'" :ids="state.dialog.ids" @event="onEvent" />
      <HistoryVersionsList v-if="state.dialog.type === 'versions'" :ids="state.dialog.ids" @event="onEvent" />
      <OtherStoreCopy
        v-if="state.dialog.type === 'otherStoreCopy'"
        :data="state.item"
        :ids="state.product_ids"
        @event="onEvent"
      />
      <GoodsSuggestion v-if="state.dialog.type === 'suggestion'" :goodsDetail="state.item" @event="onEvent" />
    </a-modal>
  </a-card>
</template>
<script lang="ts" setup>
  defineOptions({ name: 'ShopGoodsList' })
  import {
    EditOutlined,
    InfoCircleOutlined,
    ExclamationCircleOutlined,
    QuestionCircleFilled
  } from '@ant-design/icons-vue'
  // import { Button } from '@/components'
  // import SeachCom from '@/components/ui/common/SearchComponents/SearchBaseLayout.vue'
  // import BtnList from '@/components/ui/common/BtnList.vue'
  import Pagination from '@/components/ui/common/PaginationComponent/index.vue'
  import HistoryVersionsList from './components/HistoryVersionsList.vue'
  import Freight from './components/Freight.vue'
  import AdLaunch from './components/AdLaunch.vue'
  import AssociateCoupons from './components/AssociateCoupons.vue'
  // import EditPrice from './components/editPrice.vue'
  import DialogEditSku from './components/DialogEditSku.vue'
  import EditGoodsDialog from './components/EditGoodsDialog.vue'
  import StopNewYearSendModal from './components/StopNewYearSendModal.vue'
  import OtherStoreCopy from './components/OtherStoreCopy.vue'
  import GoodsSuggestion from './components/GoodsSuggestion.vue'

  // import useUserInfoStore from '@/store/user'
  import { formatDate, requireImg } from '@/utils'
  import { createVNode, onActivated, reactive, nextTick, h, ref, onMounted, watch } from 'vue'
  import {
    deleteProduct,
    copyProduct,
    onSalesProduct,
    setProductList,
    setCategoryList,
    onBatchSalesProduct,
    deleteBatchProduct,
    editProductSort,
    productListCount,
    setBrandShopApi,
    checkGoodEdit,
    bonded_manager_product,
    getShopInfo as getShopInfoApi
  } from './index.api'
  import datas from './src/datas'
  import { exportCreate } from '@/api/common'
  import { Modal, message } from 'ant-design-vue'
  const { schemas, formConfig, columns, btnOptions, colorType, statusTxt, btnsData } = datas()
  import { useRouter, useRouterBack, useDownloadCenter } from '@/hooks'
  const { routerPush, routerResolve, isRoute } = useRouter()
  import errorImg from '@/assets/images/error.png'
  import { cloneDeep, isArray } from 'lodash-es'
  import moment from 'moment'
  import { useRoute } from 'vue-router'

  const { routeParams, resetRouteParams } = useRouterBack()
  const { goCenter } = useDownloadCenter()

  // 获取筛选条件ids
  // const getCategorIds = (data: string[]) => {
  //   if (!isArray(data)) return ''
  //   return data.join(',')
  // }
  const imgObj: Record<number, { name: string; img: string }> = {
    1: {
      name: '普通商品',
      img: requireImg('goods/normal.png')
    },
    2: {
      name: '跨境商品',
      img: requireImg('goods/cross_border.png')
    },
    4: {
      name: '微信小店',
      img: requireImg('goods/weixinShop.png')
    }
  }
  const submitForm = (data: any) => {
    console.log(data, '111')
    state.forzeStatus = true
    state.query = {
      ...state.query,
      ...data.formData,
      category_id: data.formData.category_id
        ? data.formData.category_id[data.formData.category_id.length - 1]
        : undefined,
      product_type: data.formData.product_type ? data.formData.product_type + '' : undefined,
      // category_ids: getCategorIds(data.formData.category_ids),
      admin_ids: (data.formData.admin_ids || []).join(','),
      bonded_id: (data.formData.bonded_id || []).join(',')
    }
    if (isArray(data.formData.price)) {
      state.query.up_price = data.formData.price[0]
      state.query.down_price = data.formData.price[1]
    } else {
      delete state.query.up_price
      delete state.query.down_price
    }
    if (isArray(data.formData.sales_actual)) {
      state.query.up_sales = data.formData.sales_actual[0]
      state.query.down_sales = data.formData.sales_actual[1]
    } else {
      delete state.query.up_sales
      delete state.query.down_sales
    }

    if (isArray(data.formData.shelf_time)) {
      if (moment(data.formData.shelf_time[1]).diff(moment(data.formData.shelf_time[0]), 'days') > 90) {
        return message.warning('时间区间最大为90天')
      }
    }
    if (data.formData.protection) {
      state.query.protection = data.formData.protection.join(',')
    }
    state.query.shelf_time =
      !data.formData?.shelf_time?.length || data.formData?.shelf_time == null
        ? ''
        : `${data.formData.shelf_time[0]}_${data.formData.shelf_time[1]}`
    state.query.page = 1
    getProductList({})
  }

  interface Response {
    code?: number
    message?: string
    data?: any
    timestamp?: any
  }

  // 表和数据
  const tableData = reactive({
    list: [],
    total: 1,
    loading: false
  })

  const state = reactive({
    product_ids: [],
    open_wechat_shop: 2, // 是否开启微信小店
    statusName: 1,
    showPhone: false,
    forzeStatus: true,
    btnActvie: -1,
    score_sort: false,
    item: {},
    query: {
      page: 1,
      size: 10,
      useShop: 'shop_id', //店铺id
      is_examine: 0, // 1：待审核查询，0：其他
      up_price: undefined,
      down_price: undefined,
      up_sales: undefined,
      down_sales: undefined,
      sales_actual_sort_type: undefined,
      price_sort: '', // 价格排序
      sales_actual_sort: '', // 累计销量排序
      sort: '', // 排序 排序
      saleSort: ''
    },
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: '',
      ids: []
    },
    cateGoryList: [],
    selectionItem: [], // 表格选择的Item
    selectedRowKeys: [],
    tableConfigOptions: {
      bordered: false,
      loading: false,
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 2100
      },
      dataSource: [],
      columns: columns,
      pagination: false
    }
  })
  //查看历史版本
  const viewPreviousVersions = (record) => {
    state.dialog = {
      visible: true,
      title: '历史版本',
      width: 1100,
      type: 'versions',
      ids: record.id
    }
  }
  // 商品类型
  const changeBtnType = (data: { value: any }) => {
    btnOptions.value.value = Number(data.value)
    console.log('btnOptions.value', btnOptions.value)
    state.forzeStatus = false
    switch (data.value) {
      case 1:
        if (state.query.on_sale >= 0) delete state.query.on_sale
        if (state.query.is_temp >= 0) delete state.query.is_temp
        state.query.is_examine = 0
        break
      case 2:
        state.query.on_sale = 1
        state.query.is_temp = 0
        state.query.is_examine = 0
        break
      case 3:
        state.query.on_sale = 2
        state.query.is_temp = 0
        state.query.is_examine = 0
        break
      case 4:
        if (state.query.on_sale >= 0) delete state.query.on_sale
        state.query.is_temp = 1
        state.query.is_examine = 0
        break
      case 5:
        // 待审核
        state.query.is_examine = 1
        state.query.on_sale = 0
        state.query.is_temp = 0
        break
      case 6:
        // 违规下架
        state.query.is_examine = 0
        state.query.on_sale = 3
        state.query.is_temp = 0
        break
    }
    state.query.page = 1
    getProductList({})
    getHeadData()
    if (state.selectedRowKeys.length) {
      state.selectedRowKeys = []
      state.selectionItem = []
    }
  }
  const route = useRoute()
  watch(
    () => route.fullPath,
    () => {
      nextTick(() => {
        getProductList() // 监听路由变化并刷新列表
      })
    }
  )
  // 商品列表
  const getProductList = async (filter: any) => {
    try {
      tableData.loading = true
      const resp = await setProductList(state.query)
      state.tableConfigOptions.dataSource = (resp.data?.list || []).map((v: any) => {
        return {
          ...v,
          isEditSort: false,
          salesvisible: false
        }
      })
      tableData.total = resp.data?.total || 0
      if (state.forzeStatus) getHeadData()
    } catch (error) {
      console.error(error)
    } finally {
      tableData.loading = false
    }
  }

  if (route.query?.code) {
    state.query.code = route.query?.code
    schemas.value = schemas.value.map((it) => {
      if (it.field === 'code') {
        it.value = state.query.code
      }
      return it
    })
  }
  const pageChange = (pagination: any, _filters: any, sorter: any) => {
    console.log('sorter', sorter)
    if (sorter && Object.keys(sorter).length) {
      switch (sorter.field) {
        case 'price':
          state.query.price_sort = tablePropSort(sorter)
          state.query.sort = ''
          break
        case 'sort':
          state.query.sort = tablePropSort(sorter)
          state.query.price_sort = ''
          break
      }
      state.query.sales_actual_sort_type = null
      state.query.saleSort = ''
      state.query.sales_actual_sort = ''
      state.query.page = 1
    }
    getProductList({})
  }

  // 获取头部数据
  const getHeadData = async () => {
    try {
      const params = cloneDeep(state.query)
      // 删除商品类型相关字段
      if (params.is_examine >= 0) delete params.is_examine
      if (params.on_sale >= 0) delete params.on_sale
      if (params.is_temp >= 0) delete params.is_temp
      let res = await productListCount(params)
      console.log(res, 'res')
      const list = res.data?.count || {}
      const keys = {
        1: 'all',
        2: 'on_sale',
        3: 'off_sale',
        4: 'temp',
        5: 'wait_status',
        6: 'boss_off_sale'
      }
      btnOptions.value.list.forEach((v) => {
        v.total = 0
        if (v.value) {
          for (let key in list) {
            if (key == (keys as any)[v.value]) v.total = list[key]
          }
        }
      })
    } catch (error) {
      console.log(error)
    }
  }

  const changeSort = (event) => {
    const val = event.target.checked ? 'asc' : undefined
    state.query.score_sort = val
    getProductList({})
  }
  // 获取商品类目列表
  const getCategorlList = async () => {
    try {
      let [res, result] = await Promise.all([await setCategoryList({}), await bonded_manager_product()])
      schemas.value.forEach((v) => {
        if (v.field == 'category_id') {
          v.props.options = res.data || []
        } else if (v.field == 'bonded_id') {
          v.props.options =
            result.data?.list?.map((it: any) => {
              return {
                ...it,
                label: it.account_name,
                value: it.id,
                children: it.bonded_product?.map((item: any) => {
                  return (
                    {
                      ...item,
                      label: item.name,
                      value: item.id,
                      children:
                        item.bonded_manage?.map((k: any) => {
                          return {
                            ...k,
                            label: k.warehouse_name,
                            value: k.id
                          }
                        }) || []
                    } || []
                  )
                })
              }
            }) || []
        } else {
          return
        }
      })
    } catch (error) {}
  }
  getCategorlList()

  // 审核状态文案
  const statusWord = (row: { status_txt: any; status: number; audit_num: any }) => {
    let text = row.status_txt
    if (row.status === 1) text = row.audit_num ? '修改审核中' : '发布审核中'
    return text
  }

  /**
   * table表格排序 返回规则
   */
  function tablePropSort(row: { order: any }) {
    let text = ''
    switch (row.order) {
      case 'ascend':
        text = 'asc'
        break
      case 'descend':
        text = 'desc'
        break
      default:
        text = ''
        break
    }
    return text
  }

  // 累计销量排序
  function salesActualSort(row: { prop: string; order: string | null }) {
    // ttTableRef.value.clearSort()
    if (!row.order) state.query.saleSort = ''
    state.query.sales_actual_sort = tablePropSort(row)
    state.query.price_sort = ''
    state.query.sort = ''
    getProductList({})
  }

  // 是否同步店铺
  const onShopSwitch = async (v: any, row: any) => {
    row.is_brand_shop = v == 1 ? 0 : 1
    let text = v == 1 ? '开启后小程序店铺内将展示该商品' : '关闭后小程序店铺内将不再展示该商品'
    Modal.confirm({
      title: '提示',
      centered: true,
      icon: createVNode(ExclamationCircleOutlined),
      content: text,
      async onOk() {
        try {
          setBrandShop(row.id)
        } catch (error) {
          console.error(error)
        }
      }
    })
  }

  // 同步店铺开关接口
  const setBrandShop = async (id: any) => {
    try {
      let res = await setBrandShopApi({ product_id: id })
      message.success(res.msg)
      getProductList({})
    } catch (error) {}
  }
  //选择商品优惠券
  const relation = () => {}

  const copy = async (item: { id: any }) => {
    Modal.confirm({
      title: '提示',
      centered: true,
      icon: createVNode(ExclamationCircleOutlined),
      content: '是否确认复制当前商品？',
      onOk() {
        return new Promise(async (resolve, reject) => {
          // setTimeout(Math.random() > 0.5 ? resolve : reject, 1000)
          try {
            await copyProduct({ product_ids: String(item.id), type: 1 }) //type 1：内部复制，2：跨店复制
            message.success('复制当前商品成功')
            resolve
            getProductList({})
          } catch (error) {
            reject(error)
          } finally {
            Modal.destroyAll()
          }
        }).catch(() => {
          message.warning('复制当前商品失败')
          getProductList({})
        })
      },
      onCancel() {
        Modal.destroyAll()
      }
    })
  }

  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys)
    console.log('selectedRows changed: ', selectedRows)
    state.selectedRowKeys = selectedRowKeys
    state.selectionItem = selectedRows
  }

  const onBtns = async (i: any, row: any, btn: any, actionType?: number) => {
    if (btn && btn.disabledStatus(state.selectionItem) && actionType != 1) {
      let tips = actionType == 2 ? '跨店复制商品仅支持普通商品' : '请选择有效数据'
      message.warning(tips)
      return
    }
    switch (i) {
      case 0:
        // 批量上架
        //  2为厂库中状态  把草稿剔除其他的全上架
        {
          let ids = state.selectionItem
            .filter((v) => [2, 3].includes(v.on_sale) && v.is_temp != 1 && v.status != 1)
            .map((v) => v.id)
          onShelves(ids, 2)
        }
        break
      case 1:
        // 批量下架
        //  1为售卖中状态 把草稿剔除其他的全上架
        {
          let ids = state.selectionItem.filter((v) => v.on_sale == 1 && v.status != 1).map((v) => v.id)
          onShelves(ids, 1)
        }
        break
      case 2:
        // 批量删除
        //  1为售卖中状态 把草稿剔除其他的全上架
        {
          let ids = state.selectionItem.map((v) => v.id)
          console.log(ids)
          onDel(ids, 'onBtns')
        }
        break
      case 3:
        // 批量设置运费
        let ids = state.selectionItem.map((v) => v.id)

        if (!ids.length) return message.warning('请选择商品')
        state.dialog = { visible: true, title: '批量设置运费', width: 435, type: 'Freight', ids }
        break
      case -100:
        // 设置价格
        state.dialog = { visible: true, title: '批量修改价格', width: 500, type: 'editSku', ids: row.id }
        break
      case 4: {
        // 批量复制
        let ids = state.selectedRowKeys

        if (actionType == 2) {
          //批量跨店复制
          state.product_ids = ids
          state.dialog = {
            visible: true,
            title: createVNode('div', {}, [
              h('span', {}, '跨店铺复制商品'),
              h(
                'span',
                { style: { color: '#aaaaaa', fontSize: '13px', marginLeft: '10px', fontWeight: 400 } },
                '可将本商品信息复制到其他店铺内'
              )
            ]),
            width: 518,
            type: 'otherStoreCopy'
          }
        } else {
          // 批量本店复制
          let params = {
            product_ids: ids.join(','),
            type: 1
          }
          let res = await copyProduct(params)
          if (res.code == 0) {
            if (res.data.err_data?.length > 0) {
              let id_tips = res.data.err_data.join(',')
              message.warning(`${id_tips}复制失败`)
            } else {
              message.success('复制成功')
            }
          }
          getProductList({})
          if (state.selectedRowKeys.length) {
            state.selectedRowKeys = []
            state.selectionItem = []
          }
          state.product_ids = []
        }
      }
      // case 4:
      //   state.dialog = {
      //     visible: true,
      //     title: '批量设置发货时间',
      //     width: 635,
      //     type: 'batchSend',
      //     ids: state.selectionItem.map((v) => v.id)
      //   }
      //   break
    }
    state.btnActvie = i
  }

  /**
   * 上下架功能
   * @param {string} id 商品id 多选ids
   * @param {number} on_sale 上下架操作 1商品上架状态 2商品下架操作
   */
  const onShelves = async (id: any[], on_sale: number, record?: any) => {
    try {
      const text = on_sale == 1 ? { name: '下架', val: 2 } : { name: '上架', val: 1 }
      if (!id) return message.warning(`请选择需要${text.name}的商品`)
      if (record?.type === 4 && on_sale == 2) message.warning('上架后，小店商品将同步上架')
      Modal.confirm({
        title: '提示',
        centered: true,
        icon: createVNode(ExclamationCircleOutlined),
        content: `请确认是否${text.name}当前商品？`,
        onOk() {
          return new Promise(async (resolve, reject) => {
            try {
              let res: Response = {}
              if (!Array.isArray(id)) {
                // 普通操作
                res = await onSalesProduct({ id: Number(id), useShop: 'shop_id', on_sales: text.val })
              } else {
                // 批量操作
                res = await onBatchSalesProduct({ ids: id.join(','), useShop: 'shop_id', on_sales: text.val })
              }
              message.success(res.msg)
              if (state.selectedRowKeys.length) {
                state.selectedRowKeys = []
                state.selectionItem = []
              }
              resolve
              state.forzeStatus = true
              getProductList({})
            } catch (error) {
              getProductList({})
              reject(error)
            } finally {
              Modal.destroyAll()
            }
          }).catch(() => {
            // message.warning('复制当前商品失败')
          })
        },
        onCancel() {
          Modal.destroyAll()
        }
      })
    } catch (error) {
      if (error == 'cancel') {
        // commonMethods.msg('取消操作', 'warning')
      } else {
        console.error(error)
      }
      getProductList({})
    }
  }

  /**
   * 删除item
   * @param {string} id 商品id
   */
  const onDel = async (id: any, type: string) => {
    try {
      if (!id) return message.warning('请选择商品')
      if (type === 'onBtns') {
        Modal.confirm({
          title: '提示',
          centered: true,
          icon: createVNode(ExclamationCircleOutlined),
          content: '请确认是否删除当前商品？',
          onOk() {
            return new Promise(async (resolve, reject) => {
              // setTimeout(Math.random() > 0.5 ? resolve : reject, 1000)
              try {
                if (!Array.isArray(id)) {
                  await deleteProduct({ id })
                } else {
                  await deleteBatchProduct({ product_ids: id.join(',') })
                }
                message.success('删除当前商品成功')
                if (state.selectedRowKeys.length) {
                  state.selectedRowKeys = []
                  state.selectionItem = []
                }
                state.forzeStatus = true
                getProductList({})
                resolve
              } catch (error) {
                reject(error)
              } finally {
                Modal.destroyAll()
              }
            }).catch(() => {
              message.warning('删除当前商品失败')
            })
          },
          onCancel() {
            Modal.destroyAll()
          }
        })
      } else {
        await deleteProduct({ id })
        message.success('删除当前商品成功')
        if (state.selectedRowKeys.length) {
          state.selectedRowKeys = []
          state.selectionItem = []
        }
        state.forzeStatus = true
        getProductList({})
      }
    } catch (error) {
      console.error('删除当前商品失败')
      getProductList({})
    }
  }

  const handleExportCreate = async () => {
    try {
      await exportCreate({
        type: 'product_list',
        params: JSON.stringify(state.query)
      })
      goCenter('DownloadCenter', 'DownloadCenter')
    } catch (error) {
      console.log(error)
    }
  }
  const gotoSubjectManagement = () => {
    routerResolve({
      name: 'SubjectManagement'
    })
  }

  onActivated(() => {
    // nextTick(() => {
    if (routeParams.params?.page) {
      if (routeParams.params?.page === 'add') {
        state.query.page = 1
        state.query.size = 10
      }
      getProductList({})
      resetRouteParams()
    }
    // })
  })
  getProductList({})
  const handleClose = (value) => {
    setTimeout(() => {
      state.dialog.visible = value
      getProductList()
    }, 700)
  }
  const onShowDialog = (type: any, item: {}) => {
    state.item = { ...item }
    switch (type) {
      case 'ad':
        state.dialog = { visible: true, title: '广告投放', width: 920, type: 'ad' }
        break
      case 'price':
        state.dialog = { visible: true, title: '批量修改价格', width: 500, type: 'price' }
        break
      case 'editGoods':
        state.dialog = { visible: true, title: '编辑商品', width: 418, type: 'editGoods' }
        break
      case 'associateCoupons':
        state.dialog = { visible: true, title: '商品优惠券', width: 1200, type: 'associateCoupons' }
        break
      case 'otherStoreCopy':
        state.product_ids = [item.id]
        state.dialog = {
          visible: true,
          title: createVNode('div', {}, [
            h('span', {}, '跨店铺复制商品'),
            h(
              'span',
              { style: { color: '#aaaaaa', fontSize: '13px', marginLeft: '10px', fontWeight: 400 } },
              '可将本商品信息复制到其他店铺内'
            )
          ]),
          width: 518,
          type: 'otherStoreCopy'
        }
        break
      case 'suggestion':
        state.dialog = { visible: true, title: '优化建议', width: 680, type: 'suggestion' }
    }
  }

  // 跳转到商品详情页面
  const handleGoodsDetail = (record: any) => {
    const { audit_status } = record
    if (audit_status.on_sale_status) {
      routerPush({
        path: '/mall/goods/goods_detail',
        query: { id: record.id, version_status: 'on_sale_status' }
      })
    } else if (audit_status.audit_ver_status) {
      routerPush({
        path: '/mall/goods/goods_detail',
        query: { id: record.id, version_status: 'audit_ver_status' }
      })
    } else {
      routerPush({
        path: '/mall/goods/goods_detail',
        query: { id: record.id, version_status: 'temp_status' }
      })
    }
  }

  // 商品编辑功能
  const handleGoodsEdit = async (record: any) => {
    console.log('点击编辑按钮', record)

    try {
      let res = await checkGoodEdit({ id: record.id })
      console.log('ressssss', res)
      if (res.code === 0 && res.data.length > 0) {
        console.log('res', res.data)
        let modal = await Modal.confirm({
          title: '提示',
          width: '482px',
          centered: true,
          content: h('div', {}, [
            h('div', { style: { 'line-height': '24px', 'max-height': '200px', overflow: 'auto' } }, [
              h('span', { style: { color: '#313233' } }, '该商品正在参与'),
              res.data.map((item) => {
                return h(
                  'span',
                  {
                    style: {
                      color: '#1677ff',
                      cursor: 'pointer',
                      padding: '0 2px'
                    },
                    onClick: () => {
                      modal.destroy()
                      routerResolve({
                        name: 'justBuyOne',
                        query: {
                          id: item.id,
                          type: item.type
                        }
                      })
                      // routerPush({
                      //   name: 'justBuyOne',
                      //   query: {
                      //     id: item.id,
                      //     type: item.type
                      //   }
                      // })

                      // window.open(href.href, '_blank')
                    }
                  },
                  `【${item.name}${item.active_type === 1 ? '（顺手买一件）' : '（周边推荐）'}】`
                )
              }),
              h('span', { style: { color: '#313233' } }, '活动，请先去活动中删除该商品，再编辑该商品信息')
            ])
          ]),

          async onOk() {
            console.log('ok')
          }
        })
      } else {
        const result = Object.keys(record.audit_status || {})
          .map((v: any) => record.audit_status[v])
          .filter((v: any) => v)
        if (result.length > 1) {
          onShowDialog('editGoods', record)
        } else {
          routerPush({
            name: 'GoodsPublish',
            query: {
              id: record.id,
              is_temp: record.audit_status.temp_status ? 1 : 0,
              is_online: record.audit_status.on_sale_status ? 1 : 0,
              type: 'edit',
              isWeixinshop: record.type === 4 ? 1 : 0
            }
          })
        }
      }
    } catch (error) {
      console.log('---')
      // error != 'cancel' && commonMethods.catchMsg(error)
    }
    // routerPush({ name: 'GoodsPublish', query: { id: record.id, is_temp: record.is_temp, type: 'edit' } })
  }
  const handleCancel = () => {
    getProductList({})
  }
  const onEvent = (data: { cmd: string }) => {
    if (data.cmd == 'close') {
      state.dialog.visible = false
    } else if (['freightClose', 'editPriceClose', 'submit'].includes(data.cmd)) {
      state.dialog.visible = false
      getProductList({})
      if (state.selectedRowKeys.length) {
        state.selectedRowKeys = []
        state.selectionItem = []
      }
    }
    state.dialog.ids = []
  }

  // 修改排序值
  async function editSort(row: { id: any; sort: any; isEditSort: boolean }) {
    console.log('editSort', row)

    try {
      let res = await editProductSort({ product_id: row.id, sort: Number(row.sort) || 0 })
      message.success(res.msg)
      getProductList({})
    } catch (error) {
      console.error(error)
    } finally {
      row.isEditSort = false
    }
  }
  const getShopInfo = async () => {
    try {
      let res: any = await getShopInfoApi()
      if (res.code === 0) {
        state.open_wechat_shop = res.data.open_wechat_shop
        if (state.open_wechat_shop === 1) {
          schemas.value.forEach((v: any) => {
            if (v.field == 'product_type') {
              v.props.options?.push({
                label: '微信小店',
                value: 4
              })
            }
          })
        }
      }
    } catch (error) {
      console.log(error)
    }
  }
  getShopInfo()
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn';
  .sales_actual_class {
    width: 100%;
    justify-content: space-between;
    .ant-table-column-sorter-inner {
      padding-bottom: 2px;
    }
  }
  .goods_info {
    img,
    .img_item {
      width: 60px;
      height: 60px;
      background: #bec6d6;
      border-radius: 6px;
    }

    .goods_info_data {
      flex: 1;
    }
    .goods_info_data_name {
      word-wrap: break-word;
      word-break: break-all;
      @include text_overflow(2);
    }

    p {
      margin: 0;
    }

    &_data {
      margin-left: 10px;
      font-family: PingFang SC;
      font-weight: 400;

      &_name {
        overflow: hidden; //多出的隐藏
        text-overflow: ellipsis; //多出部分用...代替
        display: -webkit-box; //定义为盒子模型显示
        -webkit-line-clamp: 2; //用来限制在一个块元素显示的文本的行数
        -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
        font-size: 14px;
        color: var(--primary-color);
        cursor: pointer;
      }

      &_number {
        font-size: 12px;
        color: #999999;
      }
    }
  }

  // 按钮
  .icons {
    &_item {
      cursor: pointer;
      &:hover {
        color: var(--primary-color);
      }
    }
  }
  .icons_none {
    display: none;
  }
  :deep {
    .page_table_toolbar {
      margin-top: 16px;
    }
    .page_table .ant-table-wrapper .ant-table-row:hover .ant-table-cell {
      .icons_none {
        display: block;
      }
    }
  }

  // 累计销量排序弹框
  .sales_popover {
    .title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #080f1e;
      margin-bottom: 8px;
    }
    .title_assist {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #404040;
    }
    .border_line {
      width: 100%;
      height: 1px;
      background-color: #eef0f3;
      margin: 10px 0;
    }
    :deep {
      .ant-radio-group {
        //
      }
      .ant-radio-wrapper {
        width: 100%;
        height: 14px;
        margin-top: 15px;
        display: flex;
        align-items: center;
        span {
          font-size: 12px;
        }
      }
    }
  }
  .round {
    width: 8px;
    height: 8px;
    background: #404040;
    border-radius: 50%;
    display: inline-block;
  }

  .handle_btns {
    user-select: none;

    .icons {
      margin-bottom: 10px;
    }

    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }
  :deep(.ant-table-content) {
    min-height: 230px;
  }
  .ant-btn {
    height: auto;
  }
</style>
