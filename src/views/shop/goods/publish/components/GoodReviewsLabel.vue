<template>
  <div class="good_reviews_label">
    <div class="label_title flex_ju_sp">
      <span class="label_title_text">
        <span class="label_title_dot">*</span>
        <span>好评标签</span>
      </span>
      <a-button type="link" @click="handleAddRow">添加</a-button>
    </div>
    <div class="page_main_table">
      <a-table
        ref="ttTableRef"
        :data-source="data.tableData"
        :columns="columns"
        scrollbar-always-on
        border
        empty-text="请添加好评标签"
        :pagination="false"
      >
        <template #bodyCell="{ record, column, index }">
          <template v-if="column.dataIndex === 'tag_name'">
            <a-form-item
              :name="`temp[${index}].tag_name`"
              :rules="[
                {
                  required: true,
                  trigger: ['change', 'blur'],
                  validator() {
                    return tagNameValid(record.tag_name)
                  }
                },
                {
                  trigger: ['blur'],
                  validator: (rules, value) => titleValidator(rules, record.tag_name, contrabandList)
                }
              ]"
            >
              <a-input
                v-model:value="record.tag_name"
                placeholder="请输入标签名称"
                :maxlength="5"
                oninput="value=value.replace(/[~!@#$%^&*()_.]/g, '')"
                @change="(v) => (record.tag_name = v.target.value)"
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'tag_num'">
            <a-form-item
              :name="`temp[${index}].tag_num`"
              :rules="[
                {
                  required: true,
                  trigger: ['change', 'blur'],
                  validator() {
                    return tagNumValid(record.tag_num)
                  }
                },
                {
                  trigger: ['blur'],
                  validator: (rules, value) => titleValidator(rules, record.tag_num + '', contrabandList)
                }
              ]"
            >
              <a-input-number
                v-model:value="record.tag_num"
                :min="1"
                :max="1000000000"
                :precision="0"
                step-strictly
                :controls="false"
                placeholder="请输入标签数量"
                @blur="handleBlurNum($event, record, index)"
                oninput="value=value.replace(/^(0+)|[^\d.]/g,'').replace(/^\./g, '').replace(/\.{2,}/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').match(/^\d*(\.?\d{0,2})/g)[0] || null"
                @change="(v) => (record.tag_num = v)"
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'handle'">
            <a-button type="link" @click="handleDeleteRow(record, index)">删除</a-button>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup name="GoodReviewsLabel">
  import { reactive, ref, watchEffect, inject, nextTick } from 'vue'
  import { noEmoji } from '@/utils'
  import { message } from 'ant-design-vue'
  import rules from '../src/rules'
  const formRef = inject('formRef')
  const aaa = ref()
  const aaaMeth = (e) => {
    console.log('aaaMeth', e)
  }

  const columns = reactive([
    { title: '标签名称', dataIndex: 'tag_name', key: 'tag_name' },
    { title: '标签数量', dataIndex: 'tag_num', key: 'tag_num' },
    { title: '操作', dataIndex: 'handle', key: 'handle', width: 150 }
  ])

  const props = defineProps({
    modelValue: [Array, String],
    contrabandList: Array
  })
  const emit = defineEmits(['update:modelValue', 'change'])
  const data = reactive({
    tableData: []
  })
  const { titleValidator } = rules()
  watchEffect(() => {
    const result = data.tableData.length ? data.tableData : ''
    emit('update:modelValue', data.tableData)
    emit('change', data.tableData)
  })

  // 加载组件
  const initData = () => {
    if (props.modelValue && props.modelValue.length) {
      data.tableData = props.modelValue
    }
  }
  initData()

  const handleNumChange = (e, record, index) => {
    console.log('handleNumChange', e.target.value, record, index)
    data.tableData[index].tag_num = e.target.value
    data.tableData[index].tag_num =
      data.tableData[index].tag_num
        .replace(/^(0+)|[^\d.]/g, '')
        .replace(/^\./g, '')
        .replace(/\.{2,}/g, '')
        .replace('.', '$#$')
        .replace(/\./g, '')
        .replace('$#$', '.')
        .match(/^\d*(\.?\d{0,2})/g)[0] || null
    console.log('-=-=-=-=', data.tableData)
  }

  const handleBlurNum = (e, record, index) => {
    console.log('handleNumChange', e.target.value, record, index)
    if (!record.tag_num) {
      // message.warning('请输入标签数量')
      record.tag_num = e.target.value
    }
  }

  // 校验方法
  const onKeyup = (e) => {
    e.target.value = e.target.value.replace(
      /[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#¥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、～]/g,
      ''
    )
  }
  const tagNameValid = (value) => {
    if (!value) {
      // return new Error('请输入标签名称')
      return new Promise((resolve, reject) => {
        reject('请输入标签名称')
      })
    }
    if (noEmoji().test(value)) {
      // return new Error('请勿输入emoji表情')
      return new Promise((resolve, reject) => {
        reject('请勿输入emoji表情')
      })
    }
    // return true
    return Promise.resolve()
  }
  const tagNumValid = (value) => {
    if (value === '') {
      // return new Error('请输入标签数量')
      return new Promise((resolve, reject) => {
        reject('请输入标签数量')
      })
    }
    // return true
    return Promise.resolve()
  }

  // 添加
  const handleAddRow = () => {
    if (data.tableData.length > 7) {
      message.warning('最多只能有8个标签')
      return
    }
    data.tableData.push({ tag_name: '', tag_num: '' })
  }

  // 删除
  const handleDeleteRow = (row, index) => {
    console.log('handleDeleteRow', row)
    if (data.tableData.length < 2) {
      message.warning('至少保留1个标签')
      return
    }
    let list = []
    data.tableData.forEach((it, i) => {
      list.push(`temp[${i}].tag_name`)
      list.push(`temp[${i}].tag_num`)
    })
    formRef.value.clearValidate(list)
    data.tableData.splice(index, 1)
    list = null
    nextTick(() => {
      let nlist = []
      data.tableData.forEach((it, i) => {
        nlist.push(`temp[${i}].tag_name`)
        nlist.push(`temp[${i}].tag_num`)
      })
      formRef.value.validateFields(nlist)
      nlist = null
    })
  }
</script>

<style lang="scss" scoped>
  .good_reviews_label {
    :deep {
      .ant-form-item {
        margin-bottom: 0;
        position: relative;
        .ant-form-item-explain-error {
          position: absolute;
          top: 34px;
          font-size: 12px;
        }
      }
    }
    .label_title {
      line-height: 40px;
      height: 40px;
      color: #404040;
      font-weight: 500;
      font-size: var(--el-font-size-base);
      .label_title_dot {
        color: var(--el-color-danger);
        margin-right: 4px;
      }
    }
    .page_main_table {
      padding: 0;
      margin-top: 0;
      :deep(.ant-input-number) {
        width: 100%;
      }
    }
  }
</style>
