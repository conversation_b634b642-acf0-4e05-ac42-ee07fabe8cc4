<template>
  <div class="edit_wrapper">
    <a-card style="margin-bottom: 10px">
      <a-form
        :model="state.addForm"
        ref="addForm"
        :rules="ruleData"
        @validate="validateChange"
        class="demo_form pt-0px"
        :colon="false"
        @finish="submitForm(ruleForm)"
        :labelCol="{ style: 'width: 100px; display: inline-block; vertical-align: inherit;' }"
      >
        <!-- 微信小店 -->
        <weixin-shop
          v-if="state.isWeixinshop"
          id="weixinShop"
          ref="weixinShopRef"
          v-model="state.addForm"
          :isEdit="isEdit"
          @weixinProductHandler="weixinProductHandler"
        />
        <div class="header_title comm_header_title" id="info">基本信息</div>
        <!-- <a-form-item label="商品类型" name="type" :class="[addDiffClass(['type'])]">
          <a-radio-group
            v-model:value="state.addForm.type"
            :disabled="route.query?.id"
            :options="[
              { label: '普通商品', value: 1 },
              { label: '跨境商品', value: 2 }
            ]"
            @change="changeType"
          />
        </a-form-item> -->
        <a-form-item
          label="商品类目:"
          name="category_ids"
          id="category_ids"
          :class="[addDiffClass(['category_names'])]"
        >
          <a-space :size="[15, 0]" flex middle style="height: 100%">
            <div class="cate_name flex_align_center">您当前选择的是:{{ state.addForm.category_names }}</div>
            <AButton :ml="15" @click="backEmit"> 修改 </AButton>
          </a-space>
        </a-form-item>

        <div class="flex" v-if="isMedicalSpecificFn(Number(state.addForm?.category_ids?.[0]))">
          <a-form-item
            class="w-100%"
            label="医药资质:"
            name="shop_certificate_product"
            id="shop_certificate_product"
            :rules="[
              {
                required: isEdit && !state.back_shop_certificate_product_id ? false : true,
                validator: (_: any, value: any, callback: Function) => {
                  console.log('===========000000', value)
                  if (!value?.length && !(isEdit && !state.back_shop_certificate_product_id)) {
                    callback('请选择所属主体')
                  } else if (
                    value?.length &&
                    value?.[0] &&
                    !value?.[1] &&
                    !(isEdit && !state.back_shop_certificate_product_id)
                  ) {
                    callback('请选择医药类型')
                  } else if (
                    value?.length &&
                    value?.[0] &&
                    value?.[1] &&
                    !value?.[2] &&
                    !(isEdit && !state.back_shop_certificate_product_id)
                  ) {
                    callback('请选择备案产品名称')
                  } else {
                    callback()
                  }
                },
                trigger: ['change', 'blur']
              }
            ]"
            :class="[addDiffClass(['shop_certificate_product_id'])]"
          >
            <a-cascader
              v-model:value="state.addForm.shop_certificate_product"
              :options="medicalQualificationOptions"
              placeholder="请选择所属主体"
              @change="medicalQualificationChange"
              change-on-select
              :displayRender="displayRender"
            />
          </a-form-item>
          <a-button class="ml-15px" v-auth="['addCertification']" @click="gotoSubjectManagement">新增资质</a-button>
        </div>
        <div class="block">
          <a-form-item label="商品名称:" name="title" id="title" :class="addDiffClass(['title'])">
            <a-input
              v-model:value.trim="state.addForm.title"
              placeholder="请输入商品名称"
              :maxlength="64"
              showCount
              @change="() => (state.addForm.changeField = 'title')"
            />
            <div class="input_desc">商品名称最长不超过64个汉字，建议格式“品牌+商品名称+型号”；</div>
          </a-form-item>
        </div>

        <a-form-item label="详情页标题:" name="goods_sub_title" id="goods_sub_title">
          <a-input
            v-model:value.trim="state.addForm.goods_sub_title"
            placeholder="请输入详情页标题"
            :maxlength="64"
            @change="() => (state.addForm.changeField = 'goods_sub_title')"
          />
        </a-form-item>

        <a-form-item label="备注:" name="desc" id="desc">
          <a-input v-model:value.trim="state.addForm.desc" placeholder="备注仅限内部查看" :maxlength="64" />
        </a-form-item>

        <a-form-item
          label="推荐标签:"
          required
          :class="addDiffClass(['recommend_tag_color', 'recommend_tag_txt', 'recommend_tag'])"
          id="recommend_tag"
        >
          <div class="flex_align_center bottom flex-wrap">
            <a-form-item class="bottom mb_0" name="recommend_tag">
              <a-input
                v-model:value.trim="state.addForm.recommend_tag"
                placeholder="好物推荐"
                :maxlength="6"
                class="w220"
                @change="() => (state.addForm.changeField = 'recommend_tag')"
              />
              <div class="input_desc">请输入2-6个字符</div>
            </a-form-item>

            <a-form-item class="bottom mb_0" name="recommend_tag_txt" id="recommend_tag_txt">
              <a-input
                v-model:value.trim="state.addForm.recommend_tag_txt"
                placeholder="爆款热销，正品保障"
                :maxlength="15"
                class="w220"
                @change="() => (state.addForm.changeField = 'recommend_tag_txt')"
              />
              <div class="input_desc">请输入6-15个字符</div>
            </a-form-item>
          </div>
        </a-form-item>

        <a-form-item
          label="销售标签:"
          required
          :class="addDiffClass(['sale_tag_color', 'sale_tag_txt', 'sale_tag'])"
          id="sale_tag"
        >
          <div class="flex_align_center bottom flex-wrap">
            <a-form-item name="sale_tag" class="bottom mb_0">
              <a-input
                v-model:value.trim="state.addForm.sale_tag"
                placeholder="放心购"
                :maxlength="6"
                class="w220"
                @change="() => (state.addForm.changeField = 'sale_tag')"
              />
              <div class="input_desc">请输入2-6个字符</div>
            </a-form-item>

            <a-form-item name="sale_tag_txt" id="sale_tag_txt" class="bottom mb_0">
              <a-input
                v-model:value.trim="state.addForm.sale_tag_txt"
                placeholder="品质保证，极速售后"
                :maxlength="15"
                class="w220"
                @change="() => (state.addForm.changeField = 'sale_tag_txt')"
              />
              <div class="input_desc">请输入6-15个字符</div>
            </a-form-item>
            <div class="color_picker1 m-b-20px">
              <el-color-picker
                class="color_picker12"
                v-model="state.addForm.sale_tag_color"
                @change="handleSaleTagColorChange"
              />
              <span
                class="color"
                :style="{
                  background:
                    state.addForm.sale_tag_color == defaultStateColor.sale_tag_color
                      ? state.addForm.sale_tag_color
                      : 'transparent'
                }"
              ></span>
              <span class="font" @click="setDefaultColor(defaultStateColor.sale_tag_color, 'sale_tag_color')"
                >默认</span
              >
            </div>
          </div>
        </a-form-item>

        <div class="header_title comm_header_title" id="xiaoshou">销售信息</div>
        <a-form-item v-if="state.addForm.type === 2" label="税费:" name="taxes" :class="[addDiffClass(['taxes'])]">
          <div class="flex items-center">
            <a-radio-group v-model:value="state.addForm.taxes">
              <a-radio :value="1">免税</a-radio>
            </a-radio-group>
            <div class="ml-6px c-#85878a font-size-12px flex-y-center h-18px line-height-18px">
              <ExclamationCircleFilled />
              <span class="ml-6px mr-6px">平台目前暂只支持免税商品</span>
              <a-button class="p-0 font-size-12px h-18px line-height-18px" type="link" @click="onShowDialog('taxes')"
                >税费说明</a-button
              >
            </div>
          </div>
        </a-form-item>
        <a-form-item label="活动销售价:" name="price" id="price" :class="addDiffClass(['price'])">
          <a-input-number
            class="w-full"
            :min="0.01"
            :precision="2"
            v-model:value="state.addForm.price"
            :max="9999999999"
            placeholder="请输入商品实际售卖价格"
            @change="() => (state.addForm.changeField = 'price')"
          />
        </a-form-item>

        <a-form-item label="划线价:" name="old_price" id="old_price" :class="addDiffClass(['old_price'])">
          <div>
            <a-input-number
              class="w-full"
              :min="0"
              :precision="2"
              :max="9999999999"
              v-model:value="state.addForm.old_price"
              placeholder="非必填"
              @change="() => (state.addForm.changeField = 'old_price')"
            />
            <div class="input_desc">划线价在商品详情页仅做展示使用；</div>
          </div>
        </a-form-item>

        <!-- <a-form-item label="总库存:" name="stock_num">
          <a-input-number
            :disabled="state.addForm.is_unit == 1"
            v-model:value.trim="state.addForm.stock_num"
            placeholder="开启多规格时库存以多规格为准"
            :ontrols="false"
            :max="990000"
            style="width: 100%"
          />
        </a-form-item> -->

        <a-form-item label="商品编码:" name="product_code" id="product_code">
          <a-input :maxlength="100" v-model:value.trim="state.addForm.product_code" placeholder="请输入商品编码" />
        </a-form-item>
        <!-- v-if="!useInfo.is_private_platform" -->
        <a-form-item label="同步ERP:" name="erp_id">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="state.addForm.erp_id"
            placeholder="请输入名称"
            :showSearch="true"
            allowClear
            :filterOption="filterOption"
            @change="changeErp"
          >
            <a-select-option
              v-for="item in state.erpList"
              :key="item.id"
              :value="item.id"
              :name="item.name"
              :erp_id="item.erp_id == 1 ? '聚水潭' : item.erp_id == 2 ? '旺店通' : '店管家'"
              :erp_shop_name="item.erp_shop_name"
              :erp_shop_id="String(item.erp_shop_id)"
            >
              <span>{{
                `${item.erp_id == 1 ? '聚水潭' : item.erp_id == 2 ? '旺店通' : '店管家'} ${item.name || '-'}    ${
                  item.erp_shop_name || '-'
                }  ${item.erp_shop_id || '-'}`
              }}</span>
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="商品规格:"
          name="is_unit"
          id="is_unit"
          :class="
            state.addForm.is_unit == 1 && route.query?.is_temp !== '1'
              ? addDiffClass(['is_unit', 'sku_list', 'spec_list'])
              : []
          "
        >
          <div class="specs_box">
            <div class="switch flex_align_center">
              <span class="label">启用多规格</span>
              <a-switch
                :checkedValue="1"
                :unCheckedValue="2"
                v-model:checked="state.addForm.is_unit"
                @change="moreSkuSwitch"
              />
              <a-button
                v-if="state.addForm.is_unit == 1"
                type="primary"
                size="small"
                @click="openFullscreenSku"
                class="ml-15px"
              >
                全屏编辑
              </a-button>
            </div>
            <div class="input_desc">启用多规格后可自定义规格，规格名请填写如：颜色、尺码等；</div>
          </div>
        </a-form-item>

        <a-form-item label=" " :colon="false" name="sku" id="sku_list" v-if="state.addForm.is_unit == 1">
          <Sku :list="state.specList" ref="skuIptRef" @change="handleSkuChange" :key="state.timestamp" />
        </a-form-item>
        <a-form-item v-if="state.addForm.is_unit == 1 && state.addForm.skuData.list.length" id="sku_list_price">
          <SkuList
            :skuData="state.addForm.skuData"
            :addDiffClass="addDiffClass"
            :list="state.skuList"
            @changeValue="skuChangeValue"
            @delChange="delChange"
            ref="skuListRef"
            @onUpPicChange="onUpPicChange"
            @specListChange="handleSkuChange"
          />
        </a-form-item>
        <!-- 跨境信息 -->
        <CrossBorderInfo
          id="crossborderInfo"
          v-if="state.addForm.type === 2"
          v-model="state.addForm"
          :addDiffClass="addDiffClass"
        ></CrossBorderInfo>
        <a-form-item
          label="规格展现形式:"
          :rules="[{ required: true, message: '请选择规格展现形式', trigger: 'change' }]"
          name="sku_show_style"
          id="sku_show_style"
          :class="addDiffClass(['sku_show_style'])"
        >
          <a-radio-group v-model:value="state.addForm.sku_show_style">
            <a-radio :value="1">
              <Supernatant :type="9" />
            </a-radio>
            <a-radio :value="2">
              <Supernatant :type="8" />
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <div class="header_title comm_header_title" id="jieshao">商品介绍</div>
        <a-form-item label="主图视频:" name="product_video" :class="addDiffClass(['image_ids'])">
          <div class="flex flex-col">
            <!-- <div class="item flex" v-if="state.addForm.product_video">
              <video :src="state.addForm.product_video" controls="controls" class="imgg">
                您的浏览器不支持 video 标签。
              </video>
              <CloseCircleFilled size="16" color="#404040" class="del_img" @click="onDelVideo()" />
            </div>
            <div class="upload flex_center flex_column" @click="onShowDialog('video', 'productVideo')" v-else>
              <img src="@/assets/images/goods/add.png" class="add" />
              <span class="desc">上传视频</span>
            </div> -->
            <div class="upload-main">
              <div class="video_list flex">
                <div class="item flex" v-if="state.addForm.product_video">
                  <FilePreview
                    :src="state.addForm.product_video"
                    style="width: 102px; height: 160px"
                    :showPlay="[0, 4, 5].includes(state.addForm.product_video_status) ? false : true"
                    class="overflow-hidden"
                    :class="{ error: state.error }"
                  ></FilePreview>
                  <CloseCircleFilled size="16" color="#404040" class="del_img" @click="onDelVideo('goods')" />
                  <div class="item-mark flex-center" v-if="[0, 4, 5].includes(state.addForm.product_video_status)">
                    <SvgIcon class="w-24px h-24px" icon="video-tips" />

                    <span class="font-size-12px c-#fff mt-16px line-height-17px">{{
                      state.addForm.product_video_status == 4
                        ? '视频已下线'
                        : state.addForm.product_video_status == 5
                          ? '视频已禁用'
                          : '视频已失效'
                    }}</span>
                  </div>
                </div>
                <div class="upload flex_center flex_column" @click="onShowDialog('video')" v-else>
                  <!-- <img src="@/assets/images/goods/add.png" class="add" /> -->
                  <SvgIcon class="w-19px h-18px" icon="video" />
                  <span class="desc c-#7B808D">选择视频</span>
                </div>
              </div>
            </div>
            <div class="input_desc vaild_desc" style="margin-top: 0">
              商品视频将展示在商品详情页，为了更好的展示效果，请尽量按照要求上传；<br />
              1.建议上传主图视频尺寸9:16；<br />
              2.视频分辨率不能低于720p；<br />
              3.视频大小不能超过100M；<br />
              4.视频格式：mp4、mov、mkv；<br />
              5.视频时长：建议最佳视频长度为10秒-3分钟之内；
            </div>
          </div>
        </a-form-item>
        <a-form-item
          label="上传主图:"
          name="image_ids"
          id="image_ids"
          :rules="[{ required: true, validator: valid_imgs, trigger: ['change', 'blur'] }]"
          :class="addDiffClass(['image_ids'])"
        >
          <div class="image_list flex">
            <draggable
              :list="state.addForm.image_ids"
              class="flex_align_start flex_wrap"
              itemKey="element"
              @end="handleMainDrag"
            >
              <template #item="{ element, index }">
                <div>
                  <div
                    class="item flex"
                    :class="
                      state.err_image_ids.includes(element) && !state.confirm_images[element] && 'border-red border'
                    "
                  >
                    <AImage
                      style="height: 100%; width: 100%"
                      :src="element"
                      :preview="{ visible: false }"
                      @click="handleMainPreview(element, index)"
                    />
                    <div class="name" v-show="index == 0">商品主图</div>
                    <CloseCircleFilled
                      size="16"
                      color="#404040"
                      class="del_img"
                      @click="onDelImg(index, 'main', element)"
                    />
                  </div>
                  <a-checkbox
                    v-model:checked="state.confirm_images[element]"
                    @change="confirmImages($event, element, state.addForm.image_ids)"
                    v-if="state.err_image_ids.includes(element) && !state.politics_imgs.includes(element)"
                    class="mb-20px"
                    >确认无误</a-checkbox
                  >
                </div>
              </template>
              <template #footer v-if="state.addForm.image_ids.length < 9">
                <div class="upload flex_center flex_column" @click="onShowDialog('library', 'main')">
                  <img src="@/assets/images/goods/add.png" class="add" />
                  <span class="desc">上传图片</span>
                </div>
              </template>
            </draggable>
            <div style="display: none" :key="state.main_preview.detailFresh">
              <a-image-preview-group
                :preview="{
                  visible: state.main_preview.visible,
                  onVisibleChange: (vis: any) => (state.main_preview.visible = vis),
                  current: state.main_preview.index,
                  src: state.main_preview.src
                }"
              >
                <a-image v-for="(item, index) in state.addForm.image_ids" :src="item" :key="index" />
              </a-image-preview-group>
            </div>
          </div>
          <div class="input_desc vaild_desc" style="margin-top: 0">
            商品图片将展示在列表页与商品详情页，为了更好的展示效果，请尽量按照要求上传；<br />
            1.建议上传800x800像素以上图片，长宽比例为1:1；<br />
            2.图片支持格式：jpg\jpeg\png\bmp\PNG\JPEG\JPG\GIF；<br />
            3.为保障图片加载流畅，请上传小于5M的图片；<br />
            4.建议上传9张商品图片（正面图*1、俯视图*1、侧面图*1、局部细节图*1、使用场景图*1）；
          </div>
        </a-form-item>

        <a-form-item
          label="商品详情:"
          name="detail_image"
          id="detail_image"
          :rules="[{ validator: valid_imgs_detail, trigger: ['change', 'blur'] }]"
          :class="addDiffClass(['detail_image'])"
        >
          <div style="width: 100%">
            <div class="image_list flex">
              <draggable
                :list="state.addForm.detail_image"
                class="flex_align_start flex_wrap"
                itemKey="element"
                @end="handleDetailDrag"
              >
                <template #item="{ element, index }">
                  <div>
                    <div
                      class="item flex"
                      :class="
                        state.err_detail_image.includes(element) &&
                        !state.confirm_images[element] &&
                        'border-red border'
                      "
                    >
                      <AImage
                        style="height: 100%; width: 100%"
                        :src="element"
                        :preview="{ visible: false }"
                        @click="handleDetailPreview(element, index)"
                      />
                      <CloseCircleFilled size="16" color="#404040" class="del_img" @click="onDelImg(index, 'detail')" />
                    </div>
                    <a-checkbox
                      v-model:checked="state.confirm_images[element]"
                      @change="confirmImages($event, element, state.addForm.detail_image)"
                      v-if="state.err_detail_image.includes(element) && !state.politics_detail_imgs.includes(element)"
                      class="mb-20px"
                      >确认无误</a-checkbox
                    >
                  </div>
                </template>
                <template #footer v-if="state.addForm.detail_image.length < 50">
                  <div class="upload flex_center flex_column" @click="onShowDialog('library', 'detail')">
                    <img src="@/assets/images/goods/add.png" class="add" />
                    <span class="desc">上传图片</span>
                  </div>
                </template>
              </draggable>
              <div style="display: none" :key="state.detail_preview.detailFresh">
                <a-image-preview-group
                  :preview="{
                    visible: state.detail_preview.visible,
                    onVisibleChange: (vis: any) => (state.detail_preview.visible = vis),
                    current: state.detail_preview.index,
                    src: state.detail_preview.src
                  }"
                >
                  <a-image v-for="(item, index) in state.addForm.detail_image" :src="item" :key="index" />
                </a-image-preview-group>
              </div>
            </div>
            <div class="input_desc vaild_desc" style="margin-top: 10px">
              商品详情将展示在商品详情页，请上传与商品内容相关的图片，最多不超过50张，可自由拖拽图片排序；<br />
              1.建议使用宽度480～620像素、高度为960像素的图片；<br />
              2.图片支持格式：jpg\jpeg\png\bmp\gif\PNG\JPEG\JPG\GIF；<br />
              3.为保障图片加载流畅，请上传小于5M的图片；<br />
              4.上传商品图片+服务全方位（全景展示、场景展示、细节展示、物流展示、售后保障等方面）展示详情图，建议实拍+美工优化后上传；
            </div>
          </div>
        </a-form-item>

        <a-form-item label="商品属性:" id="attribute" :class="addDiffClass(['attribute'])">
          <attribute-list
            :contrabandList="state.contrabandList"
            v-model="state.addForm.attribute"
            @change="attributeChange"
          ></attribute-list>
        </a-form-item>

        <div class="header_title comm_header_title" id="wuliu">物流信息</div>
        <a-form-item label="商品运费:" required id="freight_type" class="freight">
          <a-radio-group v-model:value="state.addForm.freight_type" class="freight" @change="onChangeFreightType">
            <a-radio :value="1" class="reset_for_item mb_24"
              >商家承担运费
              <div v-if="state.addForm.freight_type == 1" class="c-#85878a font-size-12px">
                如该商品支持送礼物功能，仅在商家承担运费的地区内可收下礼物！
              </div>
            </a-radio>
            <a-radio :value="2" class="reset_for_item no_top">
              <a-form-item name="freight" :rules="ruleData.ruleFreight(state.addForm.freight_type)">
                <span class="flex-center">
                  <span class="freight_type_child_label">固定运费</span>
                  <a-input-number
                    v-model:value="state.addForm.freight"
                    :min="1"
                    :max="999"
                    :precision="0"
                    :controls="false"
                    placeholder="请输入运费"
                    class="input_number"
                  />
                </span>
                <div v-if="state.addForm.freight_type == 2" class="c-#85878a font-size-12px mt-4px">
                  如该商品支持送礼物功能，仅在商家承担运费的地区内可收下礼物！
                </div>
              </a-form-item>
            </a-radio>
            <a-radio :value="3" class="mt10 reset_for_item moban">
              <a-form-item name="deliver_temp_id" :rules="ruleData.ruleFreightTemp(state.addForm.freight_type)">
                <div class="flex_align_center">
                  <span class="freight_type_child_label">运费模版</span>
                  <div class="areaUndeliver" @click.prevent.stop>
                    <a-select
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                      v-model:value="state.addForm.deliver_temp_id"
                      placeholder="请选择运费模版"
                      clearable
                      filterable
                      @dropdownVisibleChange="onVisibleChangeUndeliver"
                    >
                      <a-select-option v-for="item in state.areasNos" :key="item.value" :value="item.value">{{
                        item.label
                      }}</a-select-option>
                    </a-select>

                    <div class="input_desc">
                      可以前往运费模版页面添加模版；
                      <span class="set" @click="toAreaNo">去设置</span>
                    </div>
                  </div>
                </div>
              </a-form-item>
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="寄件地址:" name="send_address_id" :class="addDiffClass(['send_address_id'])">
          <div class="flex">
            <a-select
              :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
              v-model:value="state.addForm.send_address_id"
              placeholder="请选择寄件地址"
              allowClear
            >
              <a-select-option v-for="item in state.afterSaleAddress" :key="item.id" :value="item.id">
                {{ `${item.name},${item.phone},${item.area_name}${item.address}` }}
              </a-select-option>
            </a-select>
            <a-button type="link" @click="toShopAddress">管理地址</a-button>
          </div>
          <template #extra>
            <div class="font-size-12px mt-8px">
              使用<span class="c-#FE4D4F">顺丰物流</span>或<span class="c-#FE4D4F">隐私面单</span>时，该项必填，否则
              <span class="c-#FE4D4F">无法查看物流信息</span>
            </div>
          </template>
        </a-form-item>
        <a-form-item
          label="售后地址:"
          name="shop_address_id"
          id="shop_address_id"
          :class="addDiffClass(['shop_address_id'])"
          :rules="[{ required: true, message: '请选择售后地址', trigger: ['blur', 'change'] }]"
        >
          <a-select
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
            v-model:value="state.addForm.shop_address_id"
            placeholder="请选择售后地址"
            allowClear
          >
            <a-select-option v-for="item in state.afterSaleAddress" :key="item.id" :value="item.id">
              <!-- <a-descriptions :column="1" :colon="false">
                  <a-descriptions-item>{{ item.name }},{{ item.phone }},{{ item.area_name }}</a-descriptions-item>
                </a-descriptions> -->
              <div class="flex">
                {{ `${item.name},${item.phone},${item.area_name}${item.address} ` }}
                <div class="ml-30px flex">
                  <a-tooltip>
                    <template #title> {{ `${item.warehouse_name}` }} </template>
                    <div class="text_overflow max-w-240px">{{ `${item.warehouse_name}` }}</div>
                  </a-tooltip>
                  <a-tooltip>
                    <template #title> {{ `${item.warehouse_desc}` }} </template>
                    <div class="ml-20px">{{ `${item.warehouse_desc}` }}</div>
                  </a-tooltip>
                </div>
              </div>
            </a-select-option>
          </a-select>
          <template #extra>
            <!-- 若未选择售后地址，则寄回地址使用商家默认地址。 -->
            <a-button class="pa-0" type="link" @click="toShopAddress">管理地址</a-button>
            <a-button class="pa-0" type="link" @click="getSaleAddressList(true)">刷新</a-button>
          </template>
        </a-form-item>
        <!-- <a-form-item label="非配送区域:">
            <div class="areaUndeliver">
              <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="state.addForm.undeliver_id" placeholder="请选择非配送区域模板（非必选）" clearable filterable
                @visible-change="onVisibleChangeUndeliver">
                <a-select-option v-for="item in state.areasNos" :key="item.value"  :value="item.value">{{ item.label }}</a-select-option>
              </a-select>
              <div class="input_desc">
                可以前往非配送区域页面添加模板；
                <span class="set" @click="toAreaNo">去设置</span>
              </div>
            </div>
          </a-form-item> -->

        <!-- 评论设置 -->
        <div class="header_title comm_header_title mb0" id="comment">评论设置</div>
        <a-form-item label="评论组:" name="comment_temp_id" id="comments" :class="addDiffClass(['comment_temp_id'])">
          <a-select
            v-model:value="state.addForm.comment_temp_id"
            placeholder="请选择评论组"
            allowClear
            showSearch
            :filter-option="false"
            @search="commentRemoteMethod"
            :loading="state.commentLoading"
            @change="changeComment"
            :key="state.tag_list_flag"
          >
            <a-select-option v-for="item in state.commentGroups" :key="item.id" :value="item.id">{{
              item.name
            }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label=" " v-if="state.addForm.comment_temp_id" id="tag_list">
          <GoodReviewsLabel
            :contrabandList="state.contrabandList"
            v-model="state.addForm.tag_list"
            @change="onTagsChange"
            :key="state.tag_list_flag"
          />
        </a-form-item>
        <a-form-item label="问答组:" name="question_temp_id" id="question" :class="addDiffClass(['question_temp_id'])">
          <a-select
            v-model:value="state.addForm.question_temp_id"
            placeholder="请选择问答组"
            allowClear
            showSearch
            :filter-option="false"
            @search="qaRemoteMethod"
            :loading="state.qaLoading"
            @change="changeQuestion"
          >
            <a-select-option v-for="item in state.qaGroups" :key="item.id" :value="item.id">{{
              item.name
            }}</a-select-option>
          </a-select>
        </a-form-item>
        <div class="header_title comm_header_title mb0" id="pay">支付设置</div>

        <a-form-item id="limit_type" class="mb_10">
          <template #label>
            <div class="flex-y-center">
              <span>限购方式</span>
              <a-tooltip>
                <template #title> 商品自然流量及广告引流订单，均生效限购设置 </template>
                <img :src="requireImg('goods/bangzhu.png')" class="w-12px h-12px cursor-pointer ml-3px" /> </a-tooltip
              >:
            </div>
          </template>
          <a-radio-group v-model:value="state.addForm.limit_type" @change="onGroup">
            <a-radio :value="1" size="large">不限购</a-radio>
            <a-radio :value="2" size="large">限购</a-radio>
          </a-radio-group>
          <template v-if="state.addForm.limit_type == 2">
            <div class="flex-y-center">
              营销文案
              <a-tooltip placement="right">
                <template #title>
                  <img :src="requireImg('goods/goods-sku.png')" />
                </template>
                <img :src="requireImg('goods/bangzhu.png')" class="w-12px h-12px cursor-pointer ml-3px" /> </a-tooltip
              >：
              <a-input
                :maxlength="10"
                v-model:value.trim="state.addForm.limit_desc"
                style="width: 230px"
                placeholder="降价补贴，限购X件"
              />
            </div>

            <div class="flex-y-center mt-8px flex-wrap">
              每次最多购买：
              <a-input-number
                v-model:value="state.addForm.limit_once_max_num"
                :min="1"
                :max="9999999999"
                :precision="0"
                :controls="false"
                class="input_number w-120px"
              />件 <span class="mr-24px"></span>累计最多购买：
              <a-input-number
                v-model:value="state.addForm.limit_total_num"
                :min="1"
                :max="9999999999"
                :precision="0"
                :controls="false"
                class="input_number w-120px"
              />
              件 <span class="mr-24px"></span>每次最少购买：
              <a-input-number
                v-model:value="state.addForm.limit_once_min_num"
                :min="1"
                :max="9999999999"
                :precision="0"
                :controls="false"
                class="input_number w-120px"
              />件
            </div>
          </template>
        </a-form-item>
        <div class="header_title comm_header_title mb0" id="fangan">营销方案</div>
        <div class="input_desc pl5">设置合理的营销方案，可提高用户的下单转化率</div>

        <a-form-item label="销量:" id="sales_type" class="mb_10">
          <a-radio-group v-model:value="state.addForm.sales_type" @change="onGroup">
            <a-radio :value="1" size="large">累计真实销量</a-radio>
            <a-radio :value="2" size="large">不展示销量</a-radio>
            <a-radio :value="3" size="large">
              全渠道累计真实销量
              <a-form-item
                name="sales_initial"
                id="sales_initial"
                :rules="{
                  validator: validSalesType
                }"
                class="inline_block mb_0"
              >
                <a-input-number
                  v-model:value="state.addForm.sales_initial"
                  :max="100000"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入真实销量"
                  class="input_number min_w_200"
                  v-if="state.addForm.sales_type === 3"
                />
              </a-form-item>
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="跑马灯样式:" name="rolling" id="rolling" class="mb_10">
          <a-radio-group v-model:value="state.addForm.rolling" @change="() => (state.addForm.changeField = 'rolling')">
            <a-radio :value="1" size="large" class="flex-center">
              <!-- 主图浮层跑马灯 -->
              <Supernatant :type="useInfo.is_private_platform ? 1 : 0" />
            </a-radio>
            <!-- <a-radio :value="2" size="large">
              <Supernatant :type="1" />
            </a-radio> -->
            <a-radio :value="3" size="large"> 关闭跑马灯 </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="限时活动:" name="limit_active" id="limit_active" class="mb_10">
          <a-radio-group
            v-model:value="state.addForm.limit_active"
            @change="
              () => {
                validateChange('active_desc', state.addForm.limit_active != 1 || state.addForm.active_desc.length >= 1)
                state.addForm.changeField = 'limit_active'
              }
            "
          >
            <a-radio :value="1" size="large">
              <Supernatant :type="7" />
            </a-radio>
            <!-- <a-radio :value="2" size="large">
              <Supernatant :type="3" />
            </a-radio> -->
          </a-radio-group>
        </a-form-item>

        <!-- <a-form-item
          label="活动文案:"
          name="active_desc"
          id="active_desc"
          :class="addDiffClass(['active_desc'])"
          v-if="state.addForm.limit_active == 1"
        >
          <a-input
            v-model:value.trim="state.addForm.active_desc"
            :maxlength="15"
            placeholder="如：限时抢购"
            @change="() => (state.addForm.changeField = 'active_desc')"
          />
        </a-form-item> -->
        <a-form-item label="按钮文案:" name="button_text" id="button_text" :class="addDiffClass(['button_text'])">
          <a-input
            v-model:value.trim="state.addForm.button_text"
            :maxlength="4"
            placeholder="请输入按钮文案"
            @change="() => (state.addForm.changeField = 'button_text')"
          />
        </a-form-item>

        <a-form-item label="购物保障:" name="ensure" id="ensure">
          <div>
            <a-radio-group v-model:value="state.addForm.ensure" @change="() => (state.addForm.changeField = 'ensure')">
              <a-radio :value="2" size="large">不显示</a-radio>
              <a-radio :value="1" size="large">显示</a-radio>
            </a-radio-group>
            <div class="input_desc mt3">
              腾讯官方要求，必须展示“隐私协议”/“用户协议”，需用户主动点击确认。非广告审核需要，不建议展示；
            </div>
          </div>
        </a-form-item>

        <a-form-item
          label="添加到店铺首页:"
          :labelCol="{ style: 'width: 105px;' }"
          name="is_brand_shop"
          id="is_brand_shop"
          class="h_auto"
          :class="addDiffClass(['is_brand_shop'])"
        >
          <a-radio-group
            v-model:value="state.addForm.is_brand_shop"
            @change="() => (state.addForm.changeField = 'is_brand_shop')"
          >
            <a-radio :value="1">显示</a-radio>
            <a-radio :value="0">不显示</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          label="店铺入口:"
          :labelCol="{ style: 'width: 105px;' }"
          name="is_show_shop"
          id="is_show_shop"
          class="h_auto"
          :class="addDiffClass(['is_show_shop'])"
        >
          <a-radio-group
            v-model:value="state.addForm.is_show_shop"
            @change="() => (state.addForm.changeField = 'is_show_shop')"
          >
            <a-radio :value="1">显示</a-radio>
            <a-radio :value="2">不显示</a-radio>
          </a-radio-group>
        </a-form-item>
        <CashBackAfterSale :addDiffClass="addDiffClass" v-model="state.addForm" />
        <a-form-item
          v-if="shopInfo?.send_gift_switch == 1 && !state.isWeixinshop && state.addForm.type == 1"
          :labelCol="{ style: 'width: 115px;' }"
          name="send_gift_switch"
          id="send_gift_switch"
          class="h_auto"
          :class="addDiffClass(['send_gift_switch'])"
        >
          <template #label>
            <div class="flex-y-center">
              <span>送礼物</span>
              <a-tooltip>
                <template #title> 开启后，商品详情页将对用户展示送礼物功能 </template>
                <QuestionCircleFilled class="c-#C5C6CC ml-4px font-size-12px" />
              </a-tooltip>
            </div>
          </template>
          <a-radio-group
            v-model:value="state.addForm.send_gift_switch"
            @change="() => (state.addForm.changeField = 'send_gift_switch')"
          >
            <a-radio :value="1">开启</a-radio>
            <a-radio :value="0">关闭</a-radio>
          </a-radio-group>
        </a-form-item>

        <div class="header_title comm_header_title mb0" id="fuwu">服务保障</div>
        <div class="input_desc pl5">
          据90%的消费者反映：<span class="e6">服务保障</span>与<span class="e6">时效</span>直接决定他们是否选择长期购买
        </div>
        <a-form-item
          label=" "
          :colon="false"
          :labelCol="{ style: 'width: 0;' }"
          name="protection"
          id="protection"
          style="margin-bottom: 0px"
        >
          <div v-for="(item, index) in guaranteeData" :key="index" class="guarantee-item">
            <a-checkbox
              class="guarantees"
              v-model:checked="state.protectionObj[item.value]"
              :disabled="item.disabled"
              @change="protectionChange(item.value, $event)"
            >
              <div class="label">{{ item.label }}</div>
              <div class="desc">{{ item.desc }}</div>
              <template v-if="item.value == 2">
                <div class="label mt-5px">发货时效</div>
                <div class="deliver_radio flex_align_center">
                  <a-radio-group v-model:value="state.addForm.send_time" @change="changeHours">
                    <a-radio :value="4" size="large">12H</a-radio>
                    <a-radio :value="1" size="large">24H</a-radio>
                    <a-radio :value="2" size="large">48H</a-radio>
                    <a-radio :value="3" size="large">72H</a-radio>
                  </a-radio-group>
                  <div class="desc2">发货</div>
                </div>
                <div class="desc">
                  商家已经开通{{
                    sendTimeFilter(state.addForm.send_time)
                  }}小时发货延必赔服务，承诺在支付成功次日零时起{{
                    sendTimeFilter(state.addForm.send_time)
                  }}小时内为您发货，如商家未能如约发货，买家可在此笔订单中申请违约赔付；
                </div>
                <!-- 下架春节停发-暂时注释 -->
                <template v-if="false">
                  <a-checkbox
                    class="mb-6px! mt-20px min-h-20px!"
                    v-model:checked="state.addForm.stopNewYearSend"
                    @change="stopNewYearSendChange"
                  >
                    <span :class="addDiffClass(['year_stop_send'])">春节停发</span>
                  </a-checkbox>
                  <div class="stop-newYear-send-wrapper" v-if="state.addForm.stopNewYearSend">
                    <a-space :size="6">
                      <a-form-item name="stopNewYearSendDate" :rules="stopNewYearSendDateRule">
                        <a-range-picker
                          :class="addDiffClass(['stop_start_time', 'stop_end_time'])"
                          v-model:value="state.addForm.stopNewYearSendDate"
                          :disabled-date="disabledDateHandler"
                          format="YYYY-MM-DD"
                          valueFormat="YYYY-MM-DD"
                          @change="datePickerChange"
                        >
                          <template #separator>
                            <span class="c-#404040">至</span>
                          </template>
                        </a-range-picker>
                      </a-form-item>
                      <a-form-item class="c-#404040" :class="addDiffClass(['stop_start_time', 'stop_end_time'])"
                        >不发货</a-form-item
                      >
                    </a-space>
                    <div class="desc flex">
                      <span>配置完停发时段后，根据商品发货时效往前倒推并在商品详情页展示；</span>
                      <a-tooltip color="#FFFFFF">
                        <template #title>
                          <a-image :src="requireImg('supernatant/newYearSend.png')"></a-image>
                        </template>
                        <span class="exmple">示例</span>
                      </a-tooltip>
                    </div>
                  </div>
                </template>
              </template>
            </a-checkbox>
          </div>
        </a-form-item>
        <div v-if="!state.isWeixinshop && shopInfo?.auth_phone == 1">
          <div class="header_title comm_header_title mb0" id="phoneVerify">下单验证</div>
          <a-form-item class="other_style">
            <template #label>
              <div class="flex w-124px items-center">
                <span class="flex-y-center">下单验证手机号</span>
                <a-tooltip>
                  <template #title>开启后,该商品用户下单时会验证手机号</template>
                  <img
                    :src="requireImg('goods/bangzhu.png')"
                    class="w-12px h-12px cursor-pointer ml-3px mr-3px"
                  /> </a-tooltip
                >:
              </div>
            </template>
            <a-radio-group v-model:value="state.addForm.auth_phone">
              <a-radio :value="1" size="large">是</a-radio>
              <a-radio :value="2" size="large">否</a-radio>
            </a-radio-group>
          </a-form-item>
        </div>

        <UserNotice
          :addDiffClass="addDiffClass"
          class="mb-0px"
          :userNotice="state.addForm.user_notice"
          ref="userNoticeRef"
        />
        <!-- 代码仅隐藏不生效，暂不删除 -->
        <!-- <div class="header_title mb0" id="store">腾讯广告商品库</div>
        <div class="input_desc pl5">
          应腾讯广告要求，投放前需将商品信息接入腾讯商品库，请填写下方内容完成接入（非腾讯广告投放，请忽略此项）
        </div>
        <a-form-item label="投放设置:" name="protection" id="protection" style="margin-bottom: 0px">
          <a-checkbox v-model:checked="state.addForm.is_async_gdt" :disabled="disabledAsync"
            >是否同步至微信商品库</a-checkbox
          >
        </a-form-item>
        <template v-if="state.addForm.is_async_gdt">
          <a-form-item
            label="商品库:"
            name="gdt_library_id"
            id="gdt_library_id"
            :rules="[{ required: true, message: '请选择商品库' }]"
          >
            <a-select
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              v-model:value="state.addForm.gdt_library_id"
              filterable
              allowClear
              placeholder="请选择商品库"
              popper-class="select_show_id"
              show-search
              :filter-option="handleAdFilterOption"
            >
              <a-select-option
                v-for="v in state.goodsStoreList"
                :key="v.id"
                :value="v.gdt_library_id"
                :account_name="v.account_name"
              >
                <div>
                  <div>ID：{{ v.gdt_library_id }}</div>
                  <div>备注：{{ v.remark }}</div>
                </div>
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            label="微信类目:"
            name="gdt_category_id"
            id="gdt_category_id"
            :rules="[{ required: true, message: '请选择微信类目' }]"
          >
            <a-cascader
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              v-model:value="state.addForm.gdt_category_id"
              :field-names="{ label: 'name', value: 'id', children: 'children' }"
              :options="state.cateList"
              :show-search="true"
              :filterOption="onFiterOption"
              placeholder="请选择微信类目"
              @change="handleCascaderChange"
            />
          </a-form-item>

          <a-form-item label="商品资质:" name="gdt_aptitude_img" id="gdt_aptitude_img">
            <div style="width: 100%">
              <div class="image_list flex">
                <draggable
                  :list="state.addForm.gdt_aptitude_img"
                  class="flex_align_center flex_wrap"
                  itemKey="element"
                  @end="handleDetailDrag"
                >
                  <template #item="{ element, index }">
                    <div class="item flex">
                      <AImage
                        style="height: 100%; width: 100%"
                        :src="element.img"
                        :preview="{ visible: false }"
                        @click="handleDetailPreview(element, index)"
                      />

                      <CloseCircleFilled
                        size="16"
                        color="#404040"
                        class="del_img"
                        @click="onDelImg(index, 'aptitude')"
                      />
                      <div class="item-type">{{ cardInfo[element.card_type] }}</div>
                    </div>
                  </template>
                  <template #footer>
                    <div class="upload flex_center flex_column" @click="onShowDialog('aptitude', 'detail')">
                      <img src="@/assets/images/goods/add.png" class="add" />
                      <span class="desc">上传资质</span>
                    </div>
                  </template>
                </draggable>
                <div style="display: none" :key="state.detail_preview.detailFresh">
                  <a-image-preview-group
                    :preview="{
                      visible: state.detail_preview.visible,
                      onVisibleChange: (vis: any) => (state.detail_preview.visible = vis),
                      current: state.detail_preview.index,
                      src: state.detail_preview.src
                    }"
                  >
                    <a-image v-for="(item, index) in state.addForm.gdt_aptitude_img" :src="item.img" :key="index" />
                  </a-image-preview-group>
                </div>
              </div>
              <div class="input_desc vaild_desc" style="margin-top: 10px">请选择添加商品资质</div>
            </div>
          </a-form-item>
        </template> -->
        <a-form-item class="text-left" label="" name="isHistory">
          <a-checkbox v-model:checked="state.addForm.isHistory" class="mt-16px" @change="changeIsHistory"
            >审核通过后存为历史版本</a-checkbox
          >
        </a-form-item>
      </a-form>
    </a-card>
    <div class="right_box">
      <Preview
        ref="previewRef"
        :ruleStatus="state.ruleStatus"
        :info="state.addForm"
        :isCate="isCate"
        :loading="state.previewLoading"
        @preview="handleFromPreview"
      />
    </div>
    <a-modal
      :title="state.dialog.title"
      v-model:open="state.dialog.visible"
      :width="state.dialog.width"
      :footer="null"
      :bodyStyle="{ marginTop: '10px' }"
      destroyOnClose
      @cancel="modalCancel"
    >
      <MaterialLibrary
        v-if="state.dialog.library === 'library'"
        :type="state.dialog.type"
        from="goods"
        @event="onEvent"
        :size="imgTypeSize"
        :fileCheckedList="
          state.dialog.imgType === 'main'
            ? state.addForm.image_ids.map((v) => {
                return { file_url: v }
              })
            : state.dialog.imgType === 'productVideo'
              ? state.product_videos.map((v) => {
                  return { file_url: v }
                })
              : state.addForm.detail_image.map((v) => {
                  return { file_url: v }
                })
        "
      />
      <AptitudeLibrary
        v-if="state.dialog.type === 'aptitude'"
        :list="state.addForm.gdt_aptitude_img"
        @event="onEvent"
      />
      <taxesDesc v-if="state.dialog.type === 'taxesDesc'"></taxesDesc>
      <VideoList v-if="['video'].includes(state.dialog.type)" @event="onEvent" />
      <HistoryVersions v-if="['versions'].includes(state.dialog.type)" @event="onEventVersions" />
    </a-modal>

    <!-- 全屏SKU编辑弹窗 -->
    <a-modal
      v-model:open="state.fullscreenSkuVisible"
      :width="'100vw'"
      :style="{ top: 0, paddingBottom: 0 }"
      :bodyStyle="{ height: '100vh', padding: '20px', overflow: 'auto' }"
      :footer="null"
      :closable="false"
      :maskClosable="false"
      destroyOnClose
      wrapClassName="fullscreen-sku-modal"
    >
      <div class="fullscreen-sku-container">
        <div class="fullscreen-sku-content">
          <div class="header_title comm_header_title mb-20px">商品规格设置</div>

          <!-- SKU规格组件 -->
          <div class="mb-20px">
            <Sku
              :list="state.fullscreenSpecList"
              ref="fullscreenSkuIptRef"
              @change="handleFullscreenSkuChange"
              :key="state.fullscreenTimestamp"
            />
          </div>

          <!-- SKU价格列表组件 -->
          <div v-if="state.fullscreenSkuData.list && state.fullscreenSkuData.list.length">
            <SkuList
              :skuData="state.fullscreenSkuData"
              :addDiffClass="addDiffClass"
              :list="state.fullscreenSkuList"
              @changeValue="handleFullscreenSkuChangeValue"
              @delChange="handleFullscreenDelChange"
              ref="fullscreenSkuListRef"
              @onUpPicChange="handleFullscreenUpPicChange"
              @specListChange="handleFullscreenSkuChange"
            />
          </div>
        </div>

        <!-- 底部关闭按钮 -->
        <div class="fullscreen-footer">
          <a-button type="primary" size="large" @click="closeFullscreenSku"> 关闭全屏 </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { setCategoryList } from '../goodList/index.api'
  import Preview from './components/Preview.vue'
  import VideoList from '../goodList/components/VideoList.vue'
  import HistoryVersions from './components/HistoryVersions.vue'
  // import Supernatant from './Supernatants.vue'
  // import Sku from '../goodList/components/Sku.vue'
  // import SkuList from '../goodList/components/SkuList.vue'
  // import GoodReviewsLabel from './components/GoodReviewsLabel.vue'
  // import MaterialLibrary from '@/components/ui/common/MaterialLibrary/Index.vue'
  import AptitudeLibrary from './components/AptitudeLibrary.vue'
  import taxesDesc from './components/taxesDesc.vue'
  import CrossBorderInfo from './components/crossBorderInfo.vue'
  import UserNotice from './components/UserNotice.vue'
  import CashBackAfterSale from './components/CashBackAfterSale.vue'
  import {
    onMounted,
    onActivated,
    onDeactivated,
    reactive,
    ref,
    onBeforeUnmount,
    watch,
    nextTick,
    computed,
    defineAsyncComponent,
    provide,
    ComputedRef,
    h
  } from 'vue'
  import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
  import { message, Modal } from 'ant-design-vue'
  import { CloseCircleFilled, QuestionCircleFilled, ExclamationCircleFilled } from '@ant-design/icons-vue'
  import { ElColorPicker } from 'element-plus'
  import 'element-plus/es/components/color-picker/style/css'
  import draggable from 'vuedraggable'
  // import { setCategoryList } from '../goodList/index.api'
  import { cloneDeep, isObject } from 'lodash-es'
  import { getImageInfoByPath, scrollToElement, requireImg } from '@/utils'
  import { useTheme, useGlobalLoading, useRouterBack, useApp } from '@/hooks'
  import { authList } from '@/api/common'
  import {
    setList,
    getQuestionTempList,
    getCommentTempList,
    shopAreaList,
    getCategoryList,
    getGdtLibraryList,
    checkString,
    getMedicalQualificationListApi,
    product_check_image
  } from './index.api'
  import {
    createProduct,
    setProductInfo,
    editProduct,
    getExamineDetailApi,
    get_product_temp_info,
    saveVersion
  } from '../goodList/index.api'

  import { DataDiff, addClass } from './src/diff.ts'
  import { queryCommentInfo, queryQAInfo } from './src/tidyDataStructure'
  import rules from './src/rules'
  import datas from '../goodList/src/datas'
  import skuFun from './src/skuFun.ts'
  import dayjs, { Dayjs } from 'dayjs'
  // import errorImg from '@/assets/images/error_img.png'
  // import deleteImg from '@/assets/images/goods/delete.png'
  import weixinShop from './components/weixinShop.vue'
  import { findDisabledByPath, processOptions } from './src/pardonCategory'
  // 设置异步组件
  const Supernatant = defineAsyncComponent(() => import('./Supernatants.vue'))
  const Sku = defineAsyncComponent(() => import('./components/Sku2.0.vue'))
  const SkuList = defineAsyncComponent(() => import('./components/SkuList2.0.vue'))
  const GoodReviewsLabel = defineAsyncComponent(() => import('./components/GoodReviewsLabel.vue'))
  const MaterialLibrary = defineAsyncComponent(() => import('@/components/ui/common/MaterialLibrary/Index.vue'))
  const AttributeList = defineAsyncComponent(() => import('./components/AttributeList.vue'))
  // import overallStore from '@/store/data.js'
  const { themeVar } = useTheme()
  const { setGlobalLoading } = useGlobalLoading()

  const route = useRoute()
  const router = useRouter()
  const { useInfo, shopInfo } = useApp()

  const { routerBack } = useRouterBack()
  const props = defineProps(['goodsClass'])
  const emit = defineEmits(['event'])
  // const store = overallStore()
  const addForm = ref(null)
  const isCate = ref(false)
  const skuListRef = ref(null)
  const skuIptRef = ref()
  const previewRef = ref(null)
  const userNoticeRef = ref()
  const weixinShopRef = ref()

  // 全屏SKU编辑相关ref
  const fullscreenSkuListRef = ref(null)
  const fullscreenSkuIptRef = ref()

  // 设置默认值  状态默认值
  const defaultStateColor = {
    sale_tag_color: '#774535',
    recommend_tag_color: 'linear-gradient(90deg, #ff9000 0%, #ff5000 100%)'
  }
  const cardInfo = {
    1: '食品经营许可证',
    2: '图书经营许可证',
    3: '医疗器械许可证'
  }
  const setDefaultColor = (color, filed) => {
    state.addForm[filed] = color
  }
  // 是否是编辑
  const isEdit: ComputedRef<boolean> = computed(() => {
    return route.query.id ? true : false
  })
  const medicalQualificationOptions = ref([])
  const getMedicalQualificationList = async () => {
    let res = await getMedicalQualificationListApi()
    medicalQualificationOptions.value =
      res.data?.map((it: any) => {
        return {
          ...it,
          label: it.shop_entity_name,
          value: it.shop_entity_id,
          children: it.children?.map((item: any) => {
            return (
              {
                ...item,
                label: h(
                  'span',
                  {
                    style: {}
                  },
                  [
                    item.certificate_title && h('div', {}, item.certificate_title), // 如果第一行不为空，则渲染
                    h('div', { style: { fontSize: '12px', color: '#979797' } }, item.certificate_name) // 始终渲染第二行，字号12px
                  ]
                ),
                value: item.certificate_id,
                children:
                  item.children?.map((k: any) => {
                    return {
                      ...k,
                      label: k.product_name + '(备案号：' + k.product_record_no + ')',
                      value: k.product_id
                    }
                  }) || []
              } || []
            )
          })
        }
      }) || []
  }

  getMedicalQualificationList()
  const medicalQualificationChange = (val) => {
    if (!val) {
      state.addForm.shop_certificate_product_id = undefined
      return
    }
    if (val?.length >= 3) {
      state.addForm.shop_certificate_product_id = val[val.length - 1]
    }
  }
  // 使用displayRender自定义文本渲染
  const displayRender = (label: any) => {
    // 格式化需要显示的文本
    const certificateTitle = label.labels[0] || ''
    const certificateName = label.labels.length > 1 ? label.selectedOptions[1]?.certificate_name + '/' : ''
    const productDetails = label.labels.length > 2 ? label.labels[2] : ''
    return `${certificateTitle}/ ${certificateName} ${productDetails}`
  }
  const gotoSubjectManagement = () => {
    // window.open('/goods/freighttemp', '_blank')
    const to = router.resolve({
      name: 'SubjectManagement'
    })
    window.open(to.href, '_blank')
  }
  const changeComment = async (val) => {
    try {
      if (!val) {
        state.addForm.tag_list = []
        delete state.addForm.comment
      } else {
        if (state.addForm.comment_temp_id) {
          const comment = await queryCommentInfo(state.addForm.comment_temp_id)
          state.addForm.comment = comment
        }
        state.addForm.tag_list = tag_list
        setTimeout(() => {
          nextTick(() => {
            state.tag_list_flag = Date.now()
          })
        }, 500)
      }
      state.addForm.changeField = 'comment_temp_id'
    } catch (error) {
      console.error(error)
    }
  }
  const changeQuestion = async (val) => {
    try {
      if (!val) {
        delete state.addForm.question
      } else {
        if (state.addForm.question_temp_id) {
          const question = await queryQAInfo(state.addForm.question_temp_id)
          state.addForm.question = question
        }
      }
      state.addForm.changeField = 'question_temp_id'
    } catch (error) {
      console.error(error)
    }
  }
  // 监听 tag_list 变化
  const onTagsChange = (value) => {
    state.addForm.changeField = 'tag_list'
    console.log('onTagsChange', value)
  }
  const changeHours = (val) => {
    state.addForm.changeField = 'send_time'
    if ([1, 4].includes(val.target.value)) {
      const hours = val.target.value == 1 ? 24 : 12
      Modal.info({
        title: '提示',
        okText: '我已知晓',
        content: `您选择${hours}小时发货，如您未在时间内发货，平台将对超时发货的商品统一自动退款，请您谨慎选择正确合理的发货时间。`,
        onOk() {
          Modal.destroyAll()
        }
      })
    }
  }
  // 导入服务保障
  const { guaranteeData, tag_list } = datas()

  // 判断商品分类归属医疗
  const isMedical = computed(() => {
    if (props?.goodsClass?.ids) {
      return [5539, 188104, 188105, 188106, 188107].includes(props.goodsClass?.ids[0]) ? true : false
    }
    return false
  })
  // 判断商品分类归属医疗
  const isMedicalFn = (value: number) => {
    if (value) {
      return [5539, 188104, 188105, 188106, 188107].includes(value) ? true : false
    }
    return false
  }
  // 判断商品分类是否是医药健康 OTC药品 医疗器械
  const isMedicalSpecificFn = (value: number) => {
    if (value) {
      return [5539, 188104, 188107].includes(value) ? true : false
    }
    return false
  }
  interface Dialog {
    id?: string | number
    title?: string
    visible: boolean
    width?: string | number
    type?: string
    imgType?: string
    library?: string
  }
  const imgTypeSize = computed(() => {
    const obj = {
      main: 9,
      detail: 50,
      productVideo: 1
    }
    return obj[state.dialog.imgType]
  })

  const state = reactive({
    is_disabled: false, //类目是否禁用
    categoryOptions: [], //类目列表数据
    isWeixinshop: 0,
    tag_list_flag: Date.now(),
    previewField: '',
    skuReady: false,
    previewLoading: true,
    main_preview: {
      visible: false,
      src: '',
      index: 0,
      detailFresh: Date.now() // 拖拽后刷新商品详情预览
    },
    detail_preview: {
      visible: false,
      src: '',
      index: 0,
      detailFresh: Date.now() // 拖拽后刷新商品详情预览
    },
    intercept: false,
    saveType: null,
    areasNos: [
      {
        value: 'Option1',
        label: 'Option1'
      }
    ],
    ruleStatus: false,
    product_videos: [],
    className: props.goodsClass?.name,
    specList: [], // 规格数组
    skuList: [], // 价格数组
    cateList: [],
    goodsStoreList: [],
    back_shop_certificate_product_id: undefined, // 备份医药资质
    addForm: {
      shop_certificate_product_id: undefined, // 医药资质
      shop_certificate_product: [],
      type: 1, // 商品类型
      taxes: 1, // 税费
      cross_border_type: 1, // 跨境方式
      bonded_id: undefined, // 菜鸟货主id
      bonded_name: undefined,
      bonded_product_id: undefined, // 菜鸟货品id
      bonded_product_name: undefined,
      warehouse_name: undefined, // 保税仓
      rookie_bond: [] as (string | number)[], // 菜鸟保税
      tax_rate: undefined, // 税率
      customs: undefined, // 原产国
      origin: undefined, // 申报海关
      is_return_bonded: 2, // 退货至保税仓
      rookie_account_id: undefined, // 菜鸟账号id
      rookie_account_name: undefined,
      changeField: '',
      erp_id: null,
      gdt_aptitude_img: [],
      is_async_gdt: false,
      useShop: 'shop_id', //店铺id
      category_id: '', //商品类目
      category_ids: props.goodsClass?.ids, //商品类目ids
      category_names: props.goodsClass?.name, //商品类目名称
      title: '', //商品名称
      goods_sub_title: '', //详情页标题
      desc: '', //备注
      recommend_tag: isMedical.value ? '品牌特卖' : '好物推荐', //推荐标签
      recommend_tag_txt: isMedical.value ? '品牌特卖，正品保障' : '爆款热销，正品保障', // 推荐标签描述
      recommend_tag_color: defaultStateColor.recommend_tag_color, //推荐标签颜色
      sale_tag: '放心购', //销售标签
      sale_tag_txt: isMedical.value ? '品质保证，极速售后' : '品质保证，极速售后', // 销售标签描述
      sale_tag_color: defaultStateColor.sale_tag_color, //销售标签颜色
      price: null, //活动销售价
      old_price: null, //划线价
      // stock_num: null, // 总库存
      product_code: '', //商品编码
      active_desc: '', //活动文案
      button_text: '立即下单',
      detail: '', //详情
      detail_image: [], // 商品详情图片
      is_unit: 2, //是否启用多规格  1.开启   2.不开启
      image: '', //商品主图
      image_ids: [], // 商品图片
      product_video: undefined, //主图视频
      product_video_id: 0,
      product_video_info: {},
      product_video_status: 0,
      attribute: [
        // 商品属性
        {
          name: '', //属性名
          attr_val: '' //属性值
        }
      ],
      freight_type: 1, // 1.商家承担运费， 2.固定运费
      freight: null, //固定运费绑定的值
      // undeliver_id: '', //非配送地区选中id
      deliver_temp_id: undefined, // 运费模版ID
      sales_type: 1, // 1累计真实销量 2不展示销量 3全渠道累计真实销量
      sales_initial: null, //真实销量数值
      rolling: 1, // 跑马灯 1.主图浮层跑马灯  2.主图下方跑马灯  3.关闭跑马灯
      limit_active: 1, // 限时活动 1.开启  2.不开启
      ensure: 1, //购物保障   1.显示  2.不显示
      protection: [1, 2], // 服务保障
      send_time: 0, //发货时间  1.24  2.48  3.72
      is_temp: 0, // 0. 保存草稿， 1. 保存并上架
      status: 0, // 0.草稿， 1.上架
      limit_type: 1, // 限购方式
      limit_desc: '',
      limit_once_max_num: null,
      limit_once_min_num: null,
      limit_total_num: null,
      tag_list: tag_list,
      question_temp_id: undefined, // 问答组
      comment_temp_id: undefined, // 评论组
      shop_address_id: undefined, // 售后地址
      sku_show_style: 1, // 规格展现形式
      is_brand_shop: 0, // 是否添加到商铺首页
      is_show_shop: 2, // 是否展示商铺入口
      user_notice: '',
      auth_phone: 2,
      stopNewYearSend: false, // 过年停发
      stopNewYearSendDate: [],
      isHistory: false, //审核通过后存为历史版本
      version_name: undefined,
      version_remark: undefined,
      is_open_return: 2, //是否开启售后返现
      rebate_config: {
        type: 1,
        money: undefined,
        list: [
          {
            min_price: undefined, //
            money: undefined
          }
        ]
      },
      send_gift_switch: 0, // 是否开启送礼物
      wechat_shop_appid: undefined,
      wechat_shop_product_id: undefined,
      wechat_product_qrcode: [],
      skuData: { list: [] } // SKU数据
    },
    protectionObj: guaranteeData.reduce((acc: any, item) => {
      if ([1, 2].includes(item.value)) {
        acc[item.value] = true
      } else {
        acc[item.value] = false
      }
      return acc
    }, {}),
    dialog: {
      id: '',
      title: '',
      visible: false,
      width: '',
      type: '',
      library: '',
      imgType: 'main'
    } as Dialog,
    error: false,
    formChange: false, // 编辑商品详情内容无变化
    commentGroups: [], // 评论组
    afterSaleAddress: [], // 售后地址列表
    commentLoading: false,
    qaGroups: [], // 问答组
    qaLoading: false,
    erpList: [],
    timestamp: Date.now(),
    contrabandList: [],
    initContrabandFirst: true,
    version_name_remark: '',
    main_imgs_boolean: false,
    shop_detail_boolean: false,
    err_image_ids: [],
    err_detail_image: [],
    politics_imgs: [],
    politics_detail_imgs: [],
    confirm_images: {},
    img_ignores: [] as any,
    detail_ignores: [] as any,

    // 全屏SKU编辑相关状态
    fullscreenSkuVisible: false,
    fullscreenSpecList: [], // 全屏模式下的规格数组
    fullscreenSkuList: [], // 全屏模式下的价格数组
    fullscreenSkuData: { list: [] }, // 全屏模式下的SKU数据
    fullscreenTimestamp: Date.now()
  })
  const stopNewYearSendDateRule = {
    required: true,
    validator: (_: any, value: Dayjs[]) => {
      if (!value?.length) {
        return Promise.reject('请选择发货时效起止时间')
      }
      if (!value[0]) {
        return Promise.reject('请选择发货时效开始时间')
      }
      if (!value[1]) {
        return Promise.reject('请选择发货时效截止时间')
      }
      return Promise.resolve()
    },
    trigger: ['change', 'blur']
  }
  const disabledDateHandler = (current: Dayjs) => {
    const today = dayjs().startOf('day')
    const endDate = dayjs('2025-02-06').endOf('day')
    return current.isBefore(today) || current.isAfter(endDate)
    // return current.isBefore(today)
  }
  const datePickerChange = (value: Dayjs[]) => {
    console.log(value)
    if (value.length) {
      if (dayjs(value[1]).isAfter(dayjs('2025-02-06'))) {
        message.warning('停发截止时间不得大于2025年2月2日')
        state.addForm.stopNewYearSendDate = []
        return
      }
      if (dayjs(value[1]).diff(dayjs(value[0]), 'day') > 15) {
        message.warning('起止时间间隔不得大于15天，请选择合理的时间范围')
        state.addForm.stopNewYearSendDate = []
        return
      }
    }
  }
  // 导入sku相关操作
  const { skuChange, skuData, setTableList, getColumns, initEditTableData, setType, handleSkuData } = skuFun(
    state.addForm
  )
  state.addForm.skuData = skuData
  // 导入form验证规则
  const { ruleData, titleValidator, addRulesFun } = rules(state)
  // 实时预览监听
  const handleFromPreview = (field) => {
    state.previewField = field
  }
  //弹窗关闭事件
  const modalCancel = () => {
    if (state.dialog.type !== 'versions') return
    state.addForm.isHistory = false
    // alert('清空addForm里面对应的历史版本数据')
  }
  const changeIsHistory = () => {
    addForm.value
      .validate()
      .then(() => {
        if (!state.addForm.send_time) {
          initLoading()
          state.addForm.isHistory = false
          return message.warning('请选择发货时间')
        }
        if (state.addForm.isHistory) {
          state.addForm.isHistory = true
          state.dialog = {
            id: '',
            title: '提示',
            visible: true,
            width: '480px',
            type: 'versions'
          }
        }
        resolve(true)
      })
      .catch((error) => {
        console.log('error', error)

        // addForm.value.scrollToField(error.errorFields[0].name)
        let scrollDom = null
        // 评论组
        if (error.errorFields[0]?.name[0].indexOf('temp[') > -1) {
          scrollDom = document.querySelector('#tag_list')
        } else if (error.errorFields[0]?.name[0].indexOf('attrs_') > -1) {
          scrollDom = document.querySelector('#attribute')
        } else if (error.errorFields[0]?.name[0].indexOf('sku_group_') > -1) {
          scrollDom = document.querySelector('#sku_list')
        } else if (error.errorFields[0]?.name[0].indexOf('skuData') > -1) {
          scrollDom = document.querySelector('#sku_list_price')
        } else if (error.errorFields[0]?.name[0].indexOf('rookie_bond') > -1) {
          scrollDom = document.querySelector('#crossborderInfo')
        } else if (error.errorFields[0]?.name[0].indexOf('user_notice_list') > -1) {
          scrollDom = document.querySelector('#user_notice')
        } else if (error.errorFields[0]?.name[0].indexOf('stopNewYearSendDate') > -1) {
          scrollDom = document.querySelector('#protection')
        } else if (
          error.errorFields[0]?.name[0].includes('min_price') ||
          error.errorFields[0]?.name[0].includes('money')
        ) {
          scrollDom = document.querySelector('#is_open_return')
        } else {
          scrollDom =
            document.querySelector(`[name="${error.errorFields[0].name}"]`) ||
            document.querySelector(`#${error.errorFields[0].name}`) ||
            document.querySelector(`[name="${error.errorFields[0].name[0]}"]`) ||
            document.querySelector(`#${error.errorFields[0].name[0]}`) ||
            document.querySelector(`.${error.errorFields[0].name[0]}`)
        }

        scrollToElement(scrollDom)
        message.warning(error.errorFields[0].errors[0])
        state.addForm.isHistory = false
        initLoading()
        resolve(false)
      })
  }
  // 含违禁词图片点击勾选框
  const confirmImages = (event: any, element: string, data: any) => {
    data.forEach((item) => {
      if (element == item) {
        state.confirm_images[item] = event.target.checked
      }
    })
    addForm.value
      .validateFields(['image_ids', 'detail_image'])
      .then(() => {
        console.log('Validation passed')
      })
      .catch((error) => {
        console.log('Validation failed', error)
      })
  }
  const getViolationReasons = (type, data) => {
    if (!Array.isArray(data)) {
      return ''
    }
    const invalidItems = data.filter((item) => !item.valid)
    if (type === 'image_ids') {
      state.err_image_ids = invalidItems.map((item) => item.url)
      state.politics_imgs = invalidItems.filter((item) => item.image_reason).map((item) => item.url) //含时政的图片
    } else {
      state.err_detail_image = invalidItems.map((item) => item.url)
      state.politics_detail_imgs = invalidItems.filter((item) => item.image_reason).map((item) => item.url) //含时政的图片
    }

    // 提取对象 key为url，值为是否确认无误，默认为false
    const images = data.reduce((obj, item) => {
      obj[item.url] = false
      return obj
    }, {})
    state.confirm_images = { ...images, ...state.confirm_images }

    const image_reason = invalidItems.map((item) => item.image_reason).filter((reason) => reason)
    const text_reason = invalidItems
      .map((item) => {
        if (!state.confirm_images[item.url]) return item.text_reason //未确认无误的图片才展示提示原因
      })
      .filter((reason) => reason)
    const hasFalse = invalidItems.some((value) => state.confirm_images[value.url] == false && image_reason)
    return {
      image_reason: image_reason.length > 0 ? image_reason.join('、') : '',
      text_reason: text_reason.length > 0 ? text_reason.join('、') : '',
      hasFalse
    }
  }
  // 商品主图图片验证
  const valid_imgs = async (rule?: any, value?: string) => {
    if (state.addForm.image_ids.length === 0) {
      return Promise.reject('请上传主图')
    } else {
      state.main_imgs_boolean = false
      let { data } = await product_check_image({ images: state.addForm.image_ids, scene: 1 })
      const { image_reason, text_reason, hasFalse } = getViolationReasons('image_ids', data.result)

      // 处理主图传参的状态
      img_agnores_handle()
      // 是否有未确认的
      if (!hasFalse) {
        return false
      }
      if (image_reason && text_reason) {
        return Promise.reject(
          `您上传的图片涉及到”${image_reason}“相关，图片中”${text_reason}“涉及到违禁词，请您重新上传！如您上传照片确认无误，请勾选“确认无误”`
        )
      } else if (image_reason) {
        return Promise.reject(`您上传的图片涉及到”${image_reason}“相关，请您重新上传！`)
      } else if (text_reason) {
        return Promise.reject(
          `图片中”${text_reason}“涉及到违禁词，请您重新上传！如您上传照片确认无误，请勾选“确认无误”`
        )
      } else {
        state.main_imgs_boolean = true
        return Promise.resolve()
      }
    }
  }

  const valid_imgs_detail = async (rule?: any, value?: string) => {
    if (state.addForm.detail_image.length === 0) {
      state.shop_detail_boolean = true
      return Promise.resolve()
    } else {
      state.shop_detail_boolean = false
      let { data } = await product_check_image({ images: state.addForm.detail_image, scene: 1 })
      const { image_reason, text_reason, hasFalse } = getViolationReasons('detail_image', data.result)
      detail_agnores_handle()
      // 是否有未确认的
      if (!hasFalse) {
        return false
      }
      if (image_reason && text_reason) {
        return Promise.reject(
          `您上传的图片涉及到”${image_reason}“相关，图片中”${text_reason}“涉及到违禁词，请您重新上传！如您上传照片确认无误，请勾选“确认无误”`
        )
      } else if (image_reason) {
        return Promise.reject(`您上传的图片涉及到”${image_reason}“相关，请您重新上传！`)
      } else if (text_reason) {
        return Promise.reject(
          `图片中”${text_reason}“涉及到违禁词，请您重新上传！如您上传照片确认无误，请勾选“确认无误”`
        )
      } else {
        state.shop_detail_boolean = true
        return Promise.resolve()
      }
    }
  }

  const img_agnores_handle = () => {
    // 如果有删除图片操作
    const result = state.img_ignores.filter((item) => {
      const urlExists = state.addForm.image_ids.includes(item.images)
      if (!urlExists) {
        return false // 删除当前元素
      } else {
        item.shop_status = state.confirm_images[item.images] ? 1 : 0
        return true
      }
    })
    // 如果有新增图片操作
    const existingUrls = state.img_ignores.map((item) => item.images)
    const newItems = state.addForm.image_ids
      .filter((url) => !existingUrls.includes(url))
      .map((url) => ({
        images: url,
        boss_status: 0, // 默认值
        shop_status: state.confirm_images[url] ? 1 : 0 // 默认值
      }))
    state.img_ignores = [...result, ...newItems]
  }
  const detail_agnores_handle = () => {
    // 如果有删除图片操作
    const result = state.detail_ignores.filter((item) => {
      const urlExists = state.addForm.detail_image.includes(item.images)
      if (!urlExists) {
        return false // 删除当前元素
      } else {
        item.shop_status = state.confirm_images[item.images] ? 1 : 0
        return true
      }
    })
    // 如果有新增图片操作
    const existingUrls = state.detail_ignores.map((item) => item.images)
    const newItems = state.addForm.detail_image
      .filter((url) => !existingUrls.includes(url))
      .map((url) => ({
        images: url,
        boss_status: 0, // 默认值
        shop_status: state.confirm_images[url] ? 1 : 0 // 默认值
      }))
    state.detail_ignores = [...result, ...newItems]
  }
  // 销售标签颜色变化
  const handleSaleTagColorChange = ($event) => {
    if ($event == null) {
      state.addForm.sale_tag_color = defaultStateColor.sale_tag_color
    }
    state.addForm.changeField = 'sale_tag_color'
  }

  // 商品主图图片拖拽完成
  const handleMainDrag = () => {
    state.main_preview.detailFresh = Date.now()
  }
  // 商品主图图片预览
  const handleMainPreview = (src: any, index: any) => {
    state.main_preview.src = src
    state.main_preview.index = index
    state.main_preview.visible = true
  }

  // 商品详情图片拖拽完成
  const handleDetailDrag = () => {
    state.detail_preview.detailFresh = Date.now()
  }
  // 商品详情图片预览
  const handleDetailPreview = (src: any, index: any) => {
    state.detail_preview.src = src
    state.detail_preview.index = index
    state.detail_preview.visible = true
  }

  // 判断商品属性是否变化
  const addDiffClass = computed(() => {
    // return (field: any) => addClass(field, state.addForm.status)
    return (field: any) => addClass(field)
  })
  const disabledAsync = computed(() => {
    return route.query?.is_temp !== '1' && state.addForm.is_async_gdt
  })

  // 发货时间 Filter
  const sendTimeFilter = (key) => {
    const obj = {
      1: 24,
      2: 48,
      4: 12,
      3: 72
    }
    return obj[key] || 'X'
  }
  // 广告账号前端模糊搜索
  const handleAdFilterOption = (input: string, option: any) => {
    return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0 || option.value.indexOf(input) >= 0
  }
  const filterOption = (val: any, options: any) => {
    if (
      options.erp_id.indexOf(val) != -1 ||
      options.name.indexOf(val) != -1 ||
      options.erp_shop_name.indexOf(val) != -1 ||
      (options.erp_shop_id && options.erp_shop_id.indexOf(val) != -1)
    ) {
      return true
    } else {
      return false
    }
  }

  const copyForm = computed(() => {
    return JSON.parse(JSON.stringify(state.addForm))
  })
  // 侦听 编辑表单变化
  let unwatch = watch(
    () => copyForm,
    () => {
      // 编辑表单发生改变
      state.formChange = true
    },
    { deep: true }
  )
  watch(
    () => state.formChange,
    () => {
      console.log('state.formChange', state.formChange)
    }
  )
  watch(
    () => state.addForm.skuData.list,
    () => {
      if (state.skuReady) {
        const { sku_list, spec_list } = handleSkuData(skuData)
        state.addForm.sku_list = sku_list
        state.addForm.spec_list = spec_list
        state.addForm.changeField = 'sku_list'
      }
    },
    { deep: true }
  )

  // 校验对象是否为空
  const validObjectEmpty = (data) => {
    if (isObject(data)) {
      const fieldsArr = Object.keys(data)
      if (fieldsArr?.length) {
        return data
      } else {
        return null
      }
    } else {
      return null
    }
  }

  // 回到默认值
  const abvv = (color, filed) => {
    state.addForm[filed] = color
  }
  const initSkuHandler = (sku_list: any, spec_list: any) => {
    console.log('initSkuHandler', sku_list)
    // 处理商品规格值
    state.specList = (spec_list || []).map((item, index) => {
      item.key = 'sp' + (Number(index) + 1)
      if (item?.value_list?.length) {
        item.value_list?.forEach((k) => {
          let spec_tag = {}
          if (k.tag) {
            spec_tag[k.id || k.value_key] = k.tag
          }
          k.spec_tag = spec_tag
        })
      }
      return item
    })
    state.skuList = initEditTableData(sku_list, state.specList)
    // 回显表格数据
    setTableList(state.skuList)
    // 回显表头数据
    getColumns(state.specList)
    // 主动触发一次change事件
    skuChange(state.specList, true)
    // store.setIsTemp(state.addForm.is_temp)
  }

  // 获取分类列表
  const getCategoryListNew = async () => {
    try {
      const resp = await setCategoryList({
        is_wechat: route.query.isWeixinshop && +route.query.isWeixinshop ? 1 : 0
      })
      const result = resp.data || []
      //微信小店正常赋值可全选-->非微信小店商品处理disabled是否可选
      if (route.query?.isWeixinshop && +route.query.isWeixinshop) {
        state.categoryOptions = result
      } else {
        state.categoryOptions = processOptions(cloneDeep(result))
      }
    } catch (error) {
      console.error(error)
    } finally {
    }
  }
  // 获取商品详情
  const getInfo = async () => {
    try {
      // setGlobalLoading(true)
      let data = {}
      if (route.query?.is_temp === '1') {
        // 如果为草稿数据,对比草稿与线上数据差异高亮显示
        let [{ data: onLineData }, { data: tempData }] = await Promise.all([
          setProductInfo({ id: route.query?.id }),
          get_product_temp_info({ id: route.query?.id })
        ])
        tempData = JSON.parse(tempData.data)

        DataDiff.init(cloneDeep(onLineData), cloneDeep(tempData || onLineData), 'label_red')
        data = validObjectEmpty(tempData) || validObjectEmpty(onLineData) || {}
      } else {
        // 如果为线上或者审核数据
        if (route.query?.is_online === '1') {
          // 如果为线上数据，直接取值
          const { data: onLineData } = await setProductInfo({ id: route.query?.id })
          DataDiff.init({}, {}, 'label_red')
          data = onLineData
        } else {
          // 如果为审核数据，对比审核与线上数据差异高亮显示
          let [{ data: onLineData }, { data: examineData }] = await Promise.all([
            setProductInfo({ id: route.query?.id }),
            getExamineDetailApi({ id: route.query?.id })
          ])
          DataDiff.init(cloneDeep(onLineData), cloneDeep(examineData || onLineData), 'label_red')
          data = validObjectEmpty(examineData) || validObjectEmpty(onLineData) || {}
        }
      }

      // DataDiff.init(cloneDeep(data), cloneDeep(examineData || data), 'label_red')
      // 审核不通过时
      // if (data.status === 3) {
      //   data = validObjectEmpty(examineData) || validObjectEmpty(data) || {}
      // }
      // let res = await setProductInfo({ id: route.query?.id })
      let _product_video = (data.product_video && JSON.parse(data.product_video)) || {}

      state.addForm = {
        changeField: '',
        ...data,
        protection: (data.protection.split(',') || []).map((v) => (v = Number(v))),
        image_ids: data.image_ids.trim() ? data.image_ids.split(',') : [],
        category_ids: props.goodsClass?.ids || data.category_ids.split(',') || [],
        category_names: props.goodsClass?.name || data.category_names, //商品类目名称
        price: data.price == 0 ? '' : data.price.toFixed(2) || null,
        old_price: data.old_price == 0 ? '' : data.old_price.toFixed(2) || null,
        freight: data.freight_type == 2 && data.freight != 0 ? data.freight.toFixed(2) : null,
        // undeliver_id: data.undeliver_id || '',
        deliver_temp_id: data.deliver_temp_id || undefined,
        attribute: data.attribute || [],
        button_text: data.button_text || '立即下单',
        skuData,
        tag_list: data.tag_list || tag_list,
        comment_temp_id: data.comment_temp_id || undefined,
        question_temp_id: data.question_temp_id || undefined,
        shop_address_id: data.shop_address_id || undefined,
        send_address_id: data.send_address_id || undefined,
        recommend_tag: props?.goodsClass?.ids && isMedical.value ? '品牌特卖' : data.recommend_tag,
        recommend_tag_txt: props?.goodsClass?.ids && isMedical.value ? '品牌特卖，正品保障' : data.recommend_tag_txt,
        sale_tag_txt: props?.goodsClass?.ids && isMedical.value ? '品质保证，极速售后' : data.sale_tag_txt,
        limit_once_max_num: data.limit_once_max_num || undefined,
        limit_once_min_num: data.limit_once_min_num || undefined,
        limit_total_num: data.limit_total_num || undefined,
        detail_image: data.detail_image ? JSON.parse(data.detail_image).map((v: any) => v.src) : [],
        detail_image_list: data.detail_image ? JSON.parse(data.detail_image) : [],
        erp_id: data.erp_id || undefined,
        is_async_gdt: data.is_async_gdt == 1 ? true : false,
        gdt_library_id: data.gdt_library_id ? String(data.gdt_library_id) : undefined,
        gdt_category_id: data.gdt_category_id ? data.gdt_category_ids.split(',').map(Number) : undefined,
        gdt_aptitude_img: data.gdt_aptitude_img ? JSON.parse(data.gdt_aptitude_img) : [],
        product_video_info: _product_video || '',
        product_video: _product_video?.old_url || '',
        product_video_status: data.product_video_status,
        product_video_id: data.product_video_id || 0,

        is_show_shop: data.is_show_shop === 0 ? 2 : data.is_show_shop,
        sku_show_style: data.sku_show_style === 0 ? 1 : data.sku_show_style,
        type: data.type === 0 ? 1 : data.type,
        limit_type: data.limit_type === 0 ? 1 : data.limit_type,

        is_open_return: (data?.rebate_config && !JSON.parse(data?.rebate_config)) || !data?.rebate_config ? 2 : 1,
        rebate_config:
          (data?.rebate_config && !JSON.parse(data?.rebate_config)) || !data?.rebate_config
            ? {
                type: 1,
                money: undefined,
                list: [
                  {
                    min_price: undefined, //
                    money: undefined
                  }
                ]
              }
            : JSON.parse(data?.rebate_config)
      }
      // debugger
      // 服务保障
      if (data?.protection) {
        ;(data.protection.split(',') || [])
          .map((v) => (v = Number(v)))
          .forEach((value) => {
            if (state.protectionObj.hasOwnProperty(value)) {
              state.protectionObj[value] = true
            }
          })
      }
      // 处理跨境保税信息
      if (state.addForm.rookie_account_id && state.addForm.bonded_id && state.addForm.bonded_product_id) {
        // state.addForm.rookie_bond = `${state.addForm.bonded_name}-${state.addForm.bonded_product_name}`
        state.addForm.rookie_bond = [
          state.addForm.rookie_account_id,
          state.addForm.bonded_product_id,
          state.addForm.bonded_id
        ]
      }
      // 处理医药资质
      if (data?.shop_entity_id && data?.shop_certificate_id && data?.shop_certificate_product_id) {
        state.addForm.shop_certificate_product = [
          data.shop_entity_id,
          data.shop_certificate_id,
          data.shop_certificate_product_id
        ]
      }
      state.back_shop_certificate_product_id = data?.shop_certificate_product_id
      state.timestamp = Date.now()
      // 下架春节停发-暂时注释
      // 春节停发
      if (data?.year_stop_send === 1 && false) {
        // state.addForm.stopNewYearSend = true
        // state.addForm.stopNewYearSendDate = [
        //   dayjs.unix(data?.stop_start_time).format('YYYY-MM-DD'),
        //   dayjs.unix(data?.stop_end_time).format('YYYY-MM-DD')
        // ]
      } else {
        state.addForm.stopNewYearSend = false
        state.addForm.stopNewYearSendDate = []
      }
      // 处理商品价格表内容
      // state.skuList = (data.sku_list || []).map((item, index) => {
      //   const names = item.title.split('-')
      //   item.rowKey = item.title.split('-').join(',')
      //   for (let i in names) {
      //     item['sp' + (Number(i) + 1)] = names[i]
      //   }
      //   item.pic = item.image_id && [{ id: item.image_id, url: item.image_url }]
      //   return item
      // })
      if (data.type === 4) {
        state.addForm.wechat_product_qrcode = [{ url: data.wechat_product_qrcode }]
      }
      initSkuHandler(data.sku_list, data.spec_list)
      // store.setIsTemp(state.addForm.is_temp)
      // 设置活动销售价必填标识显示
      moreSkuSwitch(state.addForm.is_unit)
      // 设置评论问答详情数据

      if (state.addForm.comment_temp_id) {
        const comment = await queryCommentInfo(state.addForm.comment_temp_id)
        state.addForm.comment = comment
        state.addForm.changeField = ''
      }
      if (state.addForm.question_temp_id) {
        const question = await queryQAInfo(state.addForm.question_temp_id)
        state.addForm.question = question
        state.addForm.changeField = ''
      }
      // 处理确认无误回显
      if (data.image_ignores) {
        // 将整合的主图和详情图违禁词状态数组分开
        let arr1: any = []
        let arr2: any = []
        data.image_ignores.forEach((item) => {
          if (state.addForm.image_ids.includes(item.images)) {
            arr1.push(item)
          } else {
            arr2.push(item)
          }
        })
        state.img_ignores = arr1
        state.detail_ignores = arr2

        state.confirm_images = data.image_ignores.reduce((acc, item) => {
          acc[item.images] = item.shop_status === 1
          return acc
        }, {})
      }

      state.is_disabled = findDisabledByPath(state.categoryOptions, state.addForm.category_ids)
      if (state.is_disabled) {
        return message.warning('只能选择赦免类目')
      }
      // 触发校验 显示实时状态
      setTimeout(() => {
        addForm.value.validateFields()
        validateChange('active_desc', state.addForm.limit_active != 1 || state.addForm.active_desc.length >= 1)
        // addForm.value.validateFields(['image_ids', 'detail_image'])
      }, 0)
      nextTick(() => {
        // 页面初始化完成重置 编辑表单状态
        setTimeout(() => {
          state.formChange = false
          state.skuReady = true
          state.addForm.changeField = ''
        }, 1000)
      })
    } catch (error) {
      console.error(error)
      // modalWarning()

      state.skuReady = true
      if (error?.code === 40001) {
        router.push({ name: 'ShopGoodsList' })
      }
    } finally {
      setGlobalLoading(false)
      state.previewLoading = false
    }
  }

  //已删除弹窗
  // const modalWarning = () => {
  //   Modal.warning({
  //     title: '提示',
  //     content: '该商品已删除！',
  //     onOk() {
  //       // router.back() // 返回上一页
  //       router.push({ name: 'ShopGoodsList' })
  //     }
  //   })
  // }
  onMounted(() => {
    // 微信小店标识

    addRulesFun()
    // 触发校验 显示实时状态
    setTimeout(() => {
      addForm.value.validateFields(['recommend_tag', 'recommend_tag_txt', 'sale_tag', 'sale_tag_txt'])
    }, 0)
    window.addEventListener('beforeunload', onBeforeunload, false)
  })

  const onBeforeunload = (e) => {
    var confirmationMessage = '确定离开此页面吗？'
    ;(e || window.event).returnValue = confirmationMessage
    return confirmationMessage
  }

  onActivated(() => {
    state.className = props.goodsClass?.name
    state.addForm.category_ids = props.goodsClass?.ids //商品类目ids
    state.addForm.category_names = props.goodsClass?.name //商品类目名称
    isCate.value = false
    initData()
  })

  onDeactivated(() => {
    isCate.value = true
  })

  const backEmit = () => {
    state.contrabandList = []
    // state.addForm.shop_certificate_product = []
    // state.addForm.shop_certificate_product_id = undefined
    isCate.value = true
    emit('event', {
      cmd: 'to',
      data: {
        name: state.addForm.category_names,
        ids: state.addForm.category_ids
      }
    })
    state.initContrabandFirst = false
  }
  onBeforeRouteLeave(() => {
    if (state.intercept == true) {
      return true
    } else {
      if (state.formChange) {
        let r = confirm('系统可能不会保存所做的修改')
        if (r == true) {
          return true
        } else {
          return false
        }
      } else {
        return true
      }
    }
  })

  onBeforeUnmount(() => {
    window.removeEventListener('beforeunload', onBeforeunload, false)
  })
  const onDelVideo = () => {
    state.addForm.product_video = undefined
    state.addForm.product_video_id = 0
    ;(state.addForm.product_video_info = {}), (state.addForm.changeField = 'product_video_info')
  }
  // 删除主图
  const onDelImg = (val: any, imgType: any, image?: any) => {
    state.dialog.imgType = imgType
    if (state.dialog.imgType === 'main') {
      state.addForm.image_ids.splice(val, 1)
      addForm.value.validateFields(['image_ids'])
      state.addForm.changeField = 'image_ids'
      // state.confirm_images = state.confirm_images.filter((item) => item !== image)
    } else if (state.dialog.imgType === 'aptitude') {
      state.addForm.gdt_aptitude_img.splice(val, 1)
    } else {
      state.addForm.detail_image.splice(val, 1)
      addForm.value.validateFields(['detail_image'])
      state.addForm.changeField = 'detail_image'
    }
  }

  // 素材库
  const onShowDialog = (type: any, imgType?: any) => {
    switch (type) {
      case 'library':
        state.dialog = {
          id: '',
          title: '素材库',
          visible: true,
          width: '1070px',
          library: 'library',
          imgType: imgType,
          type: 'image'
        }
        break
      case 'aptitude':
        state.dialog = {
          id: '',
          title: '选择店铺许可资质证',
          visible: true,
          width: '640px',
          type: 'aptitude'
        }
        break
      case 'video':
        state.dialog = {
          id: '',
          title: '选择视频',
          visible: true,
          width: '640px',
          type: 'video'
        }
        break
      case 'taxes':
        state.dialog = {
          id: '',
          title: '税费说明',
          visible: true,
          width: '640px',
          type: 'taxesDesc'
        }
        break
    }
  }

  // 向主图中添加图片
  const handleAddMainImg = (data: any) => {
    if (data.data.some((item: any) => (item.file_size / 1024 / 1024).toFixed(0) > 5)) {
      return message.warning('请上传小于5M的图片')
    }
    if (data.data.length > 9) return message.warning('最多只能添加9张图片')
    if (state.addForm.image_ids.length == 0) {
      state.addForm.image_ids = data.data.map((v: any) => v.file_url)
    } else {
      if (state.addForm.image_ids.concat(data.data.map((v: any) => v.file_url)).length > 9)
        return message.warning('最多只能添加9张图片')
      state.addForm.image_ids = state.addForm.image_ids.concat(data.data.map((v: any) => v.file_url))
    }
    state.addForm.changeField = 'image_ids'
    addForm.value.validateFields(['image_ids'])
  }

  // 向商品详情中添加图片
  const handleAddDetailImg = (data: any) => {
    if (data.data.some((item: any) => (item.file_size / 1024 / 1024).toFixed(0) > 5)) {
      return message.warning('请上传小于5M的图片')
    }
    if (data.data.length > 50) return message.warning('最多只能添加50张图片')
    if (state.addForm.detail_image.length == 0) {
      state.addForm.detail_image = data.data.map((v: any) => v.file_url)
    } else {
      if (state.addForm.detail_image.concat(data.data.map((v: any) => v.file_url)).length > 50)
        return message.warning('最多只能添加50张图片')
      state.addForm.detail_image = state.addForm.detail_image.concat(data.data.map((v: any) => v.file_url))
    }
    state.addForm.changeField = 'detail_image'
    addForm.value.validateFields(['detail_image'])
  }
  const handleAddMasterVideo = (content: any) => {
    const { data } = content
    // console.log('data1', data)
    if (data.video_len > 180) return message.warning('上传视频不能超过3分钟')
    state.addForm.product_video = data.video
    state.addForm.product_video_id = data.id || 0
    state.addForm.product_video_status = data.status
    state.addForm.product_video_info = {
      ...data,
      old_url: data.video,
      url: data.video_speed
    }
    state.addForm.changeField = 'product_video_info'
  }
  const onEvent = (data) => {
    switch (data.cmd) {
      case 'close':
        state.dialog.visible = false
        break
      case 'material':
        state.dialog.visible = false
        if (state.dialog.imgType === 'productVideo') {
          handleAddMasterVideo(data)
        } else if (state.dialog.imgType === 'main') {
          handleAddMainImg(data)
        } else {
          handleAddDetailImg(data)
        }
        break
      case 'confirm':
        state.dialog.visible = false
        console.log(data.data, 'datadatadatadata')
        state.addForm.gdt_aptitude_img = data.data
        console.log(state.addForm.gdt_aptitude_img, 'state.addForm.gdt_aptitude_img')
        break
      case 'video':
        if (data.data) {
          handleAddMasterVideo(data)
          state.dialog.visible = false
        }
    }
  }
  //历史版本回调
  const onEventVersions = (data) => {
    if (data.cmd) {
      // alert('给addForm赋值')
      if (route.query?.id) {
        // handleSaveVersion(data.form)
        console.log(data.form)
        state.version_name_remark = data.form

        save(false, true)
      } else {
        state.addForm.version_name = data.form.name
        state.addForm.version_remark = data.form.remark
      }

      state.dialog.visible = false
    } else {
      modalCancel()
      state.dialog.visible = false
    }
  }
  // 保存版本
  const handleSaveVersion = async (val, type) => {
    try {
      let params = {
        ...val,
        on_sale: 1,
        is_temp: 0
      }
      let paramsData = {
        ...state.version_name_remark,
        product_video_status: 2, //主图视频状态
        product_id: Number(route.query?.id) || undefined,
        data: JSON.stringify(params)
      }
      let res = await saveVersion(paramsData)
      console.log(res)
      if (res.code == 0) {
        message.success(res.msg)
      }
    } catch (error) {
      console.error(error)
    }
  }
  //     }

  //     const data = {
  //       ...item,
  //       spec_key: spec_key.join(','),
  //       title: title.join('$&$'),
  //       price: +price,
  //       old_price: +old_price,
  //       // stock_num: +stock_num,
  //       code,
  //       image_id: pic[0] && pic[0].id,
  //       image_url: pic[0] && (pic[0].file_url || pic[0].url)
  //     }
  //     delete data.pic
  //     delete data.ids
  //     return data
  //   })

  //   return {
  //     spec_list,
  //     sku_list
  //   }
  // }

  const initLoading = () => {
    emit('event', { cmd: 'loading', data: { type: state.saveType, loading: false } })
  }

  const ruleCheck = (val) => {
    state.ruleStatus = true
    state.intercept = true
    state.saveType = val || null
    return new Promise(async (resolve, _reject) => {
      if (state.addForm.is_unit == 1 && state.addForm.skuData.list.length && skuListRef.value) {
        await skuListRef.value.resetSearch()
        let text = ''
        const codeArr = []
        let _index = 0
        for (let index = 0; index < skuListRef.value.dynamicValidateForm?.list.length; index++) {
          const item = skuListRef.value.dynamicValidateForm?.list[index]

          codeArr.push(item.code)

          if (item.pic.length <= 0 || item.pic === 0) {
            _index = index
            text = `第${index + 1}条数据请上传产品主图`
            break
          } else if (!item.price || !Number(item.price)) {
            _index = index
            text = `第${index + 1}条数据请输入商品实际售卖价格`
            break
          } else if (item.price && item.old_price && Number(item.price) >= Number(item.old_price)) {
            _index = index
            text = `第${index + 1}条数据划线价需大于活动销售价`
            break
          } else if (item.code && codeArr.indexOf(item.code) !== index) {
            _index = index
            text = `第${index + 1}条数据的规格编号与之前的数据重复`
            break
          }
        }

        if (text) {
          message.warning(text)
          initLoading()
          console.log(_index, '_index')
          if (_index) {
            const pageNum = Math.ceil((_index + 1) / 10)
            skuListRef.value.current = pageNum
          }
          resolve(false)
          return
        } else {
          resolve(true)
        }
      }
      // 编辑和保存的时候需要校验所有必填字段
      // 保存并发布
      if (val == 'addEdit') {
        state.addForm.is_temp = 0
        state.addForm.on_sale = 1
        await addForm.value
          .validate()
          .then(() => {
            if (!state.addForm.send_time) {
              initLoading()
              return message.warning('请选择发货时间')
            }
            save()
            resolve(true)
          })
          .catch((error) => {
            console.log('error', error)
            // addForm.value.scrollToField(error.errorFields[0].name)
            let scrollDom = null
            // 评论组
            if (error.errorFields[0]?.name[0].indexOf('temp[') > -1) {
              scrollDom = document.querySelector('#tag_list')
            } else if (error.errorFields[0]?.name[0].indexOf('attrs_') > -1) {
              scrollDom = document.querySelector('#attribute')
            } else if (error.errorFields[0]?.name[0].indexOf('sku_group_') > -1) {
              scrollDom = document.querySelector('#sku_list')
            } else if (error.errorFields[0]?.name[0].indexOf('skuData') > -1) {
              scrollDom = document.querySelector('#sku_list_price')
            } else if (error.errorFields[0]?.name[0].indexOf('rookie_bond') > -1) {
              scrollDom = document.querySelector('#crossborderInfo')
            } else if (error.errorFields[0]?.name[0].indexOf('user_notice_list') > -1) {
              scrollDom = document.querySelector('#user_notice')
            } else if (error.errorFields[0]?.name[0].indexOf('stopNewYearSendDate') > -1) {
              scrollDom = document.querySelector('#protection')
            } else if (
              error.errorFields[0]?.name[0].includes('min_price') ||
              error.errorFields[0]?.name[0].includes('money')
            ) {
              scrollDom = document.querySelector('#is_open_return')
            } else {
              scrollDom =
                document.querySelector(`[name="${error.errorFields[0].name}"]`) ||
                document.querySelector(`#${error.errorFields[0].name}`) ||
                document.querySelector(`[name="${error.errorFields[0].name[0]}"]`) ||
                document.querySelector(`#${error.errorFields[0].name[0]}`) ||
                document.querySelector(`.${error.errorFields[0].name[0]}`)
            }

            scrollToElement(scrollDom)
            message.warning(error.errorFields[0].errors[0])
            initLoading()
            resolve(false)
          })
      } else if (val == 'edit') {
        // 有id的情况下保存草稿 并且当前商品非草稿状态时 返回上一步保存草稿
        state.addForm.is_temp = 0
        state.addForm.on_sale = 1

        await addForm.value
          .validate()
          .then(() => {
            save(false)
            resolve(true)
          })
          .catch((error) => {
            console.error('error', error)
            addForm.value.scrollToField(error.errorFields[0].name)
            message.warning(error.errorFields[0].errors[0])
            initLoading()
            resolve(false)
          })
      } else {
        // 新增商品时 或者草稿商品 返回上一步保存草稿
        state.addForm.is_temp = 1
        // 草稿的情况下只需要商品名称 暂未完成
        if (!state.addForm.title) {
          message.warning('请输入商品名称')
          initLoading()
          resolve(false)
        } else {
          // 判断是点击的保存草稿按钮，还是上一步
          state.addForm.on_sale = 0
          save(val == 'addDraft')
        }
      }
      resolve(true)
    })
  }
  const weixinProductHandler = (value: any) => {
    state.addForm = { ...state.addForm, ...value }
    state.addForm.changeField = 'title'
    initSkuHandler(state.addForm.sku_list, state.addForm.spec_list)
    moreSkuSwitch(state.addForm.is_unit)
    state.addForm.protection
      .map((v) => (v = Number(v)))
      .forEach((value) => {
        if (state.protectionObj.hasOwnProperty(value)) {
          state.protectionObj[value] = true
        }
      })
  }
  // 新增/编辑接口
  const save = async (isBack = true, ishistory = false) => {
    try {
      if (state.is_disabled) {
        let scrollDom = document.querySelector('#category_ids')
        scrollToElement(scrollDom)
        return message.warning('只能选择赦免类目')
      }
      if (state.addForm.limit_type == 2) {
        if (!state.addForm.limit_once_max_num && !state.addForm.limit_once_min_num && !state.addForm.limit_total_num) {
          return message.warning('请填写限购规则')
        }
        if (
          state.addForm.limit_once_max_num &&
          state.addForm.limit_total_num &&
          state.addForm.limit_once_max_num > state.addForm.limit_total_num
        ) {
          return message.warning('每次最多购买数量不能大于累计最多购买数量')
        }
        if (
          state.addForm.limit_once_min_num &&
          state.addForm.limit_once_max_num &&
          state.addForm.limit_once_min_num > state.addForm.limit_once_max_num
        ) {
          return message.warning('每次最少购买数量不能大于每次最多购买数量')
        }
        if (
          state.addForm.limit_once_min_num &&
          state.addForm.limit_total_num &&
          state.addForm.limit_once_min_num > state.addForm.limit_total_num
        ) {
          return message.warning('每次最少购买数量不能大于累计最多购买数量')
        }
      }
      if (state.addForm.product_video && [0, 4, 5].includes(state.addForm.product_video_status)) {
        const video_type =
          state.addForm.product_video_status == 4
            ? '被下线'
            : state.addForm.product_video_status == 5
              ? '被禁用'
              : '失效'
        return message.warning(`所选视频已${video_type}，请重新选择`)
      }

      if (!state.skuReady) return
      if (!state.addForm.comment_temp_id || state.addForm.comment_temp_id == 0) {
        state.addForm.tag_list = []
      }
      const tagList =
        state.addForm.tag_list &&
        state.addForm.tag_list.map((item) => {
          return {
            ...item,
            tag_num: Number(item.tag_num)
          }
        })
      if (state.addForm.is_open_return == 1) {
        if (state.addForm.rebate_config.type == 1) {
          state.addForm.rebate_config.list = [
            {
              min_price: undefined, //
              money: undefined
            }
          ]
        } else {
          state.addForm.rebate_config.money = undefined
        }
      } else {
        state.addForm.rebate_config = undefined
      }

      let params = {
        ...state.addForm,
        ...handleSkuData(skuData),
        tag_list: tagList,
        status: state.addForm.is_temp == 1 ? 0 : 1,
        category_id: +state.addForm.category_ids[state.addForm.category_ids.length - 1],
        category_ids: state.addForm.category_ids.join(','),
        price: Number(state.addForm.price),
        old_price: Number(state.addForm.old_price),
        // stock_num: state.addForm.stock_num ? Number(state.addForm.stock_num) : 0,
        protection: Object.keys(state.protectionObj)
          .filter((key) => state.protectionObj[key])
          .join(','),
        useShop: 'shop_id', //店铺id
        image: state.addForm.image_ids[0],
        image_ids: state.addForm.image_ids.join(','),
        // undeliver_id: state.addForm.freight_type == 3 ? Number(state.addForm.undeliver_id) : 0,
        deliver_temp_id: state.addForm.freight_type == 3 ? Number(state.addForm.deliver_temp_id) : 0,
        freight: state.addForm.freight_type == 2 ? +state.addForm.freight : 0,
        sales_initial: +state.addForm.sales_initial,
        is_async_gdt: state.addForm.is_async_gdt ? 1 : 2,
        gdt_library_id: Number(state.addForm.gdt_library_id),
        gdt_category_id: state.addForm.gdt_category_id
          ? state.addForm.gdt_category_id[state.addForm.gdt_category_id.length - 1]
          : 0,
        gdt_aptitude_img: JSON.stringify(state.addForm.gdt_aptitude_img) || '',
        is_save_history: state.addForm.isHistory ? 1 : 2,
        send_gift_switch: state.addForm.type == 1 ? state.addForm.send_gift_switch : 0,

        rebate_config: JSON.stringify(state.addForm.rebate_config),
        image_ignores: [...state.img_ignores, ...state.detail_ignores]
      }
      // 多规格不开启的时候提交去除sku spec
      if (state.addForm.is_unit == 2) {
        params.sku_list = []
        params.spec_list = []
      }
      if (state.addForm.stopNewYearSend) {
        params.year_stop_send = 1
        params.stop_start_time = dayjs(state.addForm.stopNewYearSendDate[0]).unix()
        params.stop_end_time = dayjs(state.addForm.stopNewYearSendDate[1]).unix()
      } else {
        params.year_stop_send = 2
        params.stop_start_time = undefined
        params.stop_end_time = undefined
      }
      // 草稿状态 如果价格为0情况下可以不传
      if (state.addForm.is_temp == 1) {
        if ([null, ''].includes(state.addForm.price)) {
          delete params.price
        }
        if ([null, ''].includes(state.addForm.old_price)) {
          delete params.old_price
        }
        // if ([null, ''].includes(state.addForm.stock_num)) {
        //   delete params.stock_num
        // }
      }
      // 删除无效的商品属性
      if (params.attribute) {
        params.attribute = params.attribute.filter((v) => v.attr_val && v.name)
      }

      if (!params.id && route.query?.id) {
        params.id = ~~route.query?.id
      }

      if (!params.comment_temp_id) {
        params.comment_temp_id = 0
      }
      if (!params.question_temp_id) {
        params.question_temp_id = 0
      }
      if (!params.shop_address_id) {
        params.shop_address_id = 0
      }

      if (state.isWeixinshop) {
        params.type = 4
        params.wechat_product_qrcode = undefined
      } else {
        params.wechat_product_qrcode = undefined
      }
      await handlePicReload(params)
      await handleDetailImgField(params)
      // return
      console.log('--- params ---', params)

      // 前端删除弃用字段 detail
      delete params.detail
      delete params.status_txt

      // 不是跨境电商类型的，不传对应参数
      if (state.addForm.type === 1) {
        let arr = [
          'taxes',
          'cross_border_type',
          'bonded_id',
          'bonded_name',
          'bonded_product_id',
          'bonded_product_name',
          'rookie_account_id',
          'warehouse_name',
          'rookie_bond',
          'tax_rate',
          'customs',
          'is_return_bonded',
          'origin'
        ]
        arr.forEach((item) => {
          delete params[item]
        })
      }
      if (!isMedicalSpecificFn(Number(state.addForm.category_ids?.[0]))) {
        params.shop_certificate_product_id = undefined
        params.shop_certificate_product = []
      }

      if (userNoticeRef?.value) {
        params.user_notice = userNoticeRef?.value.userNoticeEmit()
      }
      if (ishistory) {
        //勾选历史版本运行保存
        handleSaveVersion(params)
      } else {
        if (+route.query?.isWeixinshop) {
          const data = await weixinShopRef?.value?.checkGoods()
          if (!data?.product_id) return
        }

        let res = route.query?.id ? await editProduct(params) : await createProduct(params)
        message.success(res.msg)
        // 编辑的时候运行保存 后返回
        if (isBack) {
          routerBack({ name: 'ShopGoodsList', params: { page: route.query?.id ? 'edit' : 'add' } })
        } else {
          //新增商品时 返回需要在路由添加id
          router.replace({
            name: 'GoodsPublish',
            query: { id: route.query.id ? route.query.id : res.data.id, ...route.query }
          })
        }
        setTimeout(() => {
          isCate.value = true
        }, 500)
      }
    } catch (error) {
      console.error(error)
    } finally {
      console.log('/*11111111111', isCate.value)
      emit('event', {
        cmd: 'loading',
        data: {
          type: state.saveType,
          loading: false
        }
      })
    }
  }

  // 处理小程序添加 pic_reload 字段
  const handlePicReload = async (params: any) => {
    try {
      const list = params.image_ids.split(',')
      const pic_reload = await getImageInfoByPath(list)
      params.pic_reload = JSON.stringify(pic_reload)
    } catch (error) {
      console.error(error)
    }
  }

  // 处理商品详情字段
  const handleDetailImgField = async (params: any) => {
    try {
      const list = params.detail_image
      const detail_image = await getImageInfoByPath(list)
      params.detail_image = detail_image.map((v: any) => {
        return {
          src: v.url,
          type: v.type,
          width: v.width,
          height: v.height
        }
      })
      params.detail_image = JSON.stringify(params.detail_image)
    } catch (error) {
      console.error(error)
    }
  }

  // 非配送地区下拉框弹出请求
  const onVisibleChangeUndeliver = (val) => {
    if (val) getNoRegionList()
  }

  // 获取非配送地区数据
  const getNoRegionList = async () => {
    try {
      let res = await setList({ page: 1, page_size: 100 })
      state.areasNos = (res.data?.list || []).map((item) => {
        return {
          label: item.temp_name,
          value: item.id
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  getNoRegionList()

  // 是否开启多规格switch更改
  function moreSkuSwitch(val) {
    state.addForm.changeField = 'is_unit'
    ruleData.price[0].required = val == 2
  }

  // 去配置非配送地区
  const toAreaNo = () => {
    // window.open('/goods/freighttemp', '_blank')
    const to = router.resolve({
      path: '/mall/goods/freight'
    })
    window.open(to.href, '_blank')
  }

  // 去管理商家地址页面
  const toShopAddress = () => {
    const to = router.resolve({
      path: '/mall/goods/address'
    })
    window.open(to.href, '_blank')
  }

  // 商品属性change
  const attributeChange = () => {
    state.addForm.changeField = 'attribute'
  }

  // 表单校验改变时动态更新右侧发布助手状态
  const validateChange = (event, value) => {
    previewRef.value.validateChange(event, value, skuData, state.addForm)
  }

  // 获取问答组列表
  const getQaTempList = async (query) => {
    try {
      // 请求接口
      const res = await getQuestionTempList({ page: 1, page_size: 100, name: query || '' })
      state.qaGroups = res.data?.list || []
    } catch (error) {
      console.error(error)
    }
  }
  // 远程搜索问答组
  const qaRemoteMethod = async (query) => {
    try {
      state.qaLoading = true
      if (query) {
        await getQaTempList(query)
      } else {
        await getQaTempList()
      }
    } catch (error) {
      state.qaGroups = []
      console.error(error)
    } finally {
      state.qaLoading = false
    }
  }

  // 获取评论组列表
  const getCommentsTempList = async (query) => {
    try {
      // 请求接口
      const res = await getCommentTempList({
        page: 1,
        page_size: 100,
        is_new: route.query?.id ? 0 : 1,
        product_id: route.query?.id || undefined,
        name: query || ''
      })
      state.commentGroups = res.data?.list || []
      console.log(state.commentGroups, 'state.commentGroups')
    } catch (error) {
      console.error(error)
    }
  }
  // 远程搜索评论组
  const commentRemoteMethod = async (query) => {
    // if (option.label.indexOf(value) != -1) {
    //   return true
    // } else {
    //   return false
    // }
    console.log(query, 'query')

    try {
      state.commentLoading = true
      if (query) {
        await getCommentsTempList(query)
      } else {
        await getCommentsTempList()
      }
    } catch (error) {
      state.commentGroups = []
      console.error(error)
    } finally {
      state.commentLoading = false
    }
  }
  // 获取售后地址列表
  const getSaleAddressList = async (isRefresh = false) => {
    try {
      // 请求接口
      const res = await shopAreaList({})
      state.afterSaleAddress = res.data || []
      if (isRefresh) {
        message.success('刷新成功')
      }
    } catch (error) {
      console.error(error)
    }
  }
  //调用erp列表数据,展示默认数据
  const geterp = async () => {
    try {
      if (useInfo.value.is_private_platform) return
      let res = await authList({ page: 1, page_size: 1000, is_bind: true })
      state.erpList = res.data?.list || []
      let _list = cloneDeep(res.data?.list) || []
      const _default = _list.filter((item) => item.enable == 2)
      state.addForm.erp_id = state.addForm.erp_id
        ? state.addForm.erp_id
        : (_default.length > 0 && _default[0].id) || state.addForm.erp_id || null
    } catch (error) {
      console.log(error, 'error')
    }
  }
  const changeErp = async (e, data) => {
    console.log(e, data, state.addForm.erp_id, 'e,data')
  }
  // 获取类目下的所有违禁词
  const getContraband = async () => {
    // await addForm.value.clearValidate()

    // todo 提供新接口
    let res = await checkString({ category_ids: state.addForm.category_ids?.join(',') || '' })

    if (typeof res.data === 'object' && Array.isArray(res.data) && res.data.length) {
      state.contrabandList = res.data
    } else {
      state.contrabandList = []
    }
    if (!state.initContrabandFirst) {
      // 第二次及以后就要触发校验
      nextTick(() => {
        addForm.value.validate()
      })
    }
  }
  // 初始化
  const initData = async () => {
    getQaTempList()
    getCommentsTempList()
    getSaleAddressList()
    if (route.query?.id) {
      await getCategoryListNew()
      await getInfo()
      await geterp()
    } else {
      DataDiff.init({}, {}, '')
      setTimeout(() => {
        state.formChange = false
        state.skuReady = true
        geterp()
      }, 200)
      state.addForm.recommend_tag = isMedical.value ? '品牌特卖' : '好物推荐'
      state.addForm.recommend_tag_txt = isMedical.value ? '品牌特卖，正品保障' : '爆款热销，正品保障'
      state.addForm.sale_tag_txt = isMedical.value ? '品质保证，极速售后' : '品质保证，极速售后'
    }
    if (+route.query?.isWeixinshop) {
      state.isWeixinshop = +route.query.isWeixinshop
    }
    await getContraband()
  }

  const validSalesType = (_rule, value, _callback) => {
    if (!value && state.addForm.sales_type == 3) {
      // callback(new Error('请输入全渠道真实销量'))
      return new Promise((_resolve, reject) => {
        reject('请输入全渠道真实销量')
      })
    } else {
      // callback()
      return Promise.resolve()
    }
  }
  // select搜索
  const onFiterOption = (value: any, option: any) => {
    if (option.label.indexOf(value) != -1) {
      return true
    } else {
      return false
    }
  }

  // onGroup
  const onGroup = () => {
    state.addForm.changeField = 'sales_type'
    addForm.value.clearValidate(['sales_initial'])
  }
  const onChangeFreightType = () => {
    state.addForm.changeField = 'freight_type'
    const type = state.addForm.freight_type
    const status = type == 1 || (type == 2 && !state.addForm.freight) || (type == 3 && !state.addForm.deliver_temp_id)
    if (type !== 2) {
      addForm.value.clearValidate(['freight'])
    }

    validateChange('freight_type', status)

    if (type == 1) {
      state.addForm.freight = undefined
      state.addForm.deliver_temp_id = undefined
    } else if (type == 2) {
      state.addForm.deliver_temp_id = undefined
    } else if (type == 3) {
      state.addForm.freight = undefined
    }
  }
  // 检查 form 表单数据是否变化
  const checkFormChange = () => {
    return state.formChange
  }

  // 商品规格设置
  const handleSkuChange = (v, flag) => {
    // state.specList = [...v]
    skuChange(v)
    state.addForm.specList = v
    if (flag) {
      state.specList = v
    }
    skuChangeValue()
  }

  const onUpPicChange = (i) => {
    addForm.value.clearValidate(['pic' + i.value])
  }

  // 获取商品类目列表
  const getGdtCategorlList = async () => {
    try {
      let res = await getCategoryList({})
      state.cateList = res.data
    } catch (error) {}
  }
  getGdtCategorlList()

  // 获取商品库
  const getLibrarylList = async () => {
    try {
      let res = await getGdtLibraryList({})
      state.goodsStoreList = res.data
    } catch (error) {}
  }
  getLibrarylList()

  const handleCascaderChange = (value, selectedOptions) => {
    console.log('Selected value:', value) // 选中的id
    // state.addForm.category_id = value[value.length - 1]
    state.addForm.gdt_category_ids = value.join(',')
    state.addForm.gdt_category_name = selectedOptions.map((o) => o.name).join('>')
  }
  const changeType = (e) => {
    state.addForm.is_unit = 2
    state.addForm.skuData.list = []
    setType(state.addForm.type)
  }
  const skuChangeValue = () => {
    const { sku_list, spec_list } = handleSkuData(state.addForm.skuData)
    state.addForm.sku_list = sku_list
    state.addForm.spec_list = spec_list
  }
  const delChange = ({ arr, now }: { arr: boolean[]; now: string[] }) => {
    skuIptRef?.value?.delChange({ arr, now })
  }
  const protectionChange = (val: any, event: any) => {
    state.addForm.changeField = 'protection'
    state.addForm.protection = Object.keys(state.protectionObj).filter((key) => state.protectionObj[key])
    if ((val === 5 || val === 6) && event.target.checked) {
      Modal.info({
        title: '提示',
        okText: '我已知晓',
        content: `您选择${val === 6 ? '假一赔十' : '正品保障'}，该服务保障将会在小程序商品详情页中对用户开放展示，请您谨慎选择。`,
        onOk() {
          Modal.destroyAll()
        }
      })
    }
  }

  // 全屏SKU编辑相关方法
  const openFullscreenSku = () => {
    // 将当前数据同步到全屏模式
    state.fullscreenSpecList = JSON.parse(JSON.stringify(state.specList))
    state.fullscreenSkuList = JSON.parse(JSON.stringify(state.skuList))
    state.fullscreenSkuData = JSON.parse(JSON.stringify(state.addForm.skuData))
    state.fullscreenTimestamp = Date.now()
    state.fullscreenSkuVisible = true

    console.log('打开全屏时的数据:', {
      specList: state.specList,
      fullscreenSpecList: state.fullscreenSpecList
    })
  }

  const closeFullscreenSku = () => {
    console.log('关闭全屏时的数据:', {
      fullscreenSpecList: state.fullscreenSpecList,
      fullscreenSkuList: state.fullscreenSkuList,
      fullscreenSkuData: state.fullscreenSkuData
    })

    // 将全屏模式的数据同步回主页面
    state.specList = JSON.parse(JSON.stringify(state.fullscreenSpecList))
    state.skuList = JSON.parse(JSON.stringify(state.fullscreenSkuList))
    state.addForm.skuData = JSON.parse(JSON.stringify(state.fullscreenSkuData))
    state.timestamp = Date.now()

    // 触发主页面的SKU变化事件
    skuChange(state.specList, true)
    skuChangeValue()

    state.fullscreenSkuVisible = false
  }

  const handleFullscreenSkuChange = (v: any, flag: any) => {
    console.log('全屏SKU变化:', { v, flag })

    // 直接更新全屏模式的规格数据
    state.fullscreenSpecList = JSON.parse(JSON.stringify(v))

    // 如果需要重新生成SKU列表
    if (flag) {
      // 临时保存当前主页面数据
      const originalSpecList = state.specList
      const originalSkuList = state.skuList
      const originalSkuData = state.addForm.skuData

      try {
        // 临时设置为全屏数据来生成SKU
        state.specList = state.fullscreenSpecList
        state.skuList = state.fullscreenSkuList
        state.addForm.skuData = state.fullscreenSkuData

        // 执行SKU变化逻辑
        skuChange(v, flag)

        // 保存生成的结果到全屏数据
        state.fullscreenSpecList = JSON.parse(JSON.stringify(state.specList))
        state.fullscreenSkuList = JSON.parse(JSON.stringify(state.skuList))
        state.fullscreenSkuData = JSON.parse(JSON.stringify(state.addForm.skuData))

        console.log('生成的全屏SKU数据:', {
          fullscreenSpecList: state.fullscreenSpecList,
          fullscreenSkuList: state.fullscreenSkuList,
          fullscreenSkuData: state.fullscreenSkuData
        })
      } finally {
        // 恢复主页面数据
        state.specList = originalSpecList
        state.skuList = originalSkuList
        state.addForm.skuData = originalSkuData
      }
    } else {
      // 如果不需要重新生成SKU列表，只更新规格数据
      state.fullscreenSpecList = JSON.parse(JSON.stringify(v))
    }
  }

  const handleFullscreenSkuChangeValue = () => {
    // 全屏模式下的SKU值变化处理
    console.log('全屏SKU值变化')

    // 临时保存当前主页面数据
    const originalSkuData = state.addForm.skuData

    try {
      // 临时设置为全屏数据来处理SKU值变化
      state.addForm.skuData = state.fullscreenSkuData
      const { sku_list, spec_list } = handleSkuData(state.fullscreenSkuData)

      // 更新全屏数据（这里主要是为了保持数据一致性）
      console.log('处理后的SKU数据:', { sku_list, spec_list })
    } finally {
      // 恢复主页面数据
      state.addForm.skuData = originalSkuData
    }
  }

  const handleFullscreenDelChange = ({ arr, now }: { arr: boolean[]; now: string[] }) => {
    fullscreenSkuIptRef?.value?.delChange({ arr, now })
  }

  const handleFullscreenUpPicChange = (_i: any) => {
    state.fullscreenTimestamp = Date.now()
  }

  defineExpose({
    ruleCheck,
    checkFormChange
  })
  provide('addForm', addForm)
  provide(
    'formRef',
    computed(() => addForm.value)
  )
  provide('addDiffClass', addDiffClass)
  provide(
    'contrabandList',
    computed(() => state.contrabandList)
  )
  provide(
    'isMedicalSpecific',
    computed(() => {
      return isMedicalSpecificFn(Number(state.addForm?.category_ids?.[0]))
    })
  )
</script>

<style lang="scss" scoped>
  :deep(.other_style .ant-row .ant-form-item-label) {
    label {
      width: 124px !important;
    }
    width: 124px !important;
  }
  .freight_type_child_label {
    display: inline-block;
    width: 60px;
  }
  .areaUndeliver {
    position: relative;
    margin-left: 10px;

    :deep(.el-select) {
      width: 100%;
    }

    .input_desc {
      position: absolute;
    }
  }

  .block {
    :deep(.el-form-item__content) {
      display: block;
    }
  }

  :deep(.el-radio__label) {
    display: flex;
    align-items: center;
  }

  .flex_wrap {
    flex-wrap: wrap;
  }

  .edit_wrapper {
    display: flex;
    :deep(.ant-card) {
      flex: 1;
      display: block;
      overflow: hidden;
      //
    }
    :deep(.ant-form-item) {
      .popup {
        position: relative;
      }
      .popup::before {
        content: '';
        display: inline-block;
        position: absolute;
        top: 0;
        background-color: #feea3d;
        opacity: 0;
        z-index: 10;
        border-radius: 5px;
        width: 0;
        height: 0%;
        -webkit-animation-name: pop-data;
        animation-name: pop-data;
        -webkit-animation-duration: 3s;
        animation-duration: 3s;
        pointer-events: none;
      }
    }
  }

  .mar {
    margin: 0 0 20px 100px;
  }

  .header_title {
    line-height: 16px;
    font-size: 16px;
    font-weight: 500;
    color: #080f1e;
    margin-bottom: 20px;
    padding: 0px 5px 0;
  }

  .carousel_img {
    width: 300px;
    height: 400px;
  }

  .demo_form {
    // width: calc(100% - 418px);
    flex: 1;
    // width: 100%;
    padding: 0;
    background: #ffffff;
    border-radius: 10px;
    box-sizing: border-box;
    margin-bottom: 24px;

    :deep(.el-form-item__label) {
      line-height: 40px;
      height: 40px;
      padding-right: 15px;
    }

    :deep(.el-input),
    :deep(.el-select) {
      max-width: 700px;
    }

    :deep(.el-form-item.bottom) {
      margin-bottom: 0 !important;
    }

    :deep(.el-color-picker__trigger) {
      // width: 60px;
      // height: 100%;
      // padding: 7px 10px;
      // background: #f3f6fc;
      border-radius: 6px;
      border: none;
      justify-content: flex-start;

      &:hover {
        border: none;
      }

      .el-color-picker__color {
        position: static;
        width: 20px;
        height: 20px;
        border: none;
      }

      .is-icon-arrow-down {
        position: absolute;
        top: 7px;
        left: 64px;
        color: #404040;
        width: 0;
        height: 0;
        border: 5px solid;
        border-color: #404040 transparent transparent transparent;
      }
    }

    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #404040;
    }

    .color_picker {
      position: relative;
      height: 34px;

      .caret_bottom {
        position: absolute;
        right: 8px;
        bottom: 10px;
      }
    }

    .color_picker1 {
      width: 95px;
      height: 34px;
      background: #f3f6fc;
      border-radius: 6px;
      margin-left: 10px;
      // margin-bottom: 24px;
      color: #404040;
      display: flex;
      flex-shrink: 0;
      justify-content: center;
      align-items: center;
      user-select: none;
      cursor: pointer;
      position: relative;
      padding-left: 10px;
      box-sizing: border-box;

      .color {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: linear-gradient(90deg, #ff9000 0%, #ff5000 100%);
        z-index: 1;
        pointer-events: none;
      }

      :deep(.el-color-picker),
      .color {
        width: 20px;
        height: 20px;
        position: absolute;
        top: 50%;
        left: 10px;
        transform: translateY(-50%);

        .el-color-picker__trigger {
          padding: 0 !important;
          width: 20px;
          height: 20px;
        }
      }
    }

    .cate_name {
      min-width: 309px;
      height: 34px;
      line-height: 34px;
      padding: 0px 15px;
      background: #f0f2f6;
      border-radius: 6px;
      font-size: 14px;
      color: #080f1e;
      box-sizing: border-box;
      display: inline-block;
      box-sizing: border-box;
    }

    .title_limit {
      color: #e63030;
      font-size: 12px;
    }

    .input_desc {
      margin-top: 10px;
      font-size: 12px;
      color: #85878a;
      line-height: 10px;

      .set {
        color: var(--primary-color);
        cursor: pointer;
      }

      .e6 {
        color: #e63030;
      }

      &.pl5 {
        padding: 0 5px 7px;
      }
    }

    .specs_box {
      .input_desc {
        margin-top: 0;
      }
    }

    .switch {
      line-height: 32px;

      .label {
        color: #404040;
        margin-right: 13px;
      }
    }

    .image_list {
      flex-wrap: wrap;
      .item {
        justify-content: center;
      }
      :deep {
        .ant-image {
          height: 100%;
        }
      }
      .item,
      .upload {
        position: relative;
        width: 101px;
        height: 101px;
        background: #f1f5fc;
        border-radius: 4px;
        cursor: pointer;
        // overflow: hidden;
        margin-bottom: 28px;
      }

      .item {
        margin-right: 21px;

        :deep(.el-image) {
          width: 100%;
          height: 100%;
        }

        .name {
          height: 30px;
          background: linear-gradient(0deg, #000000, rgba(0, 0, 0, 0.01));
          border-radius: 0px 0px 4px 4px;
          color: #fff;
          position: absolute;
          bottom: 0;
          width: 100%;
          text-align: center;
          box-sizing: border-box;
          padding: 0 5px;
        }

        .del_img {
          position: absolute;
          top: -10px;
          right: -7px;
          display: none;
        }

        &:hover {
          .del_img {
            display: inline-block;
          }
        }
      }

      .upload {
        border: 1px dashed #d8dde8;

        .add {
          width: 26px;
        }

        .desc {
          color: #999;
          margin-top: 9px;
          line-height: normal;
        }
      }
    }

    .vaild_desc {
      margin-top: 5px;
      line-height: 20px;
    }
    :deep(.ant-radio-group.freight) {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .el-radio {
        margin-right: 0;
      }
    }

    :deep(.input_number) {
      margin-left: 4px;
      margin-right: 6px;

      .el-input__inner {
        height: 33px;
        text-align: left;
      }

      .el-input-number {
        margin-left: 4px;
      }
    }

    :deep(.ant-checkbox-group.guarantees) {
      width: 100%;
      padding: 13px 5px 0;

      .ant-checkbox-wrapper {
        width: 100%;
        min-height: 41px;
        align-items: flex-start;
        height: auto;
        margin-bottom: 20px;
        .ant-checkbox {
          align-self: flex-start;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }

      .desc {
        margin-top: 3px;
        color: #404040;
        font-size: 12px;
        white-space: normal;
        line-height: 1.8;
      }

      .label {
        color: #404040;
        line-height: 16px;
      }
    }

    .deliver_radio {
      padding: 7px 0 1px;

      :deep(.el-radio.el-radio--large) {
        height: 20px;
      }

      .desc2 {
        color: #404040;
        margin-left: 20px;
      }
    }

    .help {
      width: 12px;
      height: 12px;
      margin-left: 4px;
    }

    .w220 {
      width: 220px;
      margin-right: 20px;
    }

    .mb0 {
      margin-bottom: 0;
    }

    .mt3 {
      margin-top: -3px;
    }
  }
  :deep(.ant-card .ant-card-body) {
    padding-bottom: 0;
  }
  .right_box {
    width: 408px;
    margin-left: 10px;
  }

  .mt10 {
    margin-top: 10px;
  }
  .mb_10 {
    margin-bottom: 10px;
  }
  .mb_0 {
    margin-bottom: 0;
  }
  .mb_24 {
    margin-bottom: 24px;
  }
  .reset_for_item {
    position: relative;
    &.ant-radio-wrapper {
      width: 50%;
      :deep {
        .ant-input-number,
        .areaUndeliver {
          min-width: 300px;
          margin-left: 10px;
        }

        .ant-form-item-explain-error {
          position: absolute;
          // top: 50px;
          top: auto;
          left: 69px;
        }
      }
    }
  }
  .reset_for_item.no_top {
    position: relative;
    &.ant-radio-wrapper {
      :deep {
        .ant-form-item-explain-error {
          top: auto;
        }
      }
    }
  }
  .moban {
    position: relative;
    &.ant-radio-wrapper {
      width: 50%;
      :deep {
        .ant-input-number,
        .areaUndeliver {
          min-width: 300px;
          margin-left: 10px;
        }

        .ant-form-item-explain-error {
          font-size: 12px;
          position: absolute;
          top: 59px !important;
          left: 65px;
          transform: translateX(5px);
        }
      }
    }
  }
  .ant-radio-group {
    display: flex;
  }
  .ant-radio-wrapper {
    line-height: 32px;
    :deep {
      .ant-radio {
        align-self: flex-start;
        width: 16px;
        display: flex;
        align-items: center;
        align-self: flex-start;
        margin-top: 8px;
      }
    }
  }
  .inline_block {
    display: inline-block;
  }
  .min_w_200 {
    min-width: 200px;
  }
  .gray {
    color: v-bind('themeVar.textColorGray');
  }
  .label_red {
    :deep(.ant-form-item-label) {
      & > label {
        color: var(--error-color);
      }
    }
  }

  @keyframes pop-data {
    0% {
      opacity: 0.5;
      width: 100%;
      height: 100%;
    }
    99% {
      opacity: 0;
      width: 100%;
      height: 100%;
    }
    100% {
      opacity: 0;
      width: 0;
      height: 0%;
    }
  }
  .item-type {
    position: absolute;
    bottom: -23px;
  }
  .image_list {
    flex-wrap: wrap;
    flex-direction: column;

    .item,
    .upload {
      position: relative;
      width: 101px;
      height: 101px;
      background: #f1f5fc;
      border-radius: 4px;
      cursor: pointer;
      // overflow: hidden;
      margin-bottom: 10px;
    }

    .item {
      margin-right: 21px;
      .imgg {
        width: 100%;
        height: 100%;
        border-radius: 4px;
      }

      :deep(.el-image) {
        width: 100%;
        height: 100%;
      }

      .name {
        height: 30px;
        background: linear-gradient(0deg, #000000, rgba(0, 0, 0, 0.01));
        border-radius: 0px 0px 4px 4px;
        color: #fff;
        position: absolute;
        bottom: 0;
        width: 100%;
        text-align: center;
        box-sizing: border-box;
        padding: 0 5px;
      }

      .del_img {
        position: absolute;
        top: -10px;
        right: -7px;
        display: none;
      }

      &:hover {
        .del_img {
          display: inline-block;
        }
      }
    }

    .upload {
      border: 1px dashed #d8dde8;

      .add {
        width: 26px;
      }

      .desc {
        color: #999;
        margin-top: 9px;
        line-height: normal;
      }
    }
  }
  .video_list {
    flex-wrap: wrap;
    flex-direction: column;

    .item,
    .upload {
      position: relative;
      width: 102px;
      height: 160px;
      background: #f1f5fc;
      border-radius: 4px;
      cursor: pointer;
    }

    .item {
      margin-right: 21px;
      .imgg {
        width: 100%;
        height: 100%;
        border-radius: 4px;
      }

      :deep(.el-image) {
        width: 100%;
        height: 100%;
      }

      .name {
        height: 30px;
        background: linear-gradient(0deg, #000000, rgba(0, 0, 0, 0.01));
        border-radius: 0px 0px 4px 4px;
        color: #fff;
        position: absolute;
        bottom: 0;
        width: 100%;
        text-align: center;
        box-sizing: border-box;
        padding: 0 5px;
      }

      .del_img {
        position: absolute;
        top: -10px;
        right: -7px;
        display: none;
      }

      &:hover {
        .del_img {
          display: inline-block;
        }
      }
    }

    .upload {
      border: 1px dashed #d8dde8;

      .add {
        width: 26px;
      }

      .desc {
        font-size: 12px;
        margin-top: 16px;
        line-height: normal;
      }
    }
  }
  .item-mark {
    position: absolute;
    width: 100px;
    height: 158px;
    background: #000;
    z-index: 100;
    flex-direction: column;
    opacity: 0.58;
    left: 1px;
    top: 1px;
  }
  .error {
    border: 1px solid red;
  }
  .guarantee-item {
    padding: 0 5px;
    :deep(.ant-checkbox-wrapper) {
      display: flex;
      align-items: flex-start;
      width: 100%;
      min-height: 41px;
      height: auto;
      margin-bottom: 20px;
      .ant-checkbox {
        align-self: flex-start;
      }
      .desc {
        margin-top: 3px;
        color: #404040;
        font-size: 12px;
        white-space: normal;
        line-height: 1.8;
      }
      .label {
        color: #404040;
        line-height: 16px;
      }
    }
    &:first-child {
      margin-top: 13px;
    }
  }
  .stop-newYear-send-wrapper {
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #eaedf7;
    :deep(.ant-checkbox-wrapper) {
      display: flex;
      align-items: center;
      width: 100%;
      min-height: 20px;
      margin-bottom: 0px;
      height: auto;
      .ant-checkbox {
        align-self: center;
        transform: translateY(-1px);
      }
    }
    .date-wrapper {
      margin-top: 12px;
    }
    .desc {
      margin-top: 3px;
      color: #404040;
      font-size: 12px;
      white-space: normal;
      line-height: 1.8;
      .exmple {
        color: var(--primary-color);
        cursor: pointer;
      }
    }
  }
  .label_red {
    color: var(--error-color);
    border-color: var(--error-color);
  }

  /* 全屏SKU编辑弹窗样式 */
  :global(.fullscreen-sku-modal) {
    .ant-modal {
      max-width: 100vw;
      margin: 0;
      padding: 0;
    }

    .ant-modal-content {
      height: 100vh;
      border-radius: 0;
    }

    .ant-modal-body {
      height: calc(100vh - 40px);
      padding: 0;
    }
  }

  .fullscreen-sku-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .fullscreen-sku-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
  }

  .fullscreen-footer {
    padding: 20px;
    text-align: center;
    border-top: 1px solid #f0f0f0;
    background: #fff;
  }
</style>
