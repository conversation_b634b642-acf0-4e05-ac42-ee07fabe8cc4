<template>
  <DesTablePage class="page_main common_page_warp">
    <template #title>开放平台</template>
    <!-- <template #extra>
      <a-space>
        <a-button type="primary" @click="onButton(null, 'guide')">操作指引</a-button>
        <a-button type="primary" @click="onButton(null, 'add')">新增</a-button>
      </a-space>
    </template> -->
    <template #tableWarp>
      <!-- <SearchBaseLayout :data="searchList" @changeValue="changeValue" :actions="actions" /> -->

      <div class="platform grid">
        <template v-for="(item, index) in erpicon" :key="index">
          <div :class="['platform-box', erpIndex === index ? 'active' : '']" @click="handleClick(item, index)">
            <img class="img" :src="item.url" alt="" />
            <img v-show="erpIndex === index" class="active_icon" src="@/assets/images/erp/choose.png" alt="" />
          </div>
        </template>
      </div>
      <div>
        <a-space v-if="erpId === 4" class="flex button1">
          <div>
            <a-button type="primary" @click="onButton(null, 'guide')">操作教程</a-button>
            <a-button type="primary" @click="onButton(null, 'add')">新增</a-button>
          </div>
        </a-space>
        <a-space class="flex button2" v-else>
          <div style="color: red">在未配置接口、订单、商品的情况时，默认拥有全部权限</div>
          <div>
            <a-button type="primary" @click="onInstructions(erpId)">操作教程</a-button>
            <a-button type="primary" @click="onButton(null, 'add_app')">添加应用</a-button>
          </div>
        </a-space>
      </div>
      <TableZebraCrossing :data="tableData" @change="pageChange" class="m-t-16px">
        <template #bodyCell="{ scope }">
          <template v-if="scope.column.key === 'co_id'">
            <div>
              <!-- <span>{{ scope.record.erp_id == 1 ? '聚水潭' : scope.record.erp_id == 2 ? '旺店通' : '店管家' }}</span> -->
              <span>{{ scope.record.co_id }}</span>
              <span v-if="scope.record.enable == 2" class="erp_id">默认</span>
            </div>
          </template>
          <template v-if="scope.column.key === 'erp_shop_id'">
            <span>{{ scope.record.erp_shop_id || '--' }}</span>
          </template>
          <template v-if="scope.column.key === 'erp_shop_name'">
            <span>{{ scope.record.erp_shop_name || '--' }}</span>
          </template>
          <template v-if="scope.column.key === 'updated_at'">
            <span>{{ moment(scope.record.updated_at).format('YYYY-MM-DD HH:mm:ss') || '--' }}</span>
          </template>
          <template v-if="scope.column.key === 'expires_at'">
            <span>{{ moment(scope.record.expires_at).format('YYYY-MM-DD HH:mm:ss') || '--' }}</span>
          </template>
          <template v-if="scope.column.key === 'created_at'">
            <span>{{ moment(scope.record.created_at).format('YYYY-MM-DD HH:mm:ss') || '--' }}</span>
          </template>
          <template v-if="scope.column.key === 'operation'">
            <a-space>
              <a-button type="link" @click="onButton(scope.record, 'default')" v-if="scope.record.enable == 2"
                >取消默认</a-button
              >
              <a-button type="link" @click="onButton(scope.record, 'default')" v-else>设为默认</a-button>
              <a-button type="link" @click="onButton(scope.record, 'copy_accredit')">复制授权</a-button>
              <a-button type="link" @click="onButton(scope.record, 'reauthorization')">重新授权</a-button>
              <a-button type="link" @click="onButton(scope.record, 'edit_shop')">店铺配置</a-button>
              <!-- <a-button type="link" @click="onButton(scope.record, 'rule_configuration')">规则配置</a-button> -->
            </a-space>
          </template>
          <template v-if="scope.column.key === 'action'">
            <div class="handle_btns">
              <div class="">
                <a-button type="link" @click="onButton(scope.record, 'reset')">重置app_secret</a-button>
                <a-button type="link" @click="onButton(scope.record, 'interface')">配置接口</a-button>
                <a-button type="link" @click="onButton(scope.record, 'order')">配置订单</a-button>
                <a-button type="link" @click="onButton(scope.record, 'config')">配置商品</a-button>
                <a-button type="link" @click="onButton(scope.record, 'ip')">IP白名单</a-button>
              </div>
            </div>
          </template>
        </template>
      </TableZebraCrossing>
      <a-modal
        v-model:open="data.dialog.visible"
        :title="data.dialog.title"
        :width="data.dialog.width"
        destroyOnClose
        @cancel="cancel"
        @ok="modalOk"
        :footer="data.dialog.type == 'edit_shop' ? string : null"
      >
        <Add v-if="data.dialog.type === 'add'" :item="data.dialog.item" @close="close"></Add>
        <OperateGuide v-if="data.dialog.type === 'guide'" :item="data.dialog.item" @close="close"></OperateGuide>
        <EditShop ref="editShop" v-if="data.dialog.type === 'edit_shop'" :item="data.dialog.item"></EditShop>
        <!-- <RuleConfiguration v-if="data.dialog.type === 'rule_configuration'" :item="data.dialog.item"></RuleConfiguration> -->
        <configInterface v-if="data.dialog.type == 'interface'" :item="data.item" @event="onEvent" />
        <configOrder v-if="data.dialog.type == 'order'" :item="data.item" @event="onEvent" />
        <ConfigGoods v-if="data.dialog.type == 'config'" :item="data.item" @event="onEvent" />
        <IPList v-if="data.dialog.type == 'ip'" :item="data.item" :code="data.item.code" @event="onEvent" />
        <AddApp v-if="data.dialog.type == 'add_app'" :app_type="data.params.app_type" @event="onEvent" />
        <ResetKey
          v-if="data.dialog.type == 'reset'"
          :item="data.item"
          :type="data.type"
          :list="data.item.white_list"
          @event="onEvent"
        />
        <div v-if="data.dialog.type == 'secret'">
          <div class="p-13px">
            您的app_secret是{{ data.app_secret }} <CopyOutlined @click="copy(data.app_secret)" />
          </div>
          <div class="text-end">
            <a-button type="primary" @click="data.dialog.visible = false">关闭</a-button>
          </div>
        </div>
      </a-modal>
    </template>
  </DesTablePage>
</template>
<script setup>
  import moment from 'moment'
  import { authList } from '@/api/common'
  import IPList from './components/IPList.vue'
  import AddApp from './components/AddApp.vue'
  import ConfigGoods from './components/ConfigGoods.vue'
  import ResetKey from './components/ResetKey.vue'
  import wangdt from '@/assets/images/erp/wangdt.png'
  import jiky from '@/assets/images/erp/jiky.png'
  import wangdgj from '@/assets/images/erp/wangdgj.png'
  import dgj from '@/assets/images/erp/dgj.png'
  import jist from '@/assets/images/erp/jist.png'
  import kaifyy from '@/assets/images/erp/kaifyy.png'
  import gyy from '@/assets/images/erp/gyy.png'
  import km from '@/assets/images/erp/km.png'
  import configInterface from './components/configInterface.vue'
  import configOrder from './components/configOrder.vue'
  import { defaultAuth, copyAuth, setAuth, selectAuth, getAppList } from './index.api'
  import { onMounted, onUnmounted, reactive, ref, createVNode, watch } from 'vue'
  import datas from './src/data'
  import Add from './components/add.vue'
  import OperateGuide from './components/OperateGuide.vue'
  import EditShop from './components/edit_shop.vue'
  import { copy } from '@/utils'
  import { CopyOutlined } from '@ant-design/icons-vue'
  // import RuleConfiguration from './components/rule_configuration.vue'
  import { useTheme, useApp } from '@/hooks'
  import { message, Modal } from 'ant-design-vue'
  import { useRouter } from 'vue-router'
  const { useInfo } = useApp()
  import { getConfig, localStg, randomWord } from '@/utils'
  const shopInfo = localStg.get('shopInfo')
  const url = getConfig('API_URL')
  const { themeVar } = useTheme()
  const { columns, searchList, columnsTow } = datas()
  const router = useRouter()
  const erpicon = ref([
    {
      id: 1,
      name: '旺店通',
      url: wangdt,
      app_type: 2
    },
    {
      id: 2,
      name: '吉客云',
      url: jiky,
      app_type: 3
    },
    {
      id: 3,
      name: '网店管家',
      url: wangdgj,
      app_type: 4
    },
    {
      id: 8,
      name: '店管家',
      url: dgj,
      app_type: 5
    },
    {
      id: 6,
      name: '金蝶管易云',
      url: gyy,
      app_type: 6
    },
    {
      id: 7,
      name: '快麦',
      url: km,
      app_type: 7
    },
    {
      id: 4,
      name: '聚水潭',
      url: jist,
      app_type: 0
    },
    {
      id: 5,
      name: '开放应用',
      url: kaifyy,
      app_type: 1
    }
  ])
  const erpIndex = ref(0)
  const erpId = ref(1)
  const handleClick = (item, index) => {
    erpIndex.value = index
    erpId.value = item.id
    data.params.app_type = item.app_type
    getList()
  }
  const editShop = ref(null)
  const data = reactive({
    params: {
      page: 1,
      page_size: 1000,
      app_type: 2
    },
    dialog: {
      title: '',
      visible: false,
      width: null,
      type: '',
      item: null
    },
    app_secret: '',
    type: '',
    permission: ''
  })
  const actions = {
    foldNum: 0,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 6
    }
  }
  // 表格数据
  const tableData = reactive({
    bordered: false,
    scroll: {
      scrollToFirstRowOnChange: true,
      x: 1000
    },
    dataSource: [],
    loading: false,
    columns: columnsTow,
    pagination: {
      hideOnSinglePage: true,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 1000,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  })
  watch(erpId, (newValue) => {
    if (newValue === 4) {
      tableData.columns = columns
    } else {
      tableData.columns = columnsTow
    }
  })
  // 获取列表
  const getList = async () => {
    try {
      tableData.loading = true
      let res = null
      if (erpId.value === 4) {
        res = await authList(data.params)
      } else {
        res = await getAppList(data.params)
      }
      console.log(res.data)
      tableData.dataSource = res.data?.list || []
      tableData.pagination.total = res.data?.total || 0
      // tableData.pagination.current = res.data?.page || 1
    } catch (error) {
      console.log(error, 'error')
    } finally {
      tableData.loading = false
    }
  }

  // 跳转至订单详情
  const onLinkOrder = (row) => {
    // type：1小程序  2拼多多
    if ([2].includes(row.type)) {
      router.push({ name: 'CidOrderIndex', query: { order_num: row.order_num } })
    } else {
      router.push({ name: 'OrderDetails', query: { id: row.order_id } })
    }
  }

  // 跳转至商品
  const onLinkGood = (row) => {
    if (row.type == 2) {
      router.push({ name: 'CidGoodsIndex', query: { product_name: row.product_name } })
    } else {
      router.push({ name: 'ShopGoodsListDetail', query: { id: row.product_id } })
    }
  }
  const urls = `${url}/shop-erp/select-auth?shop_id=${shopInfo.id}&user_id=${shopInfo.user_id}&company_id=${shopInfo.company_id}`
  const onButton = async (row, type) => {
    data.item = row || {}
    switch (type) {
      case 'add':
        // data.dialog = {
        //   title: '授权ERP',
        //   visible: true,
        //   width: 600,
        //   type: 'add',
        //   item: row
        // }
        window.open(urls + `&t=${+new Date() + '_' + randomWord(true, 5, 6)}`, '_blank')
        break
      case 'guide':
        data.dialog = {
          title: '操作指引',
          visible: true,
          width: 700,
          type: 'guide',
          item: row
        }
        break
      case 'default':
        postDefaultAuth(row)
        break
      case 'copy_accredit':
        postCopyAccredit(row)
        break
      case 'reauthorization':
        postReauthorization(row)
        break
      case 'edit_shop':
        data.dialog = {
          title: '账户配置',
          visible: true,
          width: 600,
          type: 'edit_shop',
          item: row
        }
        break
      case 'ip':
        data.dialog = { id: null, title: 'IP白名单', width: 548, visible: true, type: 'ip' }
        break
      case 'add_app':
        data.dialog = { title: '添加应用', width: 548, visible: true, type: 'add_app' }
        break
      case 'config':
        data.dialog = { id: null, title: '配置商品', width: 900, visible: true, type: 'config' }
        break
      case 'secret':
        data.dialog = { id: null, title: '提示', width: 516, visible: true, type: 'secret' }
        break
      case 'reset':
        data.type = 'reset'
        data.dialog = { id: null, title: '身份验证', width: 516, visible: true, type: 'reset' }
        break
      case 'interface':
        data.dialog = { id: null, title: '配置接口', width: 516, visible: true, type: 'interface' }
        break
      case 'order':
        data.dialog = { id: null, title: '配置订单', width: 516, visible: true, type: 'order' }
        break
      default:
        break
    }
  }
  const onEvent = async (state) => {
    console.log('===>', state)
    switch (state.cmd) {
      case 'close':
        data.dialog.visible = false
        await getList()
        break
      case 'add_app':
        data.dialog.visible = false
        await getList()
        data.app_secret = state.data
        onButton({}, 'secret')
        break
      case 'reset':
        data.dialog.visible = false
        data.app_secret = state.data
        onButton({}, 'secret')
        getList()
        break
      case 'confirm':
        data.dialog.visible = false
        getList()
        break
      case 'addIp':
        data.type = 'addIp'
        data.item.white_list = state.data.list
        data.dialog = { id: null, title: '身份验证', width: 516, visible: true, type: 'reset' }
        break
      case 'interface':
        data.type = 'interface'
        data.item.white_list = state.data.permission
        data.dialog = { id: null, title: '身份验证', width: 516, visible: true, type: 'reset' }
        break

      case 'order':
        data.dialog.visible = false
        break
    }
  }
  const modalOk = async () => {
    switch (data.dialog.type) {
      case 'edit_shop':
        const values = await editShop.value.validateFields()
        if (values) {
          try {
            let _form = editShop.value.form || {}
            let params = {
              id: data.dialog?.item?.id,
              erp_shop_id: _form.shop_id,
              erp_shop_name: _form.shop_name,
              sku_rule: _form.sku_rule,
              update_goods: _form.update_goods,
              start_time: moment(_form.start_time).format('YYYY-MM-DDTHH:mm:ssZ'),
              upload_after: _form.upload_after
            }
            let res = await setAuth(params)
            message.success('操作成功')
            data.dialog = {
              title: '',
              visible: false,
              width: null,
              type: '',
              item: null
            }
            await getList()
          } catch (error) {}
        }
        break
      default:
        break
    }
  }
  const close = async () => {
    data.dialog = {
      title: '',
      visible: false,
      width: null,
      type: '',
      item: null
    }
  }
  //设置默认授权
  const postDefaultAuth = async (row) => {
    let text = row.enable == 1 ? '是否设为默认' : '是否取消默认'
    Modal.confirm({
      title: '提示',
      centered: true,
      content: createVNode('div', {}, text),
      async onOk() {
        try {
          await defaultAuth({ id: row.id, enable: row.enable == 1 ? 2 : 1 })
          message.success('操作成功')
          await getList()
        } catch (error) {
          console.error(error)
        } finally {
          Modal.destroyAll()
        }
      },
      onCancel() {
        console.log('Cancel')
      }
    })
  }
  //复制授权
  const postCopyAccredit = async (row) => {
    Modal.confirm({
      title: '提示',
      centered: true,
      content: createVNode('div', {}, '是否复制授权'),
      async onOk() {
        try {
          await copyAuth({ id: row.id })
          message.success('操作成功')
          await getList()
        } catch (error) {
          console.error(error)
        } finally {
          Modal.destroyAll()
        }
      },
      onCancel() {
        console.log('Cancel')
      }
    })
  }
  //重新授权
  const postReauthorization = async (row) => {
    window.open(
      `${url}/shop-erp/select-auth?shop_id=${shopInfo.id}&user_id=${useInfo.value.id}&company_id=${useInfo.value.company_id}&t=${Date.now()}`,
      '_blank'
    )
  }
  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }
  const changeValue = (v) => {
    if (v.status) {
      data.params = {
        ...data.params,
        ...v.formData
        // page: 1
      }
      getList()
    } else {
      data.params = {
        page: 1,
        page_size: 10,
        begin_time: moment().format('YYYY-MM-DD'),
        end_time: moment().format('YYYY-MM-DD')
      }
      data.active = 1
      getList()
    }
  }

  const handleVisiable = (e) => {
    if (e.target.visibilityState === 'visible') {
      getList()
    }
  }
  onMounted(() => {
    document.addEventListener('visibilitychange', handleVisiable)
    getList()
  })
  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisiable)
  })
  const onInstructions = (id) => {
    switch (id) {
      case 1:
        window.open('https://doc.weixin.qq.com/doc/w3_Aa4A9QbJALMdatT2xswTZWoBgrl1P?scode=ANcAJQf6ADwmWVPwtw', '_blank')
        break
      case 2:
        window.open(
          'https://doc.weixin.qq.com/doc/w3_AYgA9QamAP4chHf2nLgTge018810w?scode=ANcAJQf6ADwVA3O6VJAYgA9QamAP4',
          '_blank'
        )
        break
      case 3:
        window.open('http://xy.wdgj.com/news/show-1471.html', '_blank')
        break
      case 4:
        window.open(
          'https://doc.weixin.qq.com/doc/w3_AcQALAbkAKQjqxjoyv1TWa76FNFkw?scode=ANcAJQf6ADw8t0UCtgzyOMwC6qAOA',
          '_blank'
        )
        break
      case 5:
        window.open('https://haoyouduo.apifox.cn', '_blank')
        break
      case 6:
        window.open(
          'https://vip.kingdee.com/knowledge/642392568772044032?productLineId=3&isKnowledge=2&lang=zh-CN',
          '_blank'
        )
        break
      case 7:
        window.open('https://www.yuque.com/kuaimai/qcnxig/bl3tuy3dmw34mc2g?singleDoc#', '_blank')
        break
      case 8:
        window.open('https://m0ij9216j9.feishu.cn/wiki/It0GwAHPJiNYjRk8kBgcWT4vn0b', '_blank')
        break
    }
    // 打开新的网页
  }
</script>

<style lang="scss" scoped>
  .product_id {
    margin-top: v-bind('themeVar.marginSmall');
    width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    font-family: PingFang SC;
    color: #404040;
    line-height: 20px;
  }
  .product_name {
    font-size: 14px;
    color: v-bind('themeVar.infoColor');
    line-height: 24px;
    cursor: pointer;
  }
  @import './src/assets/css/mixin_scss_fn';
  .description-warp {
    border: none;
    @include set_border_radius(--border-radius);
  }
  .column-user-img {
    @include set_node_whb(30px, 30px);
  }
  .description-text {
    @include set_font_config(--font-size-mini, --text-color-gray);
  }
  .description-pl-title span:nth-child(1) {
    @include set_font_config(--font-size-huge, --text-color-base);
  }
  .description-pl-title span:nth-child(2) {
    padding-left: var(--padding-medium);
    @include set_font_config(--font-size, --text-color-gray);
  }
  .tips {
    font-size: 12px;
    font-family: PingFang SC;
    color: v-bind('themeVar.textColorGray');
    line-height: 16px;
  }
  .erp_id {
    font-size: 12px;
    padding: 4px;
    color: #e42424;
    background-color: #fceeee;
    margin-left: 8px;
    border-radius: 2px;
  }
  .platform {
    box-sizing: border-box;
    grid-template-columns: repeat(5, auto);
    gap: 16px;
    margin-bottom: 24px;
    &-box {
      box-sizing: border-box;
      position: relative;
      width: 100%;
      height: 66px;
      background: #f5f7f9;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      .img {
      }
      .active_icon {
        position: absolute;
        top: 0;
        right: 0;
      }
    }
  }
  .active {
    background: #edf0ff;
    border-radius: 6px;
    border: 1px solid #647dff;
  }
  .button1 {
    justify-content: end;
  }
  .button2 {
    justify-content: space-between;
    align-items: center;
  }
</style>
