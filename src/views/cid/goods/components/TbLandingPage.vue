<template>
  <div>
    <div>
      <div class="">
        <a-button class="item-btn" v-if="props.use != 'ad'" type="primary" @click="onShowDialog('add')">
          添加云台落地页
        </a-button>
      </div>
      <div class="custom-searchForm">
        <TableZebraCrossing
          :data="data.tableConfigOptions"
          @change="pageChange"
          :rowSelection="
            type != 'goods'
              ? {
                  selectedRowKeys: data.selectedRowKeys,
                  onChange: selectionEvent,
                  type: 'radio'
                }
              : null
          "
        >
          <template #headerCell="{ scope }">
            <template v-if="['page_type'].includes(scope.column.dataIndex)">
              <div>
                <span>{{ scope.column.title }}</span>
                <a-tooltip>
                  <template #title> 用于直达链接跳转后展示的页面内容 </template>
                  <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
                </a-tooltip>
              </div>
            </template>
          </template>
          <template #bodyCell="{ scope }">
            <template v-if="scope.column.key === 'name'">
              <div class="flex_column flex item-pdd">
                <div class="flex-y-center">
                  <a-tooltip popper-class="toolt" placement="bottomLeft">
                    <span class="text_overflow">{{ scope.record.name || '--' }}</span>
                    <template #title>
                      {{ scope.record.name }}
                    </template>
                  </a-tooltip>
                </div>
              </div>
            </template>
            <template v-if="scope.column.key === 'remark'">
              <div class="flex_column flex item-pdd">
                <div class="flex-y-center">
                  <a-tooltip popper-class="toolt" placement="bottomLeft">
                    <span class="text_overflow">{{ scope.record.remark || '--' }}</span>
                    <template #title>
                      {{ scope.record.remark }}
                    </template>
                  </a-tooltip>
                </div>
              </div>
            </template>
            <template v-if="scope.column.key === 'page_url'">
              <div class="flex_column flex item-pdd">
                <div class="flex-y-center">
                  <a-tooltip popper-class="toolt" placement="bottomLeft">
                    <span class="text_overflow">{{ scope.record.page_url || '--' }}</span>
                    <template #title>
                      {{ scope.record.page_url }}
                    </template>
                  </a-tooltip>
                  <CopyOutlined v-if="scope.record.page_url" @click="copy(scope.record.page_url)" />
                </div>
              </div>
            </template>
            <template v-if="scope.column.key === 'page_type'">
              <div class="flex_column flex">主页面</div>
            </template>
            <template v-if="scope.column.key === 'page_status'">
              <div class="flex_column flex">
                <span class="item-light">{{ getPageStatus(scope.record.page_status) }}</span>
              </div>
            </template>
            <template v-if="scope.column.key === 'new_status'">
              <div class="flex_column flex">
                <span class="item-light">{{ getPageStatus(scope.record.new_status) }}</span>
              </div>
            </template>

            <template v-if="scope.column.key === 'updated_at'">
              <div class="flex_column flex">
                <span class="item-light">{{
                  scope.record.created_at ? formatDate(scope.record.created_at * 1000) : '--'
                }}</span>
              </div>
            </template>
            <template v-if="scope.column.key === 'handle'">
              <div class="handle_btns">
                <div class="flex flex-items-start flex_column">
                  <!-- v-auth:GoodsList="['adEdit']" -->
                  <a-button type="link" class="p-0" @click="onShowDialog('edit', scope.record)">编辑</a-button>
                  <div v-click-outside="() => (scope.record.popViseble = false)">
                    <a-popover
                      v-model:open="scope.record.popViseble"
                      trigger="click"
                      :overlayInnerStyle="{ width: '200px', padding: '15px', fontSize: '16px' }"
                    >
                      <template #title>
                        <div @click.stop="scope.record.popViseble = true">
                          <div class="flex_ju_sp pop_title">
                            <span>落地页预览</span>
                            <img
                              class="close"
                              src="@/assets/icon/close.png"
                              alt=""
                              @click.stop="scope.record.popViseble = false"
                            />
                          </div>
                          <div class="flex-y-center flex-col">
                            <a-qrcode :value="scope.record.page_url" v-if="scope.record.page_url" />
                            <!-- <img
                              :src="data.preview_url"
                              style="min-width: 150px; min-height: 150px"
                              alt=""
                              v-if="data.preview_url"
                            /> -->
                            <img
                              src="@/assets/images/empty/no_content.png"
                              style="width: 150px; height: 150px"
                              alt=""
                              v-else
                            />
                            <p class="text-center">{{ scope.record.page_url ? '手机淘宝扫码预览' : '二维码加载中' }}</p>
                          </div>
                        </div>
                      </template>
                      <a-button
                        type="link"
                        class="p-0"
                        v-if="scope.record.page_status == 4"
                        @click="onPreview(scope.record)"
                        >预览</a-button
                      >
                    </a-popover>
                    <div>
                      <a-button type="link" class="p-0" @click="onDel(scope.record)">删除</a-button>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </template>
        </TableZebraCrossing>
        <div class="text-right">
          <a-button class="" @click="emit('event', { cmd: 'close' })">取消</a-button>
          <a-button
            type="primary"
            :disabled="!data.selectedRowKeys.length"
            @click="submit()"
            :loading="data.submitLoading"
            >确认</a-button
          >
        </div>
      </div>
    </div>
    <a-modal
      :title="data.dialog.title"
      v-model:open="data.dialog.visible"
      :width="data.dialog.width"
      centered
      :footer="null"
      destroyOnClose
    >
      <TbLandingEdit
        v-if="['add', 'edit'].includes(data.dialog.type)"
        :goods-detail="item"
        :item="data.item"
        @event="onEvent"
      />
    </a-modal>
  </div>
</template>
<script setup>
  import { reactive, createVNode, ref } from 'vue'
  import { requireImg, formatDate, copy } from '@/utils'
  import { CopyOutlined, QuestionCircleFilled, FormOutlined } from '@ant-design/icons-vue'
  import TbLandingEdit from './TbLandingEdit.vue'
  import { tbPageList, tbPageDel } from '../index.api'
  import { message, Modal } from 'ant-design-vue'

  const props = defineProps(['item', 'type', 'use'])
  const emit = defineEmits(['event'])

  const tableData = reactive({
    list: [],
    // columns,
    pageNo: 1,
    pageSize: 10,
    total: 1,
    loading: false
  })
  const columns = [
    {
      title: '页面名称',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: 120
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 120
    },
    {
      title: '页面地址',
      dataIndex: 'page_url',
      key: 'page_url',
      width: 260
    },
    {
      title: '页面类型',
      dataIndex: 'page_type',
      key: 'page_type',
      width: 120
    },

    {
      title: '页面当前状态',
      dataIndex: 'page_status',
      key: 'page_status',
      width: 120
    },
    {
      title: '页面最新修改状态',
      dataIndex: 'new_status',
      key: 'new_status',
      width: 160
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    },
    {
      title: '操作',
      dataIndex: 'handle',
      key: 'handle',
      width: 70
    }
  ]
  // 如果 use == 'ad' 去掉操作
  if (props.use == 'ad') {
    columns.pop()
  }
  const data = reactive({
    adAppIdList: [], //小程序原始ID
    popViseble: false,
    submitLoading: false,
    dialog: {
      id: '',
      type: '',
      width: '',
      visible: false,
      titie: ''
    },
    selectedRowKeys: [],
    form: {
      land_type: 1,
      ad_id: 0,
      product_id: undefined
    },
    goodsList: [],
    open_video: 1,
    visible: false,
    item: {},
    goods_details: {},
    preview_url: null,
    tableConfigOptions: {
      bordered: false,
      loading: false,
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 720,
        y: 370
      },
      dataSource: [],
      columns: columns,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        page: 1,
        pageSize: 10,
        size: 'small',
        showTotal: (total) => `共${total}条数据`
      }
    },
    query: {
      page_status: props.use ? 4 : undefined,
      page: 1,
      page_size: 10
    }
  })
  const getPageStatus = (val) => {
    const status = {
      1: '编辑中',
      2: '审核中',
      3: '被驳回',
      4: '已发布',
      5: '已下线',
      6: '已过期',
      0: '--'
    }
    return status[val]
  }
  // 列表
  const getList = async () => {
    try {
      tableData.loading = true
      data.goodsDetail = { id: data.form.product_id }
      const resp = await tbPageList({ ...data.query, product_id: props.item.item_id })
      data.tableConfigOptions.dataSource = resp.data?.list || []
      data.tableConfigOptions.pagination.page = resp.data?.page || 1
      data.tableConfigOptions.pagination.total = resp.data?.total || 0
      if (data.tableConfigOptions.dataSource.length >= 4) {
        data.tableConfigOptions.scroll.y = 370
      } else {
        delete data.tableConfigOptions.scroll.y
      }
    } catch (error) {
      console.error(error)
      tableData.loading = false
    }
  }
  getList()
  const selectionEvent = (selectedRowKeys, selectedRows) => {
    data.selectedRowKeys = selectedRowKeys
    data.selectedRows = selectedRows
    data.form.ad_id = data.selectedRowKeys[0]
    data.form.ad_url = selectedRows[0].ad_url
  }

  const onShowDialog = (type, item) => {
    data.item = { ...item }
    switch (type) {
      case 'add':
        data.dialog = { title: '添加云台落地页', width: 600, visible: true, type: type }
        break
      case 'edit':
        data.dialog = { title: '编辑云台落地页', width: 600, visible: true, type: type }
        break
    }
  }

  const pageChange = (pagination) => {
    console.log(pagination)
    data.query.page = pagination.current
    data.query.page_size = pagination.pageSize
    data.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }
  const onEvent = (values) => {
    switch (values.cmd) {
      case 'close':
        data.dialog.visible = false
        break
      case 'edit':
        data.dialog.visible = false
        getList()
        break
      case 'saveLibrary':
        data.dialog.visible = false
        data.hasLibrary = true
        break
    }
    data.dialog.type = ''
  }
  // 预览
  const onPreview = async (item) => {
    ;(data.tableConfigOptions.dataSource || []).forEach((v) => {
      if (v.id == item.id) {
        v.popViseble = item.popViseble == true ? false : true
      } else {
        v.popViseble = false
      }
    })
  }
  // 删除
  const onDel = (item) => {
    try {
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '请确认是否删除当前落地页？'),
        async onOk() {
          await tbPageDel({ id: item.id })
          getList()
          message.success('删除成功')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  const submit = (formEl) => {
    if (!data.selectedRowKeys.length) {
      return message.warning('请选择云台落地页')
    }
    emit('event', { cmd: 'add', data: data.selectedRows[0] })
    // change_link({
    //   ...data.form,
    //   id: props.item.id,
    //   ad_url: data.form.ad_url,
    //   order_count: data.form.order_count || undefined,
    //   type: props.type == 'number' ? 11 : 4,
    //   ad_id: data.selectedRowKeys[0]
    // })
    //   .then((res) => {
    //     // message.success('操作成功')
    //     // data.submitLoading = false
    //     emit('event', { cmd: 'add' })
    //   })
    //   .catch((err) => {
    //     console.error(err)
    //     data.submitLoading = false
    //   })
  }
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';
  :deep(.el-divider--horizontal) {
    margin: 10px 0;
  }
  .text_overflow {
    @include text_overflow(2);
    white-space: wrap;
  }

  .icon {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-right: 10px;
  }

  .pop_content {
    .tips {
      color: #999;
      margin: 5px 0;
    }

    .mar {
      margin: 5px 0;
    }
  }

  .ad_name {
    .name {
      margin-right: 30px;
    }
  }

  .copy_icon {
    margin-left: 10px;
    cursor: pointer;
    color: var(--el-color-primary);
  }

  :deep(.el-table) {
    border-radius: 10px;
    margin-top: 20px;
  }

  :deep(.el-dialog__body) {
    padding: 0 30px 30px 30px !important;
  }

  :deep(.el-table .cell) {
    padding: 0 12px;
    color: #080f1e;
  }

  :deep(.el-popper.is-dark) {
    max-width: 450px;
    line-height: 1.4;
    color: #fff;
    font-weight: 400;
    word-break: break-all;
  }
  // :deep(.custom-searchForm) {
  //   margin-top: 20px;
  //   margin-bottom: 20px;
  //   .comp_searchForm {
  //     .form_item {
  //       width: 180px;
  //     }
  //     .btn_group {
  //       display: none;
  //     }
  //   }
  // }

  .pop_title {
    margin-bottom: 10px;

    .close {
      cursor: pointer;
    }
  }
  .item-blue {
    border: 1px solid #3f9fff;
    color: #3f9fff;
  }
  .item-green {
    border: 1px solid #70b606;
    color: #70b606;
  }
  .item-purple {
    border: 1px solid purple;
    color: purple;
  }
  .item-cili {
    border: 1px solid #fe4a08;
    color: #fe4a08;
  }
  .item-tag {
    border-radius: 5px;
    font-size: 12px;
    padding: 0 6px;
    margin-right: 16px;
  }
  .space_between {
    justify-content: space-between;
  }

  .text_align_center {
    text-align: center;
  }

  .handle_btns {
    user-select: none;

    span {
      color: var(--el-color-primary);
      cursor: pointer;
      margin-right: 10px;
    }
  }

  .copy_icon {
    margin-left: 10px;
    cursor: pointer;
    color: var(--el-color-primary);
  }

  .divider {
    margin-top: 5px;
    padding-bottom: 5px;
    border-bottom: var(--el-border);

    &:nth-last-of-type(1) {
      border-bottom: none;
    }
  }

  .msg-item {
    background: #fef5e8;
    border-radius: 5px;
    border: 1px solid #ffe7c8;
    padding: 5px 0 5px 8px;
    line-height: 20px;
    height: 30px;
  }
  .item-status {
    padding: 6px;
    position: absolute;
    right: 0;
    top: 0;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  :deep(.ant-select.ant-select-in-form-item) {
    width: auto;
  }
</style>
