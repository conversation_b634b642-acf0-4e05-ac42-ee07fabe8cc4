<template>
  <div class="ad_link">
    <a-form
      :model="data.form"
      ref="ruleForm"
      :rules="adRules"
      :labelCol="{ style: 'width: 120px' }"
      class="ad_form mt-24px"
      autocomplete="off"
    >
      <a-form-item label="优化目标：" name="callback_status">
        <a-select
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          v-model:value="data.form.callback_status"
        >
          <template v-if="data.form.platform_id == 1">
            <a-select-option value="COMPLETE_ORDER">下单</a-select-option>
          </template>
          <template v-if="data.form.platform_id == 4">
            <a-select-option value="14">订单提交</a-select-option>
          </template>
          <template v-if="data.form.platform_id == 6">
            <a-select-option value="in_app_order">下单</a-select-option>
            <a-select-option value="active_pay">购买</a-select-option>
          </template>
          <template v-if="props.goods == 'tb'">
            <a-select-option v-if="data.form.media_type != 4" value="12">下单</a-select-option>
            <a-select-option v-else value="14">下单</a-select-option>
          </template>
        </a-select>
      </a-form-item>
      <template v-if="!shopInfo?.limit_callback && (data.form.platform_id === 1 || data.form.media_type == 1)">
        <a-form-item label="回传比例：" name="callback_ratio" required>
          <div class="coupon">
            <a-input-number
              class="w100%"
              v-model:value="data.form.callback_ratio"
              disabled
              :precision="0"
              placeholder="100"
              addonAfter="%"
            >
            </a-input-number>
            <span class="link_btn"> 广告订单将回传至广告侧，帮助广告侧优化广告投放群体 </span>
          </div>
        </a-form-item>
      </template>
      <template v-else>
        <a-form-item id="callback_type" label="回传单量类型" name="callback_type" required>
          <div class="flex">
            <div class="mr-4px mt-6px">按</div>
            <a-form-item>
              <a-select
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                v-model:value="data.form.callback_type"
                :style="{ width: '160px' }"
                :options="[
                  {
                    label: '固定比例',
                    value: 1
                  },
                  {
                    label: '金额区间',
                    value: 2
                  }
                ]"
                placeholder="请选择回传单量类型"
                @change="(val: number) => callback_handler(val, 'type')"
              ></a-select>
            </a-form-item>
            <a-form-item
              class="ml-4px"
              v-if="data.form.callback_type === 1"
              name="callback_role"
              :rules="{
                required: true,
                validator: (_: any, __: string, callback: Function) => {
                  if (!data.form.callback_role && data.form.callback_role !== 0) {
                    callback('请输入固定比例')
                  }
                  callback()
                },
                trigger: ['change', 'blur']
              }"
            >
              <a-input-number
                v-if="data.form.media_type != 4"
                class="w-120px"
                v-model:value="data.form.callback_role"
                :precision="0"
                :min="0"
                :max="100"
                addonAfter="%"
              >
              </a-input-number>
              <a-select
                v-else
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                v-model:value="data.form.callback_role"
                class="w-140px"
                :options="[
                  {
                    label: 10,
                    value: 10
                  },
                  {
                    label: 20,
                    value: 20
                  },
                  {
                    label: 30,
                    value: 30
                  },
                  {
                    label: 40,
                    value: 40
                  },
                  {
                    label: 50,
                    value: 50
                  },
                  {
                    label: 60,
                    value: 60
                  },
                  {
                    label: 70,
                    value: 70
                  },
                  {
                    label: 80,
                    value: 80
                  },
                  {
                    label: 90,
                    value: 90
                  },
                  {
                    label: 100,
                    value: 100
                  }
                ]"
                placeholder="请选择回传比例"
                :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
              ></a-select>
            </a-form-item>
            <div class="ml-4px mt-6px">
              回传广告单量
              <a-tooltip>
                <template #title>
                  支持按比例设置广告订单的回传数量
                  {{ data.form.media_type == 4 ? '' : '，填0%即不回传，填100%即全量回传' }}
                </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-tooltip>
            </div>
          </div>
          <div class="solid-wrapper" v-if="data.form.callback_type === 2">
            <div class="c-#85878a flex-y-center">
              <span class="c-#ff4d4f">*</span>
              区间中的广告流量订单按比例回传，不在区间中的广告流量订单不回传
            </div>
            <div class="mt-12px mb-12px c-#85878a">
              <span>金额区间</span>
              <span class="ml-234px">广告流量订单回传比例</span>
            </div>
            <div class="flex-y-center item" v-for="(item, index) in data.form.callback_role_list" :key="index">
              <a-form-item
                class="w-120px flex-none"
                :name="'callback_type' + index + '_min_price'"
                :rules="{
                  validator: (_: any, __: string, callback: Function) => {
                    if (!item.min_price) {
                      callback('请输入最小金额')
                    }
                    if (item.max_price && item.min_price && item.min_price > item.max_price) {
                      callback('金额不能交叉')
                    }
                    if (
                      item.min_price &&
                      index > 0 &&
                      item.min_price < Number(data.form?.callback_role_list?.[index - 1]?.max_price)
                    ) {
                      callback('金额不能交叉')
                    }
                    callback()
                  },
                  trigger: ['change', 'blur']
                }"
              >
                <a-input-number
                  v-model:value="item.min_price"
                  :min="0.01"
                  placeholder="最小金额"
                  :precision="2"
                  :max="99999999"
                  addonAfter="元"
                  @blur="resetFieldCallbackList(data.form.callback_role_list.length, 'callback_type')"
                >
                </a-input-number>
              </a-form-item>
              <div class="flex-none ml-4px mr-4px mt--16px c-#85878a">-</div>
              <a-form-item
                class="w-120px flex-none"
                :name="'callback_type' + index + '_max_price'"
                :rules="{
                  validator: (_: any, __: string, callback: Function) => {
                    if (!item.max_price) {
                      callback('请输入最大金额')
                    }
                    if (item.max_price && item.min_price && item.max_price <= item.min_price) {
                      callback('金额不能重复或交叉2')
                    }
                    let nextMin =
                      index + 1 <= (data.form.callback_role_list || [])?.length
                        ? data.form.callback_role_list?.[index + 1]?.min_price
                        : undefined
                    if (nextMin && item.max_price && nextMin < Number(item.max_price)) {
                      callback('金额不能重复或交叉')
                    }
                    callback()
                  },
                  trigger: ['blur', 'change']
                }"
              >
                <a-input-number
                  :min="0.01"
                  v-model:value="item.max_price"
                  placeholder="最大金额"
                  :precision="2"
                  :max="99999999"
                  @blur="resetFieldCallbackList(data.form.callback_role_list.length, 'callback_type')"
                  addonAfter="元"
                >
                </a-input-number>
              </a-form-item>
              <div class="flex-none ml-4px mr-4px mt--16px c-#85878a">回传</div>
              <a-form-item
                class="w-160px flex-none"
                :name="'callback_type' + index + '_ration'"
                :rules="{
                  validator: (_: any, __: string, callback: Function) => {
                    if (!item.ratio && item.ratio !== 0) {
                      callback('请输入订单比例')
                    }
                    callback()
                  },
                  trigger: ['change', 'blur']
                }"
              >
                <a-input-number
                  v-model:value="item.ratio"
                  placeholder="填写订单比例"
                  :precision="0"
                  :min="0"
                  :max="100"
                  addonAfter="%"
                >
                </a-input-number>
              </a-form-item>
              <MinusCircleOutlined
                class="font-size-16px c-#85878a cursor-pointer ml-4px mt--16px"
                @click="deleteItem('type', index)"
              />
            </div>
            <a-button
              type="link"
              class="p-0! h-auto!"
              :disabled="data.form.callback_role_list && data.form.callback_role_list.length >= 10"
              @click="addItem('type')"
              >添加阶梯</a-button
            >
          </div>
        </a-form-item>
        <a-form-item id="callback_amount">
          <template #label>
            <div class="flex-align">
              <span>回传金额</span>
              <a-tooltip>
                <template #title>
                  整单金额，即按用户的实付金额回传； <br />
                  单件金额，即按实付金额/件数的金额回传；<br />
                  按固定金额，即回传金额为指定金额； <br />
                  按固定比例，即回传金额为{{ data.form.platform_id === 4 ? '原价' : '实付金额' }}*比例；<br />
                  按金额区间，即实付金额落在金额区间中的订单，按所设比例回传；<br />
                  如50-100元回传100%，101-150元回传50%；
                </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-tooltip>
            </div>
          </template>
          <div class="flex">
            <span class="mr-4px mt-6px">按</span>
            <a-form-item>
              <a-select
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                v-model:value="data.form.callback_amount_type"
                :style="{ width: '160px' }"
                @change="(val: number) => callback_handler(val, 'amount')"
                :options="[
                  {
                    label: '整单金额',
                    value: 1
                  },
                  {
                    label: '单件金额',
                    value: 2
                  },
                  {
                    label: '固定金额',
                    value: 3
                  },
                  {
                    label: '固定比例',
                    value: 4
                  },
                  {
                    label: '金额区间',
                    value: 5
                  }
                ]"
                placeholder="请选择回传金额类型"
              ></a-select>
            </a-form-item>
            <a-form-item
              class="ml-4px"
              v-if="data.form.callback_amount_type === 3"
              name="callback_amount_role"
              :rules="{
                validator: (_: any, __: string, callback: Function) => {
                  if (!data.form.callback_amount_role) {
                    callback('请输入固定金额')
                  }
                  callback()
                },
                trigger: ['change', 'blur']
              }"
            >
              <a-input-number
                class="w-120px"
                v-model:value="data.form.callback_amount_role"
                :precision="2"
                :min="0.01"
                :max="99999999"
                addonAfter="元"
              >
              </a-input-number>
            </a-form-item>
            <a-form-item
              class="ml-4px"
              v-if="data.form.callback_amount_type === 4"
              name="callback_amount_role"
              :rules="{
                validator: (_: any, __: string, callback: Function) => {
                  if (!data.form.callback_amount_role && data.form.callback_amount_role !== 0) {
                    callback('请输入固定比例')
                  }
                  callback()
                },
                trigger: ['change', 'blur']
              }"
            >
              <a-input-number
                class="w-120px"
                v-model:value="data.form.callback_amount_role"
                :precision="0"
                :min="0"
                :max="100"
                addonAfter="%"
              >
              </a-input-number>
            </a-form-item>
            <span class="ml-4px mt-6px">回传</span>
          </div>
          <div class="solid-wrapper" v-if="data.form.callback_amount_type === 5">
            <div class="mb-12px c-#85878a">
              <span>金额区间</span>
              <!-- <span class="ml-234px">回流订单回传比例</span> -->
            </div>
            <div class="flex-y-center item" v-for="(item, index) in data.form.callback_amount_role_list" :key="index">
              <a-form-item
                class="w-120px flex-none"
                :name="'callback_amount' + index + '_min_price'"
                :rules="{
                  validator: (_: any, __: string, callback: Function) => {
                    if (!item.min_price) {
                      callback('请输入最小金额')
                    }
                    if (item.max_price && item.min_price && item.min_price > item.max_price) {
                      callback('金额不能交叉')
                    }
                    if (
                      item.min_price &&
                      index > 0 &&
                      item.min_price < Number(data.form?.callback_amount_role_list?.[index - 1]?.max_price)
                    ) {
                      callback('金额不能交叉')
                    }
                    callback()
                  },
                  trigger: ['blur', 'change']
                }"
              >
                <a-input-number
                  v-model:value="item.min_price"
                  :min="0.01"
                  placeholder="最小金额"
                  :precision="2"
                  :max="99999999"
                  addonAfter="元"
                  @blur="resetFieldCallbackList(data.form.callback_amount_role_list.length, 'callback_amount')"
                >
                </a-input-number>
              </a-form-item>
              <span class="ml-4px mr-4px mt--16px c-#85878a">-</span>
              <a-form-item
                class="w-120px flex-none"
                :name="'callback_amount' + index + '_max_price'"
                :rules="{
                  validator: (_: any, __: string, callback: Function) => {
                    if (!item.max_price) {
                      callback('请输入最大金额')
                    }
                    if (item.max_price && item.min_price && item.max_price <= item.min_price) {
                      callback('金额不能交叉')
                    }
                    let nextMin =
                      index + 1 <= (data.form.callback_amount_role_list || [])?.length
                        ? data.form.callback_amount_role_list?.[index + 1]?.min_price
                        : undefined
                    if (nextMin && item.max_price && nextMin < Number(item.max_price)) {
                      callback('金额不能交叉')
                    }
                    callback()
                  },
                  trigger: ['blur', 'change']
                }"
              >
                <a-input-number
                  :min="0.01"
                  v-model:value="item.max_price"
                  placeholder="最大金额"
                  :precision="2"
                  :max="99999999"
                  addonAfter="元"
                  @blur="resetFieldCallbackList(data.form.callback_amount_role_list.length, 'callback_amount')"
                >
                </a-input-number>
              </a-form-item>
              <span class="ml-4px mr-4px mt--16px c-#85878a">回传</span>
              <a-form-item
                class="w-160px flex-none"
                :name="'callback_amount' + index + '_ration'"
                :rules="{
                  validator: (_: any, __: string, callback: Function) => {
                    if (!item.ratio && item.ratio !== 0) {
                      callback('请输入订单比例')
                    }
                    callback()
                  },
                  trigger: ['blur', 'change']
                }"
              >
                <a-input-number
                  v-model:value="item.ratio"
                  placeholder="填写订单比例"
                  :precision="0"
                  :min="0"
                  :max="100"
                  addonAfter="%"
                >
                </a-input-number>
              </a-form-item>
              <MinusCircleOutlined
                class="font-size-16px c-#85878a cursor-pointer ml-4px mt--16px"
                @click="deleteItem('amount', index)"
              />
            </div>
            <a-button
              type="link"
              class="p-0! h-auto!"
              :disabled="data.form.callback_amount_role_list.length >= 10"
              @click="addItem('amount')"
              >添加阶梯</a-button
            >
          </div>
        </a-form-item>
      </template>
    </a-form>

    <div class="fooler_btn" v-if="props.type != 'detail'">
      <AButton :mr="30" @click="close">取消</AButton>
      <AButton type="primary" :loading="data.loading" @click="submitForm(ruleForm)">确定</AButton>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { updateAdurl, updateTbAd } from '../index.api'
  import { watch, reactive, ref, nextTick } from 'vue'
  import { message } from 'ant-design-vue'
  import { useApp } from '@/hooks'
  import { QuestionCircleFilled, MinusCircleOutlined } from '@ant-design/icons-vue'
  const ruleForm = ref<HTMLFormElement | null>(null)
  // 导入form验证规则
  const adRules = reactive({
    callback_status: [{ required: true, message: '请选择回传行为', trigger: ['change', 'blur'] }],
    callback_time: [{ required: true, message: '请选择回传节点', trigger: ['change', 'blur'] }]
  })
  const { shopInfo } = useApp()
  const props = defineProps({
    goodsDetail: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    },
    goods: {
      type: String,
      default: ''
    },
    item: {
      type: Object,
      default: () => {}
    }
  })
  interface CallbackListItem {
    max_price: number | undefined
    min_price: number | undefined
    ratio: number | undefined
  }

  const emit = defineEmits(['event'])

  const data = reactive({
    loading: false,
    form: {
      ad_account_id: null
    } as any
  })

  const close = () => {
    emit('event', { cmd: 'close' })
  }

  // 提交
  const submitForm = (formName: any) => {
    formName
      .validate()
      .then(async () => {
        await update()
      })
      .catch((err: any) => {
        console.log('error', err)
      })
  }
  const paramsFunc = () => {
    let params =
      props.goods == 'tb'
        ? {
            ad_title: data.form.ad_title || '',
            product_id: props.goodsDetail.item_id,
            media_type: Number(data.form.media_type),
            link_type: Number(data.form.link_type),
            ud_page: data.form.media_type == 4 ? undefined : Number(data.form.ud_page),
            convert_target: data.form.convert_target || '',
            callback_ratio: data.form.callback_ratio ? Number(data.form.callback_ratio) : 0
          }
        : {
            name: data.form.name || '',
            product_id: Number(data.form.product_id) || 0,
            platform_id: Number(data.form.platform_id),
            ad_account_id: data.form.ad_account_id ? +data.form.ad_account_id : 0,
            callback_status: data.form.callback_status || '',
            callback_ratio: data.form.callback_ratio ? Number(data.form.callback_ratio) : 0
          }
    if ([1, 4, 6].includes(data.form.platform_id)) {
      params.web_center = {
        ...data.form.web_center
      }
      params.shop_entity_id = data.form.shop_entity_id
      params.domain = data.form.domain
      params.web_center.content = data.form.web_center?.image?.[0]?.url
      delete params.web_center.image
      if (data.form.web_center.button_style === 4) {
        params.web_center.button = undefined
      }
    }
    let newParams = {}
    if (true) {
      newParams = {
        callback_amount_type: data.form.callback_amount_type,
        callback_type: data.form.callback_type
      } as any
      if (data.form.callback_amount_type === 1 || data.form.callback_amount_type === 2) {
        newParams.callback_amount_role = undefined
      } else if (data.form.callback_amount_type === 3) {
        newParams.callback_amount_role = data.form?.callback_amount_role
          ? data.form?.callback_amount_role + ''
          : undefined
      } else if (data.form.callback_amount_type === 4) {
        newParams.callback_amount_role = data.form.callback_amount_role + ''
      } else {
        newParams.callback_amount_role = JSON.stringify(data.form.callback_amount_role_list)
      }

      if (data.form.callback_type === 1) {
        newParams.callback_role = undefined
        newParams.callback_ratio =
          (data.form.platform_id === 1 || data.form.media_type === 1) && !shopInfo.value?.limit_callback
            ? 100
            : data.form.callback_role
      } else {
        newParams.callback_role = data.form.callback_role_list
      }
    }

    return { ...params, ...newParams }
  }

  // 更新
  const update = async () => {
    try {
      data.loading = true
      let params = paramsFunc()
      props.goods == 'tb'
        ? await updateTbAd({ ...params, convert_target: params.callback_status, id: data.form.id })
        : await updateAdurl({ ...params, id: data.form.id })
      ruleForm.value?.resetFields()
      emit('event', { cmd: 'edit' })
      message.success('操作成功')
      data.loading = false
    } catch (error) {
      console.error(error)
      data.loading = false
    }
  }
  // 金额区间change
  const callback_handler = (val: number, type: string) => {
    if (type === 'type') {
      if (val === 1) {
        data.form.callback_role = 100
        data.form.callback_role_list = []
      } else {
        data.form.callback_role = undefined
        if (!data.form.callback_role_list?.length) {
          let item: CallbackListItem = { min_price: undefined, max_price: undefined, ratio: undefined }
          data.form.callback_role_list?.push(item)
        }
      }
      return
    }
    if (type === 'amount') {
      if (val === 5) {
        data.form.callback_amount_role = undefined
        if (!data.form.callback_amount_role_list?.length) {
          let item: CallbackListItem = { min_price: undefined, max_price: undefined, ratio: undefined }
          data.form.callback_amount_role_list?.push(item)
        }
      } else {
        data.form.callback_amount_role = undefined
        data.form.callback_amount_role_list = []
      }

      return
    }
  }
  // 价格区间校验
  const resetFieldCallbackList = (len: number, type: string) => {
    let callback_type_arr = []
    let callback_amount_arr = []
    if (type === 'callback_type') {
      for (let i = 0; i < len; i++) {
        callback_type_arr.push('callback_type' + i + '_min_price')
        callback_type_arr.push('callback_type' + i + '_max_price')
      }
      ruleForm?.value?.validateFields(callback_type_arr)
    } else {
      for (let i = 0; i < len; i++) {
        callback_amount_arr.push('callback_amount' + i + '_min_price')
        callback_amount_arr.push('callback_amount' + i + '_max_price')
      }
      ruleForm.value?.validateFields(callback_amount_arr)
    }
  }
  // 添加
  const addItem = (type: string) => {
    let list = type === 'type' ? data.form.callback_role_list : data.form.callback_amount_role_list
    if (list?.some((it) => !it.min_price || !it.max_price || (!it.ratio && it.ratio !== 0)))
      return message.warning('请先填写完金额区间及回传比例')
    let item: CallbackListItem = { min_price: undefined, max_price: undefined, ratio: undefined }
    list?.push(item)
  }
  // 删除
  const deleteItem = (type: string, index: number) => {
    let list = type === 'type' ? data.form.callback_role_list : data.form.callback_amount_role_list
    if ((list || [])?.length <= 1) return message.warning('至少有一条信息')
    list?.splice(index, 1)
  }

  const initData = async () => {
    if (props.item?.id) {
      data.form =
        props.goods == 'tb'
          ? {
              id: props.item.id || '',
              ad_title: props.item.ad_title || '',
              ud_page: props.item.ud_page || 2,
              media_type: Number(props.item.media_type) || '',
              link_type: Number(props.item.link_type) || '',
              callback_status: props.item.convert_target || '',
              callback_ratio: props.item.callback_ratio || 0
            }
          : {
              id: props.item.id || '',
              name: props.item.name || '',
              product_id: props.goodsDetail.id,
              platform_id: Number(props.item.platform_id) || '',
              ad_account_id: props.item.ad_account_id ? props.item.ad_account_id + '' : null,
              callback_status: props.item.callback_status || '',
              // callback_ratio: props.item.callback_ratio || '',
              callback_ratio: props.item.callback_ratio || 0,
              product_type: props.item.product_type || 1,
              domain: props.item.domain || ''
            }
      if ([1, 4, 6].includes(data.form.platform_id)) {
        data.form.web_center = {
          ...props.item.web_center
        }
        data.form.shop_entity_id = props.item.shop_entity_id || undefined
        data.form.web_center.image = props.item.web_center.content ? [{ url: props.item.web_center.content }] : []
      }

      // 处理底部的回传
      let res = props.item
      data.form.callback_type = res.callback_type
      if (res.callback_type === 1) {
        data.form.callback_role = res.callback_ratio
        data.form.callback_role_list = []
      } else {
        data.form.callback_role = undefined
        data.form.callback_role_list = res.callback_role || []
      }
      data.form.callback_amount_type = res.callback_amount_type
      if (res.callback_amount_type === 3) {
        data.form.callback_amount_role = +res.callback_amount_role
        data.form.callback_amount_role_list = []
      } else if (res.callback_amount_type === 4) {
        data.form.callback_amount_role = +res.callback_amount_role
        data.form.callback_amount_role_list = []
      } else if ([1, 2].includes(res.callback_amount_type)) {
        data.form.callback_amount_role = undefined
        data.form.callback_amount_role_list = []
      } else {
        data.form.callback_amount_role = undefined
        data.form.callback_amount_role_list = res.callback_amount_role
          ? JSON.parse(res.callback_amount_role)
          : [{ min_price: undefined, max_price: undefined, ratio: undefined }]
      }
      const isGdt = data.form.platform_id === 1 || data.form.media_type === 1
      if (isGdt && !shopInfo.value?.limit_callback) {
        data.form.callback_ratio = 100
        data.form.callback_amount_type = 1
        data.form.callback_amount_role = undefined
      }
      if (isGdt && shopInfo.value?.limit_callback && !props.item.callback_type) {
        data.form.callback_type = 1
        data.form.callback_role = 100
        data.form.callback_amount_type = 1
      }
    }
    nextTick(() => {
      ruleForm.value?.resetFields()
    })
  }
  watch(() => props.item, initData, { immediate: true })
</script>
<style lang="scss" scoped>
  .ad_form::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    /**/
  }

  .ad_form::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
  }

  .ad_form::-webkit-scrollbar-thumb {
    background: #c5c5c5;
    border-radius: 10px;
    display: block;
    padding-left: 30px;
  }

  .ad_link {
    margin-right: -15px;

    .ad_form {
      max-height: 600px;
      overflow: auto;
      padding-right: 18px;
    }
    .flex-align {
      display: flex;
      align-items: center;
    }
    .fooler_btn {
      text-align: end;
      margin-right: 20px;
    }
  }
</style>
