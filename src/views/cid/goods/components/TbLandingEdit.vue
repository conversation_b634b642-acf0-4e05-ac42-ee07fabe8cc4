<template>
  <div class="ad_link">
    <a-form
      :model="data.form"
      ref="ruleForm"
      :rules="adRules"
      :labelCol="{ style: 'width: 80px' }"
      class="ad_form mt-24px"
      autocomplete="off"
    >
      <a-form-item label="页面类型：" name="page_type">
        <a-radio-group v-model:value="data.form.page_type">
          <a-radio :value="1">主页面</a-radio>
          <a-tooltip>
            <template #title>用于直达链接跳转后展示的页面内容</template>
            <QuestionCircleFilled class="ml--10px font-size-12px" />
          </a-tooltip>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="页面标题" name="name">
        <a-input v-model:value="data.form.name" placeholder="请输入页面标题" :maxlength="40" showCount />
      </a-form-item>
      <a-form-item label="商品ID" name="product_id">
        <a-input v-model:value="data.form.product_id" placeholder="" :maxlength="40" disabled />
      </a-form-item>
      <a-form-item label="商品名称" name="product_name">
        <a-input v-model:value="data.form.product_name" placeholder="" disabled />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value.trim="data.form.remark"
          placeholder="请输入备注"
          :auto-size="{ minRows: 3, maxRows: 4 }"
          show-count
          :maxlength="50"
        />
      </a-form-item>
    </a-form>

    <div class="fooler_btn">
      <AButton :mr="30" @click="close">取消</AButton>
      <AButton type="primary" :loading="data.loading" @click="submitForm(ruleForm)">确定</AButton>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { tbPageCreate, tbPageEdit } from '../index.api'
  import { reactive, ref } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { message, Modal } from 'ant-design-vue'

  import { QuestionCircleFilled } from '@ant-design/icons-vue'

  const router = useRouter()
  const route = useRoute()
  const ruleForm = ref(null)
  // 导入form验证规则
  const adRules = reactive({
    name: [{ required: true, message: '请输入页面标题', trigger: ['change', 'blur'] }]
  })

  const props = defineProps({
    goodsDetail: {
      type: Object,
      default: () => {}
    },
    item: {
      type: Object,
      default: () => {}
    }
  })

  const emit = defineEmits(['event'])

  const data = reactive({
    loading: false,
    form: {
      page_type: 1,
      id: props.item.id || 0,
      name: props.item.name || '',
      product_id: props.goodsDetail.item_id,
      product_name: props.goodsDetail.name,
      remark: props.item.remark || ''
    },

    fetching: false,
    dialog: {
      id: '',
      title: '',
      visible: false,
      width: '',
      type: ''
    }
  })

  const close = () => {
    emit('event', { cmd: 'close' })
  }

  // 提交
  const submitForm = (formName) => {
    formName
      .validate()
      .then(async () => {
        await save()
      })
      .catch((err) => {
        console.log('error', err)
      })
  }

  // 添加
  const save = async () => {
    try {
      data.loading = true
      const resp = await tbPageCreate({ ...data.form })
      const respData = await tbPageEdit({ ...data.form, id: resp.data.id || undefined })
      emit('event', { cmd: 'edit' })
      message.success('操作成功')
      window.open(respData.data, '_blank')

      data.loading = false
    } catch (error) {
      console.error(error)
      data.loading = false
    }
  }
</script>
<style lang="scss" scoped>
  .ad_link {
    .flex-align {
      display: flex;
      align-items: center;
    }
    .fooler_btn {
      text-align: end;
    }
  }
</style>
