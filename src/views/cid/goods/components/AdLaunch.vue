<template>
  <div>
    <div>
      <div class="flex_ju_sp mb-12px">
        <a-button type="primary" @click="onShowDialog('add')">创建广告链接</a-button>
        <div class="flex_center">
          <div class="ad_name flex_center">
            <div class="icon" :style="{ backgroundColor: '#60A13B' }"></div>
            <a-tooltip popper-class="toolt" placement="bottom" effect="light">
              <span class="name">拼多多福利券</span>
              <template #title>
                <div class="flex_ju_sp">
                  <span>小程序名称：拼多多福利券</span>
                  <CopyOutlined @click="copy('拼多多福利券')" />
                </div>
                <div class="flex_ju_sp">
                  <span>原始ID：gh_a6611aee87d6</span>
                  <CopyOutlined @click="copy('gh_a6611aee87d6')" />
                </div>
              </template>
            </a-tooltip>
          </div>
        </div>
      </div>
      <!-- <div class="custom-searchForm">
        <SearchBaseLayout :data="schemas" :actions="formConfig" @changeValue="submitForm" />
      </div> -->

      <a-table
        :data-source="tableData.list"
        :columns="sdColumns"
        style="width: 100%"
        border
        :header-cell-style="{ background: '#F3F6FC', color: '#080F1E', fontWeight: 500 }"
        height="364"
        :scroll="{ x: 822, y: 300 }"
        @change="onTableChange"
        :pagination="{
          hideOnSinglePage: false,
          showQuickJumper: true,
          total: tableData.total,
          showSizeChanger: true,
          pageSize: tableData.pageSize,
          current: tableData.pageNo,
          size: 'small',
          showTotal: (total) => `共${tableData.total}条数据`
        }"
      >
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'ad_url'">
            <div>
              <a-tooltip>
                <template #title>{{ record.name }}</template>
                <span class="text_overflow block">广告名称：{{ record.name }}</span>
              </a-tooltip>
            </div>
            <div v-if="record.platform_id == 1 && record.put_type == 1">
              <div>安卓ID：********** <CopyOutlined @click="copy('**********')" /></div>
              <div>苹果ID：********** <CopyOutlined @click="copy('**********')" /></div>
            </div>
            <div v-if="record.ad_account_id">
              <a-space :size="[6, 0]">
                <span>账户ID:</span>
                <span class="text_overflow">{{ record.ad_account_id || '--' }}</span>
              </a-space>
            </div>

            <div class="flex_center" v-if="record.mini_page">
              <a-tooltip>
                <template #title>{{ record.mini_page }}</template>
                <span class="text_overflow block"
                  >{{ record.platform_id === 5 ? '小程序路径 : ' : '' }} {{ record.mini_page }}</span
                >
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.mini_page)" />
            </div>

            <div class="flex" v-if="record.land_url">
              <a-tooltip>
                <template #title>{{ record.land_url }}</template>
                <span class="text_overflow block max-w-360px">
                  H5中转页： <span>{{ record.land_url }}</span>
                </span>
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.land_url)" />
            </div>

            <div class="flex" v-if="record.direct_ad_url && record.put_type != 2">
              <a-tooltip>
                <template #title>{{ record.direct_ad_url }}</template>
                <span class="text_overflow block max-w-360px">
                  直达链接： <span>{{ record.direct_ad_url }}</span>
                </span>
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.direct_ad_url)" />
            </div>
            <div class="flex" v-if="record.ad_url && record.put_type == 2">
              <a-tooltip>
                <template #title>{{ record.ad_url }}</template>
                <span class="text_overflow block w-360px">
                  小程序链接： <span>{{ record.ad_url }}</span>
                </span>
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.ad_url)" />
            </div>
            <div class="flex" v-if="record.report_url && record.put_type != 2">
              <a-tooltip>
                <template #title> {{ record.report_url }}</template>
                <span class="text_overflow block"> 报备链接：{{ record.report_url }}</span>
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.report_url)" />
            </div>
            <div class="flex" v-if="record.detection_url && record.put_type != 2">
              <a-tooltip>
                <template #title> {{ record.detection_url }}</template>
                <span class="text_overflow block"> 监测链接：{{ record.detection_url }}</span>
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.detection_url)" />
            </div>

            <div class="flex_align_center mt-6px">
              <!-- <img
                :src="
                  [0, 3, 4].includes(record.jump_type)
                    ? requireImg('goods/icon_app.png')
                    : requireImg('goods/icon_h5.png')
                "
                alt=""
                style="margin-right: 10px"
              /> -->
              <!-- <span class="item-tag item-blue">{{
                record.wechat_landing_type == 1 ? '图文落地页' : '视频落地页'
              }}</span> -->
              <template v-if="record.platform_id == 1">
                <!-- <span class="item-tag item-green"> {{ platform_text[record.platform_id] }}</span> -->
                <span class="item-tag item-green">腾讯广告</span>
                <span class="item-tag item-blue" v-if="record.put_type == 1">直达</span>
                <span class="item-tag item-purple" v-else>小程序</span>
              </template>
              <template v-else-if="record.platform_id == 6">
                <!-- <span class="item-tag item-green"> {{ platform_text[record.platform_id] }}</span> -->
                <span class="item-tag item-blue">巨量广告</span>
              </template>
              <template v-else>
                <span class="item-tag item-cili"> 快手广告</span>
                <!-- <span class="item-tag item-cili">{{ record.jump_type == 3 ? '二跳小程序' : '快手磁力建站' }}</span> -->
              </template>

              <span v-if="record.h5_landing_page_audit" class="item-tag item-green"
                >{{ record.h5_landing_page_audit }} {{ record.h5_landing_page_version }}</span
              >
            </div>
          </template>
          <template v-if="column.dataIndex === 'callback_ratio'">
            <div v-if="[1].includes(record.platform_id) && shopInfo?.limit_callback === 0">
              <div>回传{{ !shopInfo?.limit_callback && goodsDetail.type !== 4 ? '100' : record.callback_ratio }}%</div>
            </div>
            <template v-else>
              <div v-if="record.callback_type === 1">
                回传{{ record.callback_ratio }}%
                <FormOutlined class="ml-4px c-primary cursor-pointer" @click="onShowDialog('edit_feedback', record)" />
              </div>
              <div v-else>
                按金额区间回传
                <FormOutlined class="ml-4px c-primary cursor-pointer" @click="onShowDialog('edit_feedback', record)" />
              </div>
              <div>
                {{
                  {
                    1: '整单金额',
                    2: '单件金额',
                    3: '固定金额',
                    4: '固定比例',
                    5: '金额区间'
                  }[record.callback_amount_type]
                }}回传
              </div>
            </template>
          </template>
          <template v-if="column.dataIndex === 'created_at'">
            {{ record.created_at ? formatDate(record.created_at * 1000) : '' }}
          </template>
          <template v-if="column.dataIndex === 'handle'">
            <div class="handle_btns flex">
              <div class="flex_align_center flex_column">
                <!-- v-auth:GoodsList="['adEdit']" -->
                <a-button type="link" class="p-0" v-auth="['shopShopEditAQI']" @click="onShowDialog('edit', record)"
                  >编辑</a-button
                >
                <a-button type="link" class="p-0" v-if="record.put_type == 1" @click="onPreview(record)">
                  预览
                </a-button>
                <!-- v-auth:GoodsList="['adDelete']" -->
                <a-button type="link" class="p-0 m-0" v-auth="['shopShopDelAQI']" @click="onDel(record)">删除</a-button>
              </div>
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <a-modal
      :title="data.dialog.title"
      v-model:open="data.dialog.visible"
      :width="data.dialog.width"
      :close-modal="data.dialog.type != 'edit'"
      destroyOnClose
      :footer="null"
    >
      <AddAdLink v-if="data.dialog.type == 'edit'" :goodsDetail="goodsDetail" :item="data.item" @event="onEvent" />
      <AddAdLinkFeedback
        v-if="data.dialog.type == 'edit_feedback'"
        :goodsDetail="goodsDetail"
        :item="data.item"
        @event="onEvent"
      />
    </a-modal>
  </div>
</template>
<script setup>
  import { watch, ref, reactive, createVNode } from 'vue'
  import { requireImg, formatDate, copy } from '@/utils'
  import { getAddList, delAdurl, miniCode, getAdAppidListApi, updateAdurl } from '../index.api'
  import { useRouter, useRoute } from 'vue-router'
  import { CopyOutlined, ExclamationCircleOutlined, FormOutlined } from '@ant-design/icons-vue'
  import { message, Modal } from 'ant-design-vue'
  import AddAdLink from './AddAdLink.vue'
  import AddAdLinkFeedback from './AddAdLinkFeedback.vue'
  import { useApp } from '@/hooks'
  const { shopInfo } = useApp()
  const router = useRouter()
  // 导入广告平台、回传行为

  const props = defineProps({
    goodsDetail: {
      type: Object,
      default: () => {}
    }
  })

  const formConfig = reactive({
    foldNum: 0
  })
  const sdColumns = reactive([
    {
      title: '广告投放链接',
      dataIndex: 'ad_url',
      key: 'ad_url'
    },
    {
      title: '券后订单回传',
      dataIndex: 'callback_ratio',
      key: 'callback_ratio',
      width: 140
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    },
    {
      title: '操作',
      dataIndex: 'handle',
      key: 'handle',
      width: 90
    }
  ])

  const tableData = reactive({
    list: [],
    // columns,
    pageNo: 1,
    pageSize: 10,
    total: 1,
    loading: false
  })
  const state = reactive({
    initParams: {
      page: 1,
      page_size: 20,
      product_id: props.goodsDetail.product_id
    }
  })

  const data = reactive({
    adAppIdList: [], //小程序原始ID
    popViseble: false,
    dialog: {
      id: '',
      type: '',
      width: '',
      visible: false,
      titie: ''
    },
    visible: false,
    loading: false,
    item: {},
    goods_details: {},
    preview_url: null,
    callback_ratio: null,
    query: {
      page: tableData.pageNo || 1,
      page_size: tableData.pageSize || 10,
      product_id: props.goodsDetail.product_id,
      useShop: 'shop_id' //店铺id
    }
  })

  // 广告投放列表
  const getAdList = async () => {
    try {
      tableData.loading = true
      const resp = await getAddList(data.query)

      // state.tableConfigOptions.pagination.page = resp.data?.page || 1

      tableData.list = resp.data?.list || []
      tableData.total = resp.data?.total_num || 0
      tableData.loading = false
    } catch (error) {
      console.error(error)
      tableData.loading = false
    }
  }
  const onTableChange = (page) => {
    data.query.page = page.current
    data.query.page_size = page.pageSize
    tableData.pageNo = page.current
    tableData.pageSize = page.pageSize
    getAdList()
  }

  // 风险颜色
  const riskColors = (type) => {
    let status = {
      normal: '#60A13B',
      low_risk: '#B9C2D1',
      medium_risk: '#D4A821',
      high_risk: '#E77316',
      lock_down: '#E63030'
    }
    return status[type]
  }
  const inputRatio = () => {
    const reg = /\d+/
    data.callback_ratio = data.callback_ratio.match(reg)?.[0] || ''
  }

  // 删除
  const onDel = (item) => {
    try {
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '请确认是否删除当前广告？'),
        async onOk() {
          let res = await delAdurl({ id: item.id, useShop: 'shop_id' })
          getAdList()
          console.log(res, 'res.msgres.msg')
          message.success(res.msg)
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  const saveRatio = async (item) => {
    item.visible = false
    try {
      if (data.callback_ratio < 0 || data.callback_ratio > 200) {
        return message.warning('请输入转化比例0-200')
      }
      data.loading = true
      let params = {
        ...item,
        callback_ratio: Number(data.callback_ratio)
      }
      const resp = await updateAdurl({ ...params })
      getAdList()
      message.success('修改成功')
      data.loading = false
    } catch (error) {
      console.error(error)
      data.loading = false
    }
  }

  watch(
    () => props.goodsDetail,
    (val, old) => {
      data.goods_details = { ...val }
      data.query.product_id = data.goods_details.product_id
      getAdList()
    },
    {
      deep: true,
      immediate: true
    }
  )
  // 预览
  const onPreview = async (item) => {
    window.open(item.land_url)
  }
  const onShowDialog = (type, item) => {
    data.item = { ...item }
    switch (type) {
      case 'add':
        data.dialog = { id: null, title: '创建广告链接', width: 680, visible: true, type: 'edit' }
        break
      case 'edit':
        data.dialog = { id: item.id, title: '编辑广告链接', width: 680, visible: true, type: 'edit' }
        break
      case 'edit_feedback':
        data.dialog = { id: item.id, title: '回传比例', width: 680, visible: true, type: 'edit_feedback' }
        break
      case 'link':
        data.dialog = { id: null, title: '广告链接', width: 742, visible: true, type: 'link' }
        break
    }
  }

  const onEvent = (values) => {
    switch (values.cmd) {
      case 'close':
        data.dialog.visible = false
        break
      case 'edit':
        data.dialog.visible = false
        getAdList()
        break
    }
  }
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';
  .text_overflow {
    @include text_overflow(1);
    white-space: wrap;
  }
  .icon {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-right: 10px;
  }
  .item-popover {
    padding: 10px;
  }
  .pop_content {
    .tips {
      color: #999;
      margin: 5px 0;
    }

    .mar {
      margin: 5px 0;
    }
  }

  .ad_name {
    .name {
      margin-right: 30px;
    }
  }
  .icons_item {
    cursor: pointer;
    margin-left: 10px;
  }
  :deep(.item-custom) {
    margin-right: 10px;
    margin-bottom: 0;
  }
  :deep(.item-number) {
    .el-input {
      height: 32px;
    }
  }
  .pop_title {
    margin-bottom: 10px;

    .close {
      cursor: pointer;
    }
  }
  .item-light {
    color: #727a91;
  }
  .item-pdd {
    color: #727a91;
    width: 280px;
  }
  .item-ratio {
    color: #202a4d;
  }
  .item-green {
    border: 1px solid #4da263;
    color: #4da263;
  }
  .item-blue {
    border: 1px solid #3f9fff;
    color: #3f9fff;
  }
  .item-purple {
    border: 1px solid #7c2094;
    color: #7c2094;
  }

  .item-blue {
    border: 1px solid #3f9fff;
    color: #3f9fff;
  }
  .item-green {
    border: 1px solid #70b606;
    color: #70b606;
  }
  .item-cili {
    border: 1px solid #fe4a08;
    color: #fe4a08;
  }
  .item-tag {
    border-radius: 3px;
    font-size: 12px;
    padding: 0 6px;
    margin-right: 16px;
  }
  .space_between {
    justify-content: space-between;
  }

  .text_align_center {
    text-align: center;
  }

  .handle_btns {
    user-select: none;

    span {
      color: var(--el-color-primary);
      cursor: pointer;
      margin-right: 10px;
    }
  }

  .copy_icon {
    margin-left: 10px;
    cursor: pointer;
  }
  .ant-btn + .ant-btn {
    margin-left: 0;
  }
</style>
