<template>
  <div>
    <div>
      <div class="flex_ju_sp mb-12px">
        <a-button type="primary" @click="onShowDialog('add')">创建广告链接</a-button>
        <!-- <div class="flex_center">
          <div class="ad_name flex_center">
            <div class="icon" :style="{ backgroundColor: '#60A13B' }"></div>
            <a-tooltip popper-class="toolt" placement="bottom" effect="light">
              <span class="name">拼多多福利券</span>
              <template #title>
                <div class="flex_ju_sp">
                  <span>小程序名称：拼多多福利券</span>
                  <CopyOutlined @click="copy('拼多多福利券')" />
                </div>
                <div class="flex_ju_sp">
                  <span>原始ID：gh_a6611aee87d6</span>
                  <CopyOutlined @click="copy('gh_a6611aee87d6')" />
                </div>
              </template>
            </a-tooltip>
          </div>
        </div> -->
      </div>
      <!-- <div class="custom-searchForm">
        <SearchBaseLayout :data="schemas" :actions="formConfig" @changeValue="submitForm" />
      </div> -->

      <a-table
        :data-source="tableData.list"
        :columns="sdColumns"
        style="width: 100%"
        border
        :header-cell-style="{ background: '#F3F6FC', color: '#080F1E', fontWeight: 500 }"
        height="364"
        :scroll="{ x: 822, y: 300 }"
        @change="onTableChange"
        :pagination="{
          hideOnSinglePage: false,
          showQuickJumper: true,
          total: tableData.total,
          showSizeChanger: true,
          pageSize: tableData.pageSize,
          current: tableData.pageNo,
          size: 'small',
          showTotal: (total) => `共${tableData.total}条数据`
        }"
      >
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'ad_url'">
            <div>
              <a-tooltip>
                <template #title>{{ record.ad_title }}</template>
                <span class="text_overflow block">广告名称：{{ record.ad_title }}</span>
              </a-tooltip>
            </div>
            <div v-if="record.media_type == 1">
              <div>安卓ID：********* <CopyOutlined @click="copy('*********')" /></div>
              <div>苹果ID：********* <CopyOutlined @click="copy('*********')" /></div>
            </div>
            <div v-if="record.ad_account_id">
              <a-space :size="[6, 0]">
                <span>账户ID:</span>
                <span class="text_overflow">{{ record.ad_account_id || '--' }}</span>
              </a-space>
            </div>
            <div class="flex" v-if="record.click_url">
              <a-tooltip>
                <template #title>{{ record.click_url }}</template>
                <span class="text_overflow block max-w-360px">
                  落地页链接： <span>{{ record.click_url }}</span>
                </span>
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.click_url)" />
            </div>
            <div class="flex" v-if="record.deep_link && record.put_type != 2">
              <a-tooltip>
                <template #title>{{ record.deep_link }}</template>
                <span class="text_overflow block max-w-360px">
                  直达链接： <span>{{ record.deep_link }}</span>
                </span>
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.deep_link)" />
            </div>
            <div class="flex" v-if="record.pv_monitor_url">
              <div class="flex">
                曝光监测链接
                <a-tooltip>
                  <template #title>在三方创建广告时必须配置曝光监测链接</template>
                  <QuestionCircleOutlined class="ml-3px" /> </a-tooltip
                >：
              </div>
              <a-tooltip>
                <template #title>{{ record.pv_monitor_url }}</template>
                <span class="text_overflow block max-w-360px flex-1">
                  {{ record.pv_monitor_url }}
                </span>
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.pv_monitor_url)" />
            </div>
            <div class="flex" v-if="record.click_monitor_url">
              <div class="flex">
                点击监测链接
                <a-tooltip>
                  <template #title>在三方创建广告时必须配置点击监测链接</template>
                  <QuestionCircleOutlined class="ml-3px" /> </a-tooltip
                >：
              </div>
              <a-tooltip>
                <template #title>{{ record.click_monitor_url }}</template>
                <span class="text_overflow block max-w-360px flex-1">
                  {{ record.click_monitor_url }}
                </span>
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.click_monitor_url)" />
            </div>

            <div class="flex" v-if="record.detection_url && record.put_type != 2">
              <a-tooltip>
                <template #title> {{ record.detection_url }}</template>
                <span class="text_overflow block"> 落地页链接：{{ record.detection_url }}</span>
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.detection_url)" />
            </div>

            <div class="flex_align_center mt-6px">
              <template v-if="record.media_type == 1">
                <span class="item-tag item-green">腾讯广告</span>
              </template>
              <template v-else-if="record.media_type == 6">
                <span class="item-tag item-blue">巨量广告</span>
              </template>
              <template v-else>
                <span class="item-tag item-cili"> 快手广告</span>
              </template>
            </div>
          </template>
          <template v-if="column.dataIndex === 'callback_ratio'">
            <div v-if="[1].includes(record.media_type) && shopInfo?.limit_callback === 0">
              <div>回传{{ !shopInfo?.limit_callback && goodsDetail.type !== 4 ? '100' : record.callback_ratio }}%</div>
            </div>
            <template v-else>
              <div v-if="record.callback_type === 1">
                回传{{ record.callback_ratio }}%
                <FormOutlined class="ml-4px c-primary cursor-pointer" @click="onShowDialog('edit_feedback', record)" />
              </div>
              <div v-else>
                按金额区间回传
                <FormOutlined class="ml-4px c-primary cursor-pointer" @click="onShowDialog('edit_feedback', record)" />
              </div>
              <div>
                {{
                  {
                    1: '整单金额',
                    2: '单件金额',
                    3: '固定金额',
                    4: '固定比例',
                    5: '金额区间'
                  }[record.callback_amount_type]
                }}回传
              </div>
            </template>
          </template>
          <template v-if="column.dataIndex === 'created_at'">
            {{ record.created_at || '--' }}
          </template>
          <template v-if="column.dataIndex === 'handle'">
            <div class="handle_btns flex">
              <div class="flex_align_center flex_column">
                <!-- v-auth:GoodsList="['adEdit']" -->
                <a-button type="link" class="p-0" v-auth="['shopShopEditAQI']" @click="onShowDialog('edit', record)"
                  >编辑</a-button
                >
                <!-- <a-button type="link" class="p-0" v-if="record.put_type == 1" @click="onPreview(record)">
                  预览
                </a-button> -->
                <!-- v-auth:GoodsList="['adDelete']" -->
                <a-button type="link" class="p-0 m-0" v-auth="['shopShopDelAQI']" @click="onDel(record)">删除</a-button>
              </div>
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <a-modal
      :title="data.dialog.title"
      v-model:open="data.dialog.visible"
      :width="data.dialog.width"
      :close-modal="data.dialog.type != 'edit'"
      destroyOnClose
      :footer="null"
    >
      <AddAdLink v-if="data.dialog.type == 'edit'" :goodsDetail="goodsDetail" :item="data.item" @event="onEvent" />
      <AddAdLinkFeedback
        v-if="data.dialog.type == 'edit_feedback'"
        :goodsDetail="goodsDetail"
        goods="tb"
        :item="data.item"
        @event="onEvent"
      />
    </a-modal>
  </div>
</template>
<script setup>
  import { watch, ref, reactive, createVNode } from 'vue'
  import { requireImg, formatDate, copy } from '@/utils'
  import { fetchTbAdList, updateTbAd, miniCode, getAdAppidListApi, updateAdurl } from '../index.api'
  import { useRouter, useRoute } from 'vue-router'
  import { CopyOutlined, ExclamationCircleOutlined, QuestionCircleOutlined, FormOutlined } from '@ant-design/icons-vue'
  import { message, Modal } from 'ant-design-vue'
  import AddAdLink from './TbAddAdLink.vue'
  import AddAdLinkFeedback from './AddAdLinkFeedback.vue'
  import { useApp } from '@/hooks'
  const { shopInfo } = useApp()
  const router = useRouter()
  // 导入广告平台、回传行为

  const props = defineProps({
    goodsDetail: {
      type: Object,
      default: () => {}
    }
  })

  const formConfig = reactive({
    foldNum: 0
  })
  const sdColumns = reactive([
    {
      title: '广告投放链接',
      dataIndex: 'ad_url',
      key: 'ad_url'
    },
    {
      title: '订单回传',
      dataIndex: 'callback_ratio',
      key: 'callback_ratio',
      width: 140
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180
    },
    {
      title: '操作',
      dataIndex: 'handle',
      key: 'handle',
      width: 90
    }
  ])

  const tableData = reactive({
    list: [],
    // columns,
    pageNo: 1,
    pageSize: 10,
    total: 1,
    loading: false
  })

  const data = reactive({
    adAppIdList: [], //小程序原始ID
    popViseble: false,
    dialog: {
      id: '',
      type: '',
      width: '',
      visible: false,
      titie: ''
    },
    visible: false,
    loading: false,
    item: {},
    goods_details: {},
    preview_url: null,
    callback_ratio: null,
    query: {
      page: tableData.pageNo || 1,
      size: tableData.pageSize || 10,
      product_id: props.goodsDetail.item_id
    }
  })

  // 广告投放列表
  const getAdList = async () => {
    try {
      tableData.loading = true
      const resp = await fetchTbAdList(data.query)
      tableData.list = resp.data?.list || []
      tableData.total = resp.data?.total || 0
      tableData.loading = false
    } catch (error) {
      console.error(error)
      tableData.loading = false
    }
  }
  const onTableChange = (page) => {
    data.query.page = page.current
    data.query.size = page.pageSize
    tableData.pageNo = page.current
    tableData.pageSize = page.pageSize
    getAdList()
  }

  // 删除
  const onDel = (item) => {
    try {
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '请确认是否删除当前广告？'),
        async onOk() {
          let res = await updateTbAd({ ...item, id_deleted: 1 })
          getAdList()
          console.log(res, 'res.msgres.msg')
          message.success(res.msg)
        }
      })
    } catch (error) {
      console.error(error)
    }
  }

  watch(
    () => props.goodsDetail,
    (val, old) => {
      data.goods_details = { ...val }
      data.query.product_id = data.goods_details.item_id
      getAdList()
    },
    {
      deep: true,
      immediate: true
    }
  )
  // 预览
  const onPreview = async (item) => {
    window.open(item.land_url)
  }
  const onShowDialog = (type, item) => {
    data.item = { ...item }
    switch (type) {
      case 'add':
        data.dialog = { id: null, title: '创建广告链接', width: 640, visible: true, type: 'edit' }
        break
      case 'edit':
        data.dialog = { id: item.id, title: '编辑广告链接', width: 640, visible: true, type: 'edit' }
        break
      case 'edit_feedback':
        data.dialog = { id: item.id, title: '回传比例', width: 680, visible: true, type: 'edit_feedback' }
        break
      // case 'link':
      //   data.dialog = { id: null, title: '广告链接', width: 742, visible: true, type: 'link' }
      //   break
    }
  }

  const onEvent = (values) => {
    switch (values.cmd) {
      case 'close':
        data.dialog.visible = false
        break
      case 'edit':
        data.dialog.visible = false
        getAdList()
        break
    }
  }
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn.scss';
  .text_overflow {
    @include text_overflow(1);
    white-space: wrap;
  }
  .icon {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-right: 10px;
  }
  .item-popover {
    padding: 10px;
  }
  .pop_content {
    .tips {
      color: #999;
      margin: 5px 0;
    }

    .mar {
      margin: 5px 0;
    }
  }

  .ad_name {
    .name {
      margin-right: 30px;
    }
  }
  .icons_item {
    cursor: pointer;
    margin-left: 10px;
  }
  :deep(.item-custom) {
    margin-right: 10px;
    margin-bottom: 0;
  }
  :deep(.item-number) {
    .el-input {
      height: 32px;
    }
  }
  .pop_title {
    margin-bottom: 10px;

    .close {
      cursor: pointer;
    }
  }
  .item-light {
    color: #727a91;
  }
  .item-pdd {
    color: #727a91;
    width: 280px;
  }
  .item-ratio {
    color: #202a4d;
  }
  .item-green {
    border: 1px solid #4da263;
    color: #4da263;
  }
  .item-blue {
    border: 1px solid #3f9fff;
    color: #3f9fff;
  }
  .item-purple {
    border: 1px solid #7c2094;
    color: #7c2094;
  }

  .item-blue {
    border: 1px solid #3f9fff;
    color: #3f9fff;
  }
  .item-green {
    border: 1px solid #70b606;
    color: #70b606;
  }
  .item-cili {
    border: 1px solid #fe4a08;
    color: #fe4a08;
  }
  .item-tag {
    border-radius: 3px;
    font-size: 12px;
    padding: 0 6px;
    margin-right: 16px;
  }
  .space_between {
    justify-content: space-between;
  }

  .text_align_center {
    text-align: center;
  }

  .handle_btns {
    user-select: none;

    span {
      color: var(--el-color-primary);
      cursor: pointer;
      margin-right: 10px;
    }
  }

  .copy_icon {
    margin-left: 10px;
    cursor: pointer;
  }
  .ant-btn + .ant-btn {
    margin-left: 0;
  }
</style>
