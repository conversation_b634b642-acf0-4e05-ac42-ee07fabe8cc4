<template>
  <div class="ad_link">
    <a-form
      :model="data.form"
      ref="ruleForm"
      :rules="adRules"
      :labelCol="{ style: 'width: 120px' }"
      class="ad_form mt-24px"
      autocomplete="off"
    >
      <a-form-item label="广告名称：" name="ad_title">
        <a-input v-model:value="data.form.ad_title" placeholder="请输入广告名称" :maxlength="100" />
      </a-form-item>
      <a-form-item label="媒体类型：" name="media_type" required>
        <a-radio-group :disabled="!!props.item.id" v-model:value="data.form.media_type" @change="changePlatformId">
          <a-radio :value="1" v-if="props.goodsDetail.prod_type == 1">广点通</a-radio>
          <a-radio :value="4" v-if="props.goodsDetail.prod_type == 3">磁力引擎</a-radio>
          <a-radio :value="6" v-if="props.goodsDetail.prod_type == 1">巨量引擎</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="链接类型：" name="link_type" required>
        <a-radio-group v-model:value="data.form.link_type">
          <a-radio :value="1">商详页</a-radio>
          <a-radio :value="2" v-if="[1, 6].includes(data.form.media_type)">ud建站</a-radio>
          <a-radio :value="3" v-if="[1, 6].includes(data.form.media_type)">种草页</a-radio>
          <a-radio :value="4" v-if="[4].includes(data.form.media_type)">云台落地页</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        label="云台落地页："
        v-if="[4].includes(data.form.media_type) && data.form.link_type == 4"
        name="page_name"
        required
      >
        <div class="flex">
          <!-- <a-tag v-if="landing_page_item_data" closable color="#108ee9" @close="closeItem">{{
            landing_page_item_data.name
          }}</a-tag> -->
          <a-input v-model:value="data.form.page_name" disabled />
          <a-button type="primary" class="ml-8px" @click="onShowDialog('Landingpage')">选择云台落地页</a-button>
          <!-- <a-button v-if="landing_page_item_data" type="link" @click="onShowDialog('LandingpageCom')">编辑</a-button> -->
        </div>
      </a-form-item>
      <a-form-item label="UD落地页：" name="ud_page" v-if="data.form.link_type == 2">
        <a-radio-group v-model:value="data.form.ud_page">
          <a-radio :value="2">真实销量&评价</a-radio>
          <a-radio :value="3">同款销量&评价</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="优化目标：" name="convert_target">
        <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" v-model:value="data.form.convert_target">
          <template v-if="data.form.media_type != 4">
            <a-select-option value="12">下单</a-select-option>
          </template>
          <template v-else>
            <a-select-option value="14">下单</a-select-option>
          </template>
          <!-- <template v-if="data.form.media_type == 4">
            <a-select-option value="14">订单提交</a-select-option>
          </template>
          <template v-if="data.form.media_type == 6">
            <a-select-option value="in_app_order">下单</a-select-option>
          </template> -->
        </a-select>
      </a-form-item>
      <template v-if="!shopInfo?.limit_callback && data.form.media_type === 1">
        <a-form-item label="回传比例：" name="callback_ratio" required>
          <div class="coupon">
            <a-input-number
              class="w100%"
              v-model:value="data.form.callback_ratio"
              disabled
              :precision="0"
              placeholder="100"
              addonAfter="%"
            >
            </a-input-number>
            <span class="link_btn"> 广告订单将回传至广告侧，帮助广告侧优化广告投放群体 </span>
          </div>
        </a-form-item>
      </template>
      <template v-else>
        <a-form-item id="callback_type" label="回传单量类型" name="callback_type" required>
          <div class="flex">
            <div class="mr-4px mt-6px">按</div>
            <a-form-item>
              <a-select
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                v-model:value="data.form.callback_type"
                :style="{ width: '160px' }"
                :options="[
                  {
                    label: '固定比例',
                    value: 1
                  },
                  {
                    label: '金额区间',
                    value: 2
                  }
                ]"
                placeholder="请选择回传单量类型"
                :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
                @change="(val: number) => callback_handler(val, 'type')"
              ></a-select>
            </a-form-item>
            <a-form-item
              class="ml-4px"
              v-if="data.form.callback_type === 1"
              name="callback_role"
              :rules="{
                required: true,
                validator: (_: any, __: string, callback: Function) => {
                  if (!data.form.callback_role && data.form.callback_role !== 0) {
                    callback('请输入固定比例')
                  }
                  callback()
                },
                trigger: ['change', 'blur']
              }"
            >
              <a-input-number
                v-if="[1, 6].includes(data.form.media_type)"
                class="w-120px"
                v-model:value="data.form.callback_role"
                :precision="0"
                :min="0"
                :max="100"
                addonAfter="%"
                :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
              >
              </a-input-number>
              <a-select
                v-else
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                v-model:value="data.form.callback_role"
                class="w-140px"
                :options="[
                  {
                    label: 10,
                    value: 10
                  },
                  {
                    label: 20,
                    value: 20
                  },
                  {
                    label: 30,
                    value: 30
                  },
                  {
                    label: 40,
                    value: 40
                  },
                  {
                    label: 50,
                    value: 50
                  },
                  {
                    label: 60,
                    value: 60
                  },
                  {
                    label: 70,
                    value: 70
                  },
                  {
                    label: 80,
                    value: 80
                  },
                  {
                    label: 90,
                    value: 90
                  },
                  {
                    label: 100,
                    value: 100
                  }
                ]"
                placeholder="请选择回传比例"
                :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
              ></a-select>
            </a-form-item>
            <div class="ml-4px mt-6px">
              回传广告单量
              <a-tooltip>
                <template #title>
                  支持按比例设置广告订单的回传数量
                  {{ data.form.media_type == 4 ? '' : '，填0%即不回传，填100%即全量回传' }}
                </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-tooltip>
            </div>
          </div>
          <div class="solid-wrapper" v-if="data.form.callback_type === 2">
            <div class="c-#85878a flex-y-center">
              <span class="c-#ff4d4f">*</span>
              区间中的广告流量订单按比例回传，不在区间中的广告流量订单不回传
            </div>
            <div class="mt-12px mb-12px c-#85878a">
              <span>金额区间</span>
              <span class="ml-234px">广告流量订单回传比例</span>
            </div>
            <div class="flex-y-center item" v-for="(item, index) in data.form.callback_role_list" :key="index">
              <a-form-item
                class="w-120px flex-none"
                :name="'callback_type' + index + '_min_price'"
                :rules="{
                  validator: (_: any, __: string, callback: Function) => {
                    if (!item.min_price) {
                      callback('请输入最小金额')
                    }
                    if (item.max_price && item.min_price && item.min_price > item.max_price) {
                      callback('金额不能交叉')
                    }
                    if (
                      item.min_price &&
                      index > 0 &&
                      item.min_price < Number(data.form?.callback_role_list?.[index - 1]?.max_price)
                    ) {
                      callback('金额不能交叉')
                    }
                    callback()
                  },
                  trigger: ['change', 'blur']
                }"
              >
                <a-input-number
                  v-model:value="item.min_price"
                  :min="0.01"
                  placeholder="最小金额"
                  :precision="2"
                  :max="99999999"
                  addonAfter="元"
                  :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
                  @blur="resetFieldCallbackList(data.form.callback_role_list.length, 'callback_type')"
                >
                </a-input-number>
              </a-form-item>
              <div class="flex-none ml-4px mr-4px mt--16px c-#85878a">-</div>
              <a-form-item
                class="w-120px flex-none"
                :name="'callback_type' + index + '_max_price'"
                :rules="{
                  validator: (_: any, __: string, callback: Function) => {
                    if (!item.max_price) {
                      callback('请输入最大金额')
                    }
                    if (item.max_price && item.min_price && item.max_price <= item.min_price) {
                      callback('金额不能重复或交叉2')
                    }
                    let nextMin =
                      index + 1 <= (data.form.callback_role_list || [])?.length
                        ? data.form.callback_role_list?.[index + 1]?.min_price
                        : undefined
                    if (nextMin && item.max_price && nextMin < Number(item.max_price)) {
                      callback('金额不能重复或交叉')
                    }
                    callback()
                  },
                  trigger: ['blur', 'change']
                }"
              >
                <a-input-number
                  :min="0.01"
                  v-model:value="item.max_price"
                  placeholder="最大金额"
                  :precision="2"
                  :max="99999999"
                  :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
                  @blur="resetFieldCallbackList(data.form.callback_role_list.length, 'callback_type')"
                  addonAfter="元"
                >
                </a-input-number>
              </a-form-item>
              <div class="flex-none ml-4px mr-4px mt--16px c-#85878a">回传</div>
              <a-form-item
                class="w-160px flex-none"
                :name="'callback_type' + index + '_ration'"
                :rules="{
                  validator: (_: any, __: string, callback: Function) => {
                    if (!item.ratio && item.ratio !== 0) {
                      callback('请输入订单比例')
                    }
                    callback()
                  },
                  trigger: ['change', 'blur']
                }"
              >
                <a-input-number
                  v-model:value="item.ratio"
                  placeholder="填写订单比例"
                  :precision="0"
                  :min="0"
                  :max="100"
                  addonAfter="%"
                  :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
                >
                </a-input-number>
              </a-form-item>
              <MinusCircleOutlined
                class="font-size-16px c-#85878a cursor-pointer ml-4px mt--16px"
                @click="deleteItem('type', index)"
              />
            </div>
            <a-button
              type="link"
              class="p-0! h-auto!"
              :disabled="
                (data.form.callback_role_list && data.form.callback_role_list.length >= 10) ||
                (shopInfo?.limit_callback === 0 && data.form.media_type === 1)
              "
              @click="addItem('type')"
              >添加阶梯</a-button
            >
          </div>
        </a-form-item>
        <a-form-item id="callback_amount">
          <template #label>
            <div class="flex-align">
              <span>回传金额</span>
              <a-tooltip>
                <template #title>
                  整单金额，即按用户的实付金额回传； <br />
                  单件金额，即按实付金额/件数的金额回传；<br />
                  按固定金额，即回传金额为指定金额； <br />
                  按固定比例，即回传金额为{{ data.form.media_type === 4 ? '原价' : '实付金额' }}*比例；<br />
                  按金额区间，即实付金额落在金额区间中的订单，按所设比例回传；<br />
                  如50-100元回传100%，101-150元回传50%；
                </template>
                <QuestionCircleFilled class="font-size-12px c-#C5C6CC ml-2px!" />
              </a-tooltip>
            </div>
          </template>
          <div class="flex">
            <span class="mr-4px mt-6px">按</span>
            <a-form-item>
              <a-select
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                v-model:value="data.form.callback_amount_type"
                :style="{ width: '160px' }"
                @change="(val: number) => callback_handler(val, 'amount')"
                :options="[
                  {
                    label: '整单金额',
                    value: 1
                  },
                  {
                    label: '单件金额',
                    value: 2
                  },
                  {
                    label: '固定金额',
                    value: 3
                  },
                  {
                    label: '固定比例',
                    value: 4
                  },
                  {
                    label: '金额区间',
                    value: 5
                  }
                ]"
                placeholder="请选择回传金额类型"
                :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
              ></a-select>
            </a-form-item>
            <a-form-item
              class="ml-4px"
              v-if="data.form.callback_amount_type === 3"
              name="callback_amount_role"
              :rules="{
                validator: (_: any, __: string, callback: Function) => {
                  if (!data.form.callback_amount_role) {
                    callback('请输入固定金额')
                  }
                  callback()
                },
                trigger: ['change', 'blur']
              }"
            >
              <a-input-number
                class="w-120px"
                v-model:value="data.form.callback_amount_role"
                :precision="2"
                :min="0.01"
                :max="99999999"
                :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
                addonAfter="元"
              >
              </a-input-number>
            </a-form-item>
            <a-form-item
              class="ml-4px"
              v-if="data.form.callback_amount_type === 4"
              name="callback_amount_role"
              :rules="{
                validator: (_: any, __: string, callback: Function) => {
                  if (!data.form.callback_amount_role && data.form.callback_amount_role !== 0) {
                    callback('请输入固定比例')
                  }
                  callback()
                },
                trigger: ['change', 'blur']
              }"
            >
              <a-input-number
                class="w-120px"
                v-model:value="data.form.callback_amount_role"
                :precision="0"
                :min="0"
                :max="100"
                addonAfter="%"
                :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
              >
              </a-input-number>
            </a-form-item>
            <span class="ml-4px mt-6px">回传</span>
          </div>
          <div class="solid-wrapper" v-if="data.form.callback_amount_type === 5">
            <div class="mb-12px c-#85878a">
              <span>金额区间</span>
              <!-- <span class="ml-234px">回流订单回传比例</span> -->
            </div>
            <div class="flex-y-center item" v-for="(item, index) in data.form.callback_amount_role_list" :key="index">
              <a-form-item
                class="w-120px flex-none"
                :name="'callback_amount' + index + '_min_price'"
                :rules="{
                  validator: (_: any, __: string, callback: Function) => {
                    if (!item.min_price) {
                      callback('请输入最小金额')
                    }
                    if (item.max_price && item.min_price && item.min_price > item.max_price) {
                      callback('金额不能交叉')
                    }
                    if (
                      item.min_price &&
                      index > 0 &&
                      item.min_price < Number(data.form?.callback_amount_role_list?.[index - 1]?.max_price)
                    ) {
                      callback('金额不能交叉')
                    }
                    callback()
                  },
                  trigger: ['blur', 'change']
                }"
              >
                <a-input-number
                  v-model:value="item.min_price"
                  :min="0.01"
                  placeholder="最小金额"
                  :precision="2"
                  :max="99999999"
                  addonAfter="元"
                  :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
                  @blur="resetFieldCallbackList(data.form.callback_amount_role_list.length, 'callback_amount')"
                >
                </a-input-number>
              </a-form-item>
              <span class="ml-4px mr-4px mt--16px c-#85878a">-</span>
              <a-form-item
                class="w-120px flex-none"
                :name="'callback_amount' + index + '_max_price'"
                :rules="{
                  validator: (_: any, __: string, callback: Function) => {
                    if (!item.max_price) {
                      callback('请输入最大金额')
                    }
                    if (item.max_price && item.min_price && item.max_price <= item.min_price) {
                      callback('金额不能交叉')
                    }
                    let nextMin =
                      index + 1 <= (data.form.callback_amount_role_list || [])?.length
                        ? data.form.callback_amount_role_list?.[index + 1]?.min_price
                        : undefined
                    if (nextMin && item.max_price && nextMin < Number(item.max_price)) {
                      callback('金额不能交叉')
                    }
                    callback()
                  },
                  trigger: ['blur', 'change']
                }"
              >
                <a-input-number
                  :min="0.01"
                  v-model:value="item.max_price"
                  placeholder="最大金额"
                  :precision="2"
                  :max="99999999"
                  addonAfter="元"
                  :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
                  @blur="resetFieldCallbackList(data.form.callback_amount_role_list.length, 'callback_amount')"
                >
                </a-input-number>
              </a-form-item>
              <span class="ml-4px mr-4px mt--16px c-#85878a">回传</span>
              <a-form-item
                class="w-160px flex-none"
                :name="'callback_amount' + index + '_ration'"
                :rules="{
                  validator: (_: any, __: string, callback: Function) => {
                    if (!item.ratio && item.ratio !== 0) {
                      callback('请输入订单比例')
                    }
                    callback()
                  },
                  trigger: ['blur', 'change']
                }"
              >
                <a-input-number
                  v-model:value="item.ratio"
                  placeholder="填写订单比例"
                  :precision="0"
                  :min="0"
                  :max="100"
                  addonAfter="%"
                  :disabled="shopInfo?.limit_callback === 0 && data.form.media_type === 1"
                >
                </a-input-number>
              </a-form-item>
              <MinusCircleOutlined
                class="font-size-16px c-#85878a cursor-pointer ml-4px mt--16px"
                @click="deleteItem('amount', index)"
              />
            </div>
            <a-button
              type="link"
              class="p-0! h-auto!"
              :disabled="
                data.form.callback_amount_role_list.length >= 10 ||
                (shopInfo?.limit_callback === 0 && data.form.media_type === 1)
              "
              @click="addItem('amount')"
              >添加阶梯</a-button
            >
          </div>
        </a-form-item>
      </template>
    </a-form>

    <div class="fooler_btn" v-if="props.type != 'detail'">
      <AButton :mr="30" @click="close">取消</AButton>
      <AButton type="primary" :loading="data.loading" @click="submitForm(ruleForm)">确定</AButton>
    </div>
    <a-modal
      v-model:open="data.dialog.visible"
      :title="data.dialog.title"
      :width="data.dialog.width"
      :footer="null"
      destroyOnClose
    >
      <TbLandingPage v-if="data.dialog.type === 'Landingpage'" use="ad" :item="goodsDetail" @event="onEvent" />
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  import { updateTbAd, updateAdurl } from '../index.api'

  import { watch, reactive, ref, nextTick } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { message, Modal } from 'ant-design-vue'
  import { useApp } from '@/hooks'
  import { QuestionCircleFilled, MinusCircleOutlined } from '@ant-design/icons-vue'
  import TbLandingPage from './TbLandingPage.vue'
  const router = useRouter()
  const route = useRoute()
  const { shopInfo } = useApp()
  const ruleForm = ref(null)
  // 导入form验证规则
  const adRules = reactive({
    ad_title: [{ required: true, message: '请输入广告名称', trigger: ['change', 'blur'] }],
    page_name: [{ required: true, message: '请选择云台落地页', trigger: ['change', 'blur'] }],
    // ad_account_id: [{ required: true, message: '请输入广告账户编号', trigger: ['change', 'blur'] }],
    demo_url: [{ required: true, message: '请上传视频素材图片', trigger: ['change', 'blur'] }],
    promotion_code_url: [{ required: true, message: '请上传视频预览码', trigger: ['change', 'blur'] }],
    mall_certificate_url: [{ required: true, message: '请上传店铺营业执照', trigger: ['change', 'blur'] }],

    convert_target: [{ required: true, message: '请选择回传行为', trigger: ['change', 'blur'] }],
    callback_time: [{ required: true, message: '请选择回传节点', trigger: ['change', 'blur'] }]
  })

  const props = defineProps({
    goodsDetail: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    },
    item: {
      type: Object,
      default: () => {}
    }
  })

  const emit = defineEmits(['event'])

  const data = reactive({
    loading: false,
    form: {
      ad_title: '',
      media_type: props.goodsDetail.prod_type == 1 ? 1 : 4,
      link_type: 1,
      ud_page: 2,
      page_name: '',
      page_id: '',
      convert_target: null,
      callback_ratio: 100,
      callback_type: 1,
      callback_role: 100,
      callback_role_list: [],
      product_type: 1,
      callback_amount_type: 1,
      callback_amount_role: undefined,
      callback_amount_role_list: []
    },
    noDomain: false,
    backList: [],
    stayList: [],
    fullList: [],

    adList: [], // 账户ID
    fetching: false,
    dialog: {
      id: '',
      title: '',
      visible: false,
      width: '',
      type: ''
    },
    kefuGroupList: [],
    editRatio: false
  })

  const close = () => {
    emit('event', { cmd: 'close' })
  }
  const onShowDialog = (type) => {
    switch (type) {
      case 'Landingpage':
        data.dialog = { title: '云台落地页', width: 960, visible: true, type: type }
        break
    }
  }
  // 金额区间change
  const callback_handler = (val: number, type: string) => {
    if (type === 'type') {
      if (val === 1) {
        data.form.callback_role = 100
        data.form.callback_role_list = []
      } else {
        data.form.callback_role = undefined
        if (!data.form.callback_role_list?.length) {
          let item: CallbackListItem = { min_price: undefined, max_price: undefined, ratio: undefined }
          data.form.callback_role_list?.push(item)
        }
      }
      return
    }
    if (type === 'amount') {
      if (val === 5) {
        data.form.callback_amount_role = undefined
        if (!data.form.callback_amount_role_list?.length) {
          let item: CallbackListItem = { min_price: undefined, max_price: undefined, ratio: undefined }
          data.form.callback_amount_role_list?.push(item)
        }
      } else {
        data.form.callback_amount_role = undefined
        data.form.callback_amount_role_list = []
      }

      return
    }
  }
  //  价格区间校验
  const resetFieldCallbackList = (len: number, type: string) => {
    let callback_type_arr = []
    let callback_amount_arr = []
    if (type === 'callback_type') {
      for (let i = 0; i < len; i++) {
        callback_type_arr.push('callback_type' + i + '_min_price')
        callback_type_arr.push('callback_type' + i + '_max_price')
      }
      ruleForm?.value?.validateFields(callback_type_arr)
    } else {
      for (let i = 0; i < len; i++) {
        callback_amount_arr.push('callback_amount' + i + '_min_price')
        callback_amount_arr.push('callback_amount' + i + '_max_price')
      }
      ruleForm.value.validateFields(callback_amount_arr)
    }
  }
  // 添加
  const addItem = (type: string) => {
    let list = type === 'type' ? data.form.callback_role_list : data.form.callback_amount_role_list
    if (list?.some((it) => !it.min_price || !it.max_price || (!it.ratio && it.ratio !== 0)))
      return message.warning('请先填写完金额区间及回传比例')
    let item: CallbackListItem = { min_price: undefined, max_price: undefined, ratio: undefined }
    list?.push(item)
  }
  // 删除
  const deleteItem = (type: string, index: number) => {
    let list = type === 'type' ? data.form.callback_role_list : data.form.callback_amount_role_list
    if ((list || [])?.length <= 1) return message.warning('至少有一条信息')
    list?.splice(index, 1)
  }
  const onEvent = (values) => {
    switch (values.cmd) {
      case 'close':
        data.dialog.visible = false
        break
      case 'add':
        console.log(values.data, 'values.data')

        data.dialog.visible = false
        data.form.page_id = values.data.page_id
        data.form.page_name = values.data.name
        break
    }
  }

  // 提交
  const submitForm = (formName) => {
    formName
      .validate()
      .then(async () => {
        data.form.id ? await update() : await save()
      })
      .catch((err) => {
        console.log('error', err)
      })
  }

  const paramsFunc = () => {
    let params = {
      ad_title: data.form.ad_title || '',
      product_id: props.goodsDetail.item_id,
      media_type: Number(data.form.media_type),
      link_type: Number(data.form.link_type),
      ud_page: data.form.media_type == 4 ? undefined : Number(data.form.ud_page),
      page_id: data.form.page_id || undefined,
      page_name: data.form.page_name || undefined,
      convert_target: data.form.convert_target || '',
      callback_ratio: data.form.callback_ratio ? Number(data.form.callback_ratio) : 0
    }
    let newParams = {}
    if (data.form.media_type) {
      newParams = {
        callback_amount_type: data.form.callback_amount_type,
        callback_type: data.form.callback_type
      } as any
      if (data.form.callback_amount_type === 1 || data.form.callback_amount_type === 2) {
        newParams.callback_amount_role = undefined
      } else if (data.form.callback_amount_type === 3) {
        newParams.callback_amount_role = data.form?.callback_amount_role
          ? data.form?.callback_amount_role + ''
          : undefined
      } else if (data.form.callback_amount_type === 4) {
        newParams.callback_amount_role = data.form.callback_amount_role + ''
      } else {
        newParams.callback_amount_role = JSON.stringify(data.form.callback_amount_role_list)
      }

      if (data.form.callback_type === 1) {
        newParams.callback_role = undefined
        newParams.callback_ratio = data.form.callback_role
      } else {
        newParams.callback_role = data.form.callback_role_list
      }
    }

    return { ...params, ...newParams }

    // return { ...params }
  }

  // 添加
  const save = async () => {
    try {
      data.loading = true
      let params = paramsFunc()
      const resp = await updateTbAd(params)
      ruleForm.value.resetFields()
      emit('event', { cmd: 'edit' })
      message.success('操作成功')
      data.loading = false
    } catch (error) {
      console.error(error)
      data.loading = false
    }
  }
  // 更新
  const update = async () => {
    try {
      data.loading = true
      let params = paramsFunc()
      await updateTbAd({ ...params, id: data.form.id })
      ruleForm.value.resetFields()
      emit('event', { cmd: 'edit' })
      message.success('操作成功')
      data.loading = false
    } catch (error) {
      console.error(error)
      data.loading = false
    }
  }

  const initData = async () => {
    if (props.item?.id) {
      data.form = {
        id: props.item.id || '',
        ad_title: props.item.ad_title || '',
        ud_page: props.item.ud_page || 2,
        media_type: Number(props.item.media_type) || '',
        link_type: Number(props.item.link_type) || '',
        convert_target: props.item.convert_target || '',
        page_id: props.item.page_id || undefined,
        page_name: props.item.page_name || undefined,
        callback_ratio: props.item.callback_ratio || 0
      }
      // 处理底部的回传
      let res = props.item
      data.form.callback_type = res.callback_type
      if (res.callback_type === 1) {
        data.form.callback_role = res.callback_ratio
        data.form.callback_role_list = []
      } else {
        data.form.callback_role = undefined
        data.form.callback_role_list = res.callback_role || []
      }
      data.form.callback_amount_type = res.callback_amount_type
      if (res.callback_amount_type === 3) {
        data.form.callback_amount_role = +res.callback_amount_role
        data.form.callback_amount_role_list = []
      } else if (res.callback_amount_type === 4) {
        data.form.callback_amount_role = +res.callback_amount_role
        data.form.callback_amount_role_list = []
      } else if ([1, 2].includes(res.callback_amount_type)) {
        data.form.callback_amount_role = undefined
        data.form.callback_amount_role_list = []
      } else {
        data.form.callback_amount_role = undefined
        data.form.callback_amount_role_list = res.callback_amount_role
          ? JSON.parse(res.callback_amount_role)
          : [{ min_price: undefined, max_price: undefined, ratio: undefined }]
      }
      if (data.form.media_type === 1 && !shopInfo.value?.limit_callback) {
        data.form.callback_ratio = 100
        data.form.callback_amount_type = 1
        data.form.callback_amount_role = undefined
      }
      if (data.form.media_type === 1 && shopInfo.value?.limit_callback && !props.item.callback_type) {
        data.form.callback_type = 1
        data.form.callback_role = 100
        data.form.callback_amount_type = 1
      }
    }

    nextTick(() => {
      ruleForm.value.resetFields()
    })
  }
  watch(() => props.item, initData, { immediate: true })
  const changePlatformId = (event) => {
    data.form.convert_target = undefined
    data.form.callback_ratio = undefined
    if (event.target.value == 1) {
      data.form.callback_ratio = 100
    }
  }
</script>
<style lang="scss" scoped>
  .ad_form::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    /**/
  }

  .ad_form::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
  }

  .ad_form::-webkit-scrollbar-thumb {
    background: #c5c5c5;
    border-radius: 10px;
    display: block;
    padding-left: 30px;
  }

  .ad_link {
    margin-right: -15px;

    .ad_form {
      max-height: 600px;
      overflow: auto;
      padding-right: 18px;
      .jump_type_cls {
        height: 30px;
        line-height: var(--height-large);
      }

      .coupon {
        .title {
          .switch_name {
            display: inline-block;
            width: 80px;
            color: #404040;
          }
        }

        .link_btn {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #85878a;
          line-height: 24px;
          display: inline-block;
          margin-top: 5px;

          .btn {
            color: #118bce;
            cursor: pointer;
          }
        }
      }
      .image_list {
        flex-wrap: wrap;
        flex-direction: column;

        .item,
        .upload {
          position: relative;
          width: 101px;
          height: 101px;
          background: #f1f5fc;
          border-radius: 4px;
          cursor: pointer;
          // overflow: hidden;
          margin-bottom: 10px;
        }

        .item {
          margin-right: 21px;
          .imgg {
            width: 100%;
            height: 100%;
            border-radius: 4px;
          }

          .name {
            height: 30px;
            background: linear-gradient(0deg, #000000, rgba(0, 0, 0, 0.01));
            border-radius: 0px 0px 4px 4px;
            color: #fff;
            position: absolute;
            bottom: 0;
            width: 100%;
            text-align: center;
            box-sizing: border-box;
            padding: 0 5px;
          }

          .del_img {
            position: absolute;
            top: -10px;
            right: -7px;
            display: none;
          }

          &:hover {
            .del_img {
              display: inline-block;
            }
          }
        }

        .upload {
          border: 1px dashed #d8dde8;

          .add {
            width: 26px;
          }

          .desc {
            color: #999;
            margin-top: 9px;
            line-height: normal;
          }
        }
      }
    }
    .temp-name {
      max-width: 200px;
    }
    .ellipsis {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .flex-align {
      display: flex;
      align-items: center;
    }
    .fooler_btn {
      text-align: end;
      margin-right: 20px;
    }
  }
  .callback-tips {
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #85878a;
    line-height: 24px;
    display: inline-block;
    margin-top: 5px;
  }
</style>
