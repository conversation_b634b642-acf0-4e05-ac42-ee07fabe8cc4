<template>
  <a-space direction="vertical" class="w-full">
    <SearchBaseLayout :data="searchData" @changeValue="changeValue" :actions="formConfig" />
    <DescTableLayout :data="state.tableConfigOptions" @change="pageChange">
      <template #desc="{ data }">
        <div class="desc-table-layout-list-item-desc flex flex-justify-between">
          <a-space>
            <!-- <span class="mini_program_logo">
              <img src="@/assets/images/order/pdd_icon.png" />
            </span>
            <span>{{ data.seller_shop_title || '--' }}</span> -->
            <span>订单编号: {{ data.trade_parent_id }}</span>
            <span class="flex flex-items-center">
              <span>来源: </span>
              <template v-if="true">
                <a-tooltip placement="topLeft">
                  <template #title>{{ channelType(data.channel_type) }}</template>
                  <img
                    v-if="[1, 4, 5, 6].includes(data.channel_type)"
                    class="icon m-l-4px w-14px mr-4px"
                    :src="iconType(data.channel_type)"
                    alt=""
                  />
                </a-tooltip>
                <!-- <span>{{ data.recall_type == 1 ? '弃单召回' : ordersource(data.order_source) }}</span> -->
                <span>广告平台</span>
                <span v-if="![1, 4].includes(data.order_source)"
                  >（计划ID：{{ data.c_group_id == 0 ? '--' : data.c_group_id }}
                  <span style="width: 5px; display: inline-block"></span> 账户ID：{{ data.ad_account_id || '--'
                  }}<a-tooltip>
                    <template #title
                      >广告账户授权异常，为避免报表数据错误，请点击
                      <span class="cursor-pointer c-#97DEFF" @click="getAuthUrl">去授权</span></template
                    >
                    <ExclamationCircleOutlined
                      v-if="data.is_authorized == 1"
                      class="c-#e63030 ml-10px mr-5px"
                    /> </a-tooltip
                  >）</span
                >
              </template>
              <template v-else> -- </template>
            </span>
          </a-space>
          <a-space>
            <span>负责人: {{ data.admin_name }}</span>
            <span>支付时间: {{ secToTime(data.tk_paid_time) }}</span>
          </a-space>
        </div>
      </template>
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'title'">
          <div class="flex goods_info">
            <div class="img">
              <a-image
                :width="60"
                :preview="false"
                style="width: 60px; height: 60px; border-radius: 6px"
                :src="scope.record.item_img"
              />
            </div>
            <div class="goods_info_data">
              <a-tooltip placement="top">
                <template #title>{{ scope.record.item_title }}</template>
                <span class="goods_info_data_name">
                  <img
                    :src="
                      scope.record.channel_type == 1
                        ? state.gdtIcon
                        : scope.record.channel_type == 6
                          ? state.oceanIcon
                          : state.ciliIcon
                    "
                    class="w-14px h-14px inline-block"
                  />
                  {{ scope.record.item_title }}
                </span>
              </a-tooltip>
              <span class="goods_info_data_number grey-color">商品ID：{{ scope.record.item_id }}</span>
            </div>
          </div>
        </template>
        <template v-if="scope.column.key === 'status'">
          <a-space direction="vertical">
            <span>{{ getOrderStatus(scope.record.tk_status) }}</span>
            <span class="primary-color">{{ getCallbackStatus(scope.record.callback_status) }}</span>
          </a-space>
        </template>
        <template v-if="scope.column.key === 'alipay_total_price'">
          <span>{{ scope.record.alipay_total_price.toFixed(2) }}</span>
        </template>
        <template v-if="scope.column.key === 'income_rate'">
          <span>{{ scope.record.income_rate }}%</span>
        </template>
        <template v-if="scope.column.key === 'ad_id'">
          <div class="flex_align_center grey-color">
            <span class=""> 广告账户： </span>

            <a-tooltip placement="top">
              <template #title>{{ scope.record.ad_account_name }}</template>
              <span class="ad_url">
                {{ scope.record.ad_account_name || '-' }}
              </span>
            </a-tooltip>
          </div>
          <div class="flex_align_center grey-color">
            <span class=""> 账户ID：</span>
            <span>{{ scope.record.ad_account_id || '-' }}</span>
            <a-tooltip>
              <template #title
                >广告账户授权异常，为避免报表数据错误，请点击
                <span class="cursor-pointer c-#97DEFF" @click="getAuthUrl(scope.record)">去授权</span></template
              >
              <ExclamationCircleOutlined v-if="scope.record.is_authorized == 1" class="c-#e63030 ml-3px" />
            </a-tooltip>
          </div>
          <div class="flex_align_center grey-color">
            <span class=""> 广告创意ID：</span>
            <span v-if="scope.record.channel_type != 6">{{ scope.record.c_id || '-' }}</span>
            <span v-else>--</span>
          </div>
          <div class="flex_align_center grey-color">
            <span class=""> 广告计划ID：</span>
            <span>{{ scope.record.c_id || '-' }}</span>
          </div>
          <div class="flex_align_center grey-color">
            <span class=""> 广告链接：</span>
            <a-tooltip placement="top">
              <template #title>{{ scope.record.ad_url }}</template>
              <span class="ad_url">
                {{ scope.record.ad_url || '-' }}
              </span>
            </a-tooltip>
            <CopyOutlined v-if="scope.record.ad_url" @click="copy(scope.record.ad_url)" />
          </div>
        </template>
        <template v-if="scope.column.key === 'activity_promoti'">
          <span>{{ getSenceStatus(scope.record.order_scene) }}</span>
        </template>
        <template v-if="scope.column.key === 'admin_name'">
          <span>{{ scope.record.admin_name || '--' }}</span>
        </template>
        <template v-if="scope.column.key === 'created_at'">
          <div class="flex-col">
            <span>{{ scope.record.created_at ? secToTime(scope.record.created_at) : '--' }}</span>
          </div>
        </template>
      </template>
    </DescTableLayout>
    <Pagination
      v-model:page="state.initParams.page"
      v-model:pageSize="state.initParams.page_size"
      :total="state.tableConfigOptions.pagination.total"
      @change="pageChange"
    ></Pagination>
  </a-space>
</template>
<script setup lang="ts">
  import dayjs from 'dayjs'
  import { formatDate, copy, requireImg, secToTime } from '@/utils'
  import { useRoute, useRouter } from 'vue-router'
  import { exportCreate, setAuthUrl } from '@/api/common'
  // import { setAuthUrl } from '@/api/common'
  import { getOrderList, fetchShopList } from '../index/index.api'
  import { CopyOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import Pagination from '@/components/ui/common/PaginationComponent/index.vue'

  import { useTheme, useDownloadCenter } from '@/hooks'
  const { goCenter } = useDownloadCenter()
  const { themeVar } = useTheme()
  import { ref, reactive, onMounted, createVNode } from 'vue'
  import datas from './src/datas'
  const { searchData, formConfig, columns, callbackStatus, senceStatus, channelType, iconType, ordersource } = datas()
  const router = useRouter()
  const route = useRoute()

  const orderStatus = reactive([
    {
      label: '未支付',
      value: -1
    },
    {
      label: '已支付',
      value: 0
    },
    {
      label: '已成团',
      value: 1
    },
    {
      label: '确认收货',
      value: 2
    },
    {
      label: '审核成功',
      value: 3
    },
    {
      label: '审核失败（不可提现）',
      value: 4
    },
    {
      label: '已经结算',
      value: 5
    },
    {
      label: '-非多多进宝商品（无佣金订单）',
      value: 8
    },
    {
      label: '已处罚',
      value: 10
    }
  ])

  const getOrderStatus = (val) => {
    return orderStatus.find((item) => item.value == val)?.label || '--'
  }
  const getCallbackStatus = (val) => {
    return callbackStatus.find((item) => item.value == val)?.label || '--'
  }
  const getSenceStatus = (val) => {
    return senceStatus.find((item) => item.value == val)?.label || '--'
  }
  const state = reactive({
    initParams: {
      page: 1,
      page_size: 10,
      product: null,
      order_num: null,
      order_status: -1,
      callback_status: 0,
      order_scene: null,
      ad_account_id: null,
      ad_url: null,
      created_at: `${dayjs().subtract(6, 'day').format('YYYY-MM-DD')}_${dayjs().format('YYYY-MM-DD')}`,
      pay_begin_time: `${dayjs().subtract(6, 'day').format('YYYY-MM-DD')}_${dayjs().format('YYYY-MM-DD')}` //
    },
    oceanIcon: requireImg('cid/icon_ocean_check.png'),
    gdtIcon: requireImg('cid/icon_gd_check.png'),
    ciliIcon: requireImg('cid/icon_cl_check.png'),
    tableConfigOptions: {
      isLoading: false,
      size: 'small',
      columns,
      list: [],

      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        pageSize: 10,
        current: 1,
        size: 'small',
        showTotal: (total) => `共${total}条数据`
      }
    },

    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: ''
    },
    cateGoryList: [],
    selectionItem: [] // 表格选择的Item
  })

  const pageChange = (pagination) => {
    console.log(pagination, '~~~~~~~~~~~~~`')

    state.initParams.page = pagination.page
    state.initParams.page_size = pagination.page_size
    state.tableConfigOptions.pagination.pageSize = pagination.page_size
    getList()
  }

  // 获取d授权链接
  const getAuthUrl = async (row) => {
    try {
      // const res =
      //   row.channel_type == 1 ? await setAuthUrl({ scan: 'ACCOUNT_ROLE_TYPE_ADVERTISER' }) : await setOceanAuthUrl()
      // window.open(res.data.url, '_blank')
    } catch (error) {
      console.error(error)
    }
  }
  // 搜索
  const changeValue = (data) => {
    console.log('search 参数', data)
    state.initParams = {
      ...state.initParams,
      ...data.formData,
      page: 1,
      order_status: data.formData.order_status == null ? -1 : data.formData.order_status,
      pay_begin_time: data.formData.pay_begin_time ? data.formData.pay_begin_time.join('_') : '',
      created_at: data.formData.pay_begin_time ? data.formData.pay_begin_time.join('_') : ''
    }
    if (!data.status) {
      data.formData.pay_begin_time = [dayjs().subtract(6, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      state.initParams.pay_begin_time = `${dayjs().subtract(6, 'day').format('YYYY-MM-DD')}_${dayjs().format(
        'YYYY-MM-DD'
      )}`
      state.initParams.created_at = `${dayjs().subtract(6, 'day').format('YYYY-MM-DD')}_${dayjs().format('YYYY-MM-DD')}`
    }
    getList()
  }

  // 订单列表
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true
      const resp = await getOrderList(state.initParams)
      state.tableConfigOptions.list = resp.data?.list || []
      state.tableConfigOptions.pagination.total = resp.data?.total_num || 0
      state.tableConfigOptions.pagination.current = resp.data?.page || 1

      state.tableConfigOptions.loading = false
    } catch (error) {
      console.error(error, '--')
      state.tableConfigOptions.list = []
      state.tableConfigOptions.loading = false
    }
  }

  // 导出
  const downloadPdd = async () => {
    let params = {
      ...state.initParams
    }
    for (let prop in params) {
      if (params[prop] === null) {
        delete params[prop]
      }
    }
    const result = await exportCreate({
      params: JSON.stringify(params),
      type: 'pdd_order_list'
    })
    if (result.code === 0) {
      goCenter('DownloadCenter', 'DownloadCenter')
    }
  }

  // 店铺列表
  const getShopList = async () => {
    try {
      const res = await fetchShopList({})
      searchData.value.find((item) => item.field == 'shop_name').props.options = (res.data?.list || []).map((v) => {
        return {
          value: v.pdd_shop_id,
          label: v.pdd_shop_name
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  getShopList()

  onMounted(() => {
    if (route.query?.order_num) {
      state.initParams.order_num = route.query?.order_num
      searchData.value.find((item) => item.field == 'order_num').value = route.query?.order_num
      getList()
    } else {
      getList()
    }
  })
  defineExpose({
    downloadPdd
  })
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn';

  .primary-color {
    color: v-bind('themeVar.primaryColor');
  }
  .grey-color {
    color: v-bind('themeVar.textColorGray');
  }
  .ad_url {
    overflow: hidden; //多出的隐藏
    text-overflow: ellipsis; //多出部分用...代替
    display: -webkit-box; //定义为盒子模型显示
    -webkit-line-clamp: 1; //用来限制在一个块元素显示的文本的行数
    -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
    font-size: 14px;
    width: 96px;
    cursor: pointer;
  }
  .round {
    width: 6px;
    height: 6px;
    margin-right: 4px;
    border-radius: 50%;
  }
  .item-error {
    color: #0080ff;
    border: 1px solid #0080ff;
  }
  .goods_info_data_remak {
    overflow: hidden; //多出的隐藏
    text-overflow: ellipsis; //多出部分用...代替
    display: -webkit-box; //定义为盒子模型显示
    -webkit-line-clamp: 2; //用来限制在一个块元素显示的文本的行数
    -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
    font-size: 14px;
  }
  .goods_info {
    .img_item {
      width: 60px;
      height: 60px;
      background: #bec6d6;
      border-radius: 6px;
    }

    p {
      margin: 0;
    }

    &_data {
      margin-left: 10px;
      font-family: PingFang SC;
      font-weight: 400;

      &_name {
        overflow: hidden; //多出的隐藏
        text-overflow: ellipsis; //多出部分用...代替
        display: -webkit-box; //定义为盒子模型显示
        -webkit-line-clamp: 2; //用来限制在一个块元素显示的文本的行数
        -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
        color: var(--el-color-primary);
        cursor: pointer;
      }
    }
  }
  .desc-table-layout-list-item-desc {
    background-color: rgba(36, 47, 87, 0.05);
    padding: 8px 16px;
  }
  .mini_program_logo {
    img {
      width: 16px;
      height: 16px;
      display: block;
    }
  }
</style>
