/**
 * 用户信息
 */
export type UserInfo =
  | {
      account: any
      name: any
      id: string
      realname: string
      avatar: string
      phone: string
      company_id: string
      user_type: number
      is_show_sku_help?: boolean
    }
  | undefined

export type ShopInfo =
  | {
      id: string
      company_name: string
      name: string
      logo: string
      company_id: string
      limit_callback: number
    }
  | undefined

export interface TacticsAction {
  term: boolean
  event: () => any
}
