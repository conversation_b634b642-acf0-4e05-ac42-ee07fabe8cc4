# 全屏SKU编辑功能实现说明

## 功能概述

在商品发布页面的SKU规格设置中，当启用多规格时，在"启用多规格"开关后面会显示一个"全屏编辑"按钮，点击后可以在全屏模式下编辑SKU规格。

## 实现的功能

### 1. 全屏按钮显示

- 当 `is_unit` 为 1 时，在 `specs_box` 后面显示"全屏编辑"按钮
- 按钮样式：`type="primary" size="small"`

### 2. 全屏弹窗

- 使用 `a-modal` 组件实现全屏弹窗
- 弹窗配置：
  - 宽度：100vw（全屏宽度）
  - 高度：100vh（全屏高度）
  - 无标题栏（title）
  - 无关闭按钮（closable: false）
  - 无底部按钮（footer: null）
  - 不可点击遮罩关闭（maskClosable: false）

### 3. 全屏弹窗内容

- 显示标题："商品规格设置"
- 包含两个主要组件：
  - `Sku` 组件：用于设置规格组
  - `SkuList` 组件：用于设置SKU价格列表

### 4. 底部关闭按钮

- 在弹窗底部中间位置显示"关闭全屏"按钮
- 点击后关闭全屏模式

### 5. 数据同步机制

- **打开全屏时**：将当前页面的SKU数据同步到全屏模式
- **关闭全屏时**：将全屏模式的数据同步回主页面
- **实时响应**：在全屏模式下的所有操作都会实时更新数据

### 6. 功能保持

- 在全屏模式下可以进行所有SKU相关操作：
  - 添加/删除规格组
  - 添加/删除规格值
  - 设置SKU价格
  - 上传SKU图片
  - 等所有原有功能

## 技术实现

### 状态管理

```javascript
// 全屏相关状态
fullscreenSkuVisible: false,        // 全屏弹窗显示状态
fullscreenSpecList: [],             // 全屏模式下的规格数组
fullscreenSkuList: [],              // 全屏模式下的价格数组
fullscreenSkuData: { list: [] },    // 全屏模式下的SKU数据
fullscreenTimestamp: Date.now()     // 全屏模式时间戳
```

### 核心方法

1. `openFullscreenSku()` - 打开全屏模式
2. `closeFullscreenSku()` - 关闭全屏模式
3. `handleFullscreenSkuChange()` - 处理全屏模式下的SKU变化
4. `handleFullscreenSkuChangeValue()` - 处理全屏模式下的SKU值变化
5. `handleFullscreenDelChange()` - 处理全屏模式下的删除操作
6. `handleFullscreenUpPicChange()` - 处理全屏模式下的图片上传

### 样式实现

- 使用CSS实现全屏布局
- 弹窗内容区域可滚动
- 底部按钮固定在底部中间

## 使用方法

1. 在商品发布页面，启用多规格开关
2. 点击"全屏编辑"按钮
3. 在全屏模式下进行SKU编辑操作
4. 完成后点击"关闭全屏"按钮
5. 数据会自动同步到主页面

## 注意事项

1. 全屏模式下的数据与主页面数据完全同步
2. 关闭全屏时会触发主页面的SKU变化事件
3. 全屏模式支持所有原有的SKU编辑功能
4. 数据同步使用深拷贝确保数据独立性

## 兼容性

- 不影响现有的SKU编辑功能
- 保持所有原有的交互逻辑
- 支持所有SKU组件的功能

## 问题修复记录

### 问题：规格组数据丢失

**现象**：添加完规格后，打开全屏，规格组没了，退出全屏，页面的规格组也没有了

**原因**：数据同步逻辑不完整，没有正确处理`state.specList`的更新

**解决方案**：

1. 修复了`handleFullscreenSkuChange`方法中的数据同步逻辑
2. 确保在全屏模式下正确设置和恢复所有相关状态
3. 添加了详细的日志输出用于调试

**修复后的逻辑**：

- 打开全屏时：完整同步`specList`、`skuList`、`skuData`
- 全屏操作时：临时切换到全屏数据，执行操作，保存结果，恢复主页面数据
- 关闭全屏时：将全屏数据同步回主页面，触发相关事件

## 测试步骤

1. 启用多规格开关
2. 添加规格组（如：颜色、尺码）
3. 添加规格值（如：红色、蓝色；S、M、L）
4. 点击"全屏编辑"按钮
5. 验证全屏模式下规格组和规格值是否正确显示
6. 在全屏模式下进行编辑操作
7. 点击"关闭全屏"按钮
8. 验证主页面的规格数据是否正确同步
